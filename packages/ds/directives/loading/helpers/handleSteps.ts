import { isString, random, reduce } from 'lodash';
import { check as checkFT } from '@/core/services/featureToggle';
import { DateTime } from 'luxon';
import sanitizeHTML from '@holistics/utils/sanitizeHTML';

import {
  LOADING_MESSAGES, LOADING_HINT_MESSAGE, COMPACT_LOADING_HINT_MESSAGE, ENDING_PROGRESS_PERCENTAGE, LOADING_CLASSES,
} from '../constants';
import pickMessage from './pickMessage';
import createLoadingContent from './createLoadingContent';
import removeLoadingContent from './removeLoadingContent';
import handlePresetText from './handlePresetText';

import type {
  MsgValue, LoadingStep, LoadingSteps, TextStep, SkeletonStep, ProgressStep, LoadingStepTypes, ProgressHintStep,
  BorderStep,
} from '../types';

const useElapsedTimeForProgress = checkFT('loading_progress:use_elapsed_time');

const setLoadingText = (el: HTMLElement, text: string) => {
  (el.querySelector('.v-loading-text') as HTMLElement).innerHTML = text;
};

const handleDuration = (el: HTMLElement, duration: number | undefined, callback: () => void) => {
  if (duration) {
    setTimeout(() => {
      // no need to do shit if loading container is hidden
      // this needs to stay inside settimeout
      if (!el || el.style.display === 'none') return;
      callback();
    }, duration);
  } else {
    if (!el || el.style.display === 'none') return;
    callback();
  }
};

const handleProgressHint = (el: HTMLElement, { compact, text }: ProgressHintStep) => {
  const hint = text || compact ? COMPACT_LOADING_HINT_MESSAGE : LOADING_HINT_MESSAGE;
  const loadingProgressHint = el.querySelector(`.${LOADING_CLASSES.hint}`);
  if (loadingProgressHint) {
    loadingProgressHint.innerHTML = hint;
  }
};

function handlePercentageProgress (el: HTMLElement, progressEl: HTMLElement, { hint } : { hint?: ProgressHintStep }) {
  let currentProgress = random(5, 15);
  let loadingProgressInterval: ReturnType<typeof setInterval>;

  function updateProgress () {
    // clear interval when loading container is hidden
    if (el.style.display === 'none') {
      clearInterval(loadingProgressInterval);
      return;
    }

    currentProgress += random(5, 10);
    if (currentProgress >= 100) {
      currentProgress = ENDING_PROGRESS_PERCENTAGE;
      clearInterval(loadingProgressInterval);

      if (hint) {
        handleDuration(el, 60000, () => handleProgressHint(el, hint));
      }
    }
    progressEl.innerHTML = `(${currentProgress}%)`;
  }

  progressEl.style.display = 'inline';
  progressEl.innerHTML = `(${currentProgress}%)`;
  loadingProgressInterval = setInterval(() => updateProgress(), random(2000, 3000));
}

function handleElaspedTimeProgress (el: HTMLElement, progressEl: HTMLElement, startTime: DateTime) {
  let loadingProgressInterval: ReturnType<typeof setInterval>;

  function getDisplayProgress () {
    const duration = startTime.until(DateTime.now());
    const durationObject = duration.toDuration(['days', 'hours', 'minutes', 'seconds']).toObject();
    return reduce(durationObject, (acc, value, key) => {
      const formattedValue = value && key === 'seconds' ? Math.trunc(value) : value;
      // eslint-disable-next-line no-param-reassign
      acc += formattedValue ? `${formattedValue}${key.charAt(0)}` : '';
      return acc;
    }, '');
  }

  function updateProgress () {
    // clear interval when loading container is hidden
    if (el.style.display === 'none') {
      clearInterval(loadingProgressInterval);
      return;
    }
    const displayProgress = getDisplayProgress();
    if (displayProgress && displayProgress !== '0s') {
      progressEl.innerHTML = sanitizeHTML(`(${displayProgress})`);
    } else {
      progressEl.innerHTML = '';
    }
  }

  progressEl.style.display = 'inline';
  updateProgress();
  loadingProgressInterval = setInterval(() => updateProgress(), 1000);
}

const handleProgress = (el: HTMLElement, startTime: DateTime, { hint } : { hint?: ProgressHintStep }) => {
  const progressEl = el.querySelector(`.${LOADING_CLASSES.progress}`) as HTMLElement;
  if (!progressEl) return;

  if (useElapsedTimeForProgress) {
    handleElaspedTimeProgress(el, progressEl, startTime);
  } else {
    handlePercentageProgress(el, progressEl, { hint });
  }
};

const STEP_HANDLERS: Record<LoadingStepTypes, any> = {
  text: (el: HTMLElement, body: boolean, currentStep: TextStep) => {
    const {
      type, text, preset, hoverText, onClick,
    } = currentStep;
    createLoadingContent(type, el, body, { hoverText, onClick });

    const message = handlePresetText(text, preset);
    setLoadingText(el, message);
  },
  skeleton: (el: HTMLElement, body: boolean, currentStep: SkeletonStep) => {
    createLoadingContent(currentStep.type, el, body);
  },
  border: (el: HTMLElement, body: boolean, currentStep: BorderStep) => {
    createLoadingContent(currentStep.type, el, body);
  },
  progress: (el: HTMLElement, body: boolean, currentStep: ProgressStep) => {
    // hack to make sure progress does not run multiple times
    if ((el.querySelector(`.${LOADING_CLASSES.progress}`) as HTMLElement)?.style?.display === 'inline') return;

    const {
      hint, text, type, hoverText, onClick, startTime,
    } = currentStep;

    createLoadingContent(type, el, body, { hoverText, onClick });

    const loadingText = text || pickMessage(LOADING_MESSAGES.progressing);
    setLoadingText(el, loadingText);

    handleProgress(el, startTime || DateTime.now(), { hint });
  },
};

const getLoadingId = (el: HTMLElement): number => {
  return (parseInt(el.dataset.v_loading_id || '0') || 0);
};

const handleStep = (el: HTMLElement, body: boolean, step: LoadingStep): Promise<void> => {
  // no need to do shit if loading container is hidden
  if (!el || el.style.display === 'none') return Promise.resolve();

  // handle step
  STEP_HANDLERS[step.type](el, body, step);

  // handle duration
  // no duration -> keep this step indefnitely
  if (!step.duration) return Promise.resolve();
  // with duration -> remove this step after timeout
  const loadingId = getLoadingId(el);
  return new Promise((resolve) => {
    setTimeout(() => {
      if (getLoadingId(el) !== loadingId) return; // abort if loading settings has been changed
      if (!el || el.style.display === 'none') return;

      removeLoadingContent(LOADING_CLASSES.content, el, body);

      resolve();
    }, step.duration);
  });
};

export default (steps: LoadingSteps | undefined, el: HTMLElement, value: MsgValue, { body, isUpdating }: { body: boolean, isUpdating?: boolean }): void => {
  if (!steps) return; // just to trick typescript, should never happen cause we have already assign default steps in getPresetSteps.ts

  const newLoadingId = getLoadingId(el) + 1;
  el.dataset.v_loading_id = newLoadingId.toString();

  // clean up old content if updating
  if (isUpdating) {
    removeLoadingContent(LOADING_CLASSES.content, el, body);
  }

  let prevStepPromise = Promise.resolve();
  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];

    // if step.type is 'text', step.text is not defined, and value is a string, assign value to step.text
    if (step.type === 'text' && !step.text && isString(value)) {
      step.text = value;
    }

    prevStepPromise = prevStepPromise.then(() => {
      if (getLoadingId(el) !== newLoadingId) return Promise.resolve(); // abort if loading settings has been changed

      return handleStep(el, body, step);
    });
    if (!step.duration) break;
  }
};
