/* eslint-disable max-classes-per-file */
import { Sugg<PERSON><PERSON><PERSON><PERSON><PERSON>, MonacoProvider, MonacoCursorContext } from '@aml-studio/h/ai';
import { File } from '@aml-studio/client/types/allTypes';
import { monaco } from '@holistics/aml-editor';
import {
  computed, ComputedRef, onMounted, ref, watch,
} from 'vue';
import { useStore } from 'vuex';

const CONSTANTS = {
  TAB_DELAY: 200,
  ESCAPE_DELAY: 1000,
} as const;

type HolisticsInlineCompletionItemProvider = monaco.languages.InlineCompletionsProvider & {
  reset(): void;
}

function extractContext (
  model: monaco.editor.ITextModel,
  position: monaco.Position,
): MonacoCursorContext {
  const word = model.getWordUntilPosition(position);
  const range = {
    startLineNumber: position.lineNumber,
    endLineNumber: position.lineNumber,
    startColumn: word.startColumn,
    endColumn: word.endColumn,
  };

  const textBeforeCursor = model.getValueInRange({
    startLineNumber: 1,
    startColumn: 1,
    endLineNumber: position.lineNumber,
    endColumn: position.column,
  });

  const textAfterCursor = model.getValueInRange({
    startLineNumber: position.lineNumber,
    startColumn: position.column,
    endLineNumber: model.getLineCount(),
    endColumn: model.getLineMaxColumn(model.getLineCount()),
  });

  const currentLineTextBeforeCursor = model.getValueInRange({
    startLineNumber: position.lineNumber,
    startColumn: 1,
    endLineNumber: position.lineNumber,
    endColumn: position.column,
  });

  return {
    range,
    textBeforeCursor,
    textAfterCursor,
    currentLineTextBeforeCursor,
    currentWord: word.word,
  };
}

export function useInlinesCompletionsProviders (
  suggestionFetcher: ComputedRef<SuggestionsFetcher | undefined>,
  file: ComputedRef<File>,
  editor: ComputedRef<monaco.editor.IStandaloneCodeEditor>,
) {
  const suggestionsProvider = computed(() => {
    if (suggestionFetcher.value === undefined) return undefined;
    return new MonacoProvider(suggestionFetcher.value);
  });

  // Monaco editor does not provide API to detect if user pressed tab and apply inline completion
  const shouldShowSuggestions = ref(true);
  watch(editor, (v) => {
    if (v) {
      v.onKeyDown((e) => {
        if (e.code === 'Tab') {
          shouldShowSuggestions.value = false;
          setTimeout(() => { shouldShowSuggestions.value = true; }, CONSTANTS.TAB_DELAY);
        }
        if (e.code === 'Escape') {
          shouldShowSuggestions.value = false;
          setTimeout(() => { shouldShowSuggestions.value = true; }, CONSTANTS.ESCAPE_DELAY);
        }
      });
    }
  });

  function triggerShowSuggestion () {
    if (editor.value) {
      editor.value.trigger('source', 'editor.action.inlineSuggest.trigger', null);
    }
  }

  function prefetchSuggestions (fileValue: File) {
    if (fileValue.content.trim().length === 0) {
      return;
    }
    const context = {
      textBeforeCursor: '',
      textAfterCursor: fileValue.content,
      currentLineTextBeforeCursor: fileValue.content,
      currentWord: '',
      editor: editor.value,
      range: {
        startLineNumber: 1,
        endLineNumber: 1,
        startColumn: 1,
        endColumn: 1,
      },
    };
    if (suggestionsProvider.value) {
      suggestionsProvider.value.fetchSuggestions({ context, delay: 0 });
    }
  }
  const store = useStore();
  const currentFilePath = computed(() => store.getters['aml/files/currentFilePath']);
  watch(currentFilePath, () => {
    if (suggestionsProvider.value) {
      suggestionsProvider.value.reset();
    }
    // prefetchSuggestions(file.value);
  });
  onMounted(() => {
    // prefetchSuggestions(file.value);
  });

  const inlineCompletionItemProvider = computed<HolisticsInlineCompletionItemProvider>(() => ({
    provideInlineCompletions: async (
      model: monaco.editor.ITextModel,
      position: monaco.Position,
      context: monaco.languages.InlineCompletionContext,
      _token: monaco.CancellationToken,
    ) => {
      if (!shouldShowSuggestions.value || !suggestionsProvider.value) {
        return { items: [] };
      }
      const suggestionContext = extractContext(model, position);
      suggestionContext.onAfterFetch = triggerShowSuggestion;

      const fetchInBackground = context.triggerKind === monaco.languages.InlineCompletionTriggerKind.Automatic;
      const suggestions = await suggestionsProvider.value.getSuggestions(suggestionContext, fetchInBackground);

      return {
        items: suggestions
          .slice(0, 1) // only show the first suggestion for ease of control
          .map((suggestion) => ({
            insertText: suggestion,
            range: suggestionContext.range,
            completeBracketPairs: false,
          })),
      };
    },
    freeInlineCompletions: () => {
      // empty
    },
    reset () {
      if (suggestionsProvider.value) {
        suggestionsProvider.value.reset();
      }
    },
  }));

  return {
    inlineCompletionItemProvider,
  };
}
