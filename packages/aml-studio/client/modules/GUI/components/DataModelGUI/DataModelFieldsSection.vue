<script setup lang="ts">
import { computed, ref } from 'vue';
import { HTooltip, HButton, HDropdown } from '@holistics/design-system';
import Model from '@aml-studio/client/models/Model';
import addModelFieldModal from '@aml-studio/client/modules/GUI/components/DataModelGUI/modals/addModelField.modal';
import SearchInput from '@aml-studio/client/modules/common/components/SearchInput.vue';
import useUpdateObject from '@aml-studio/client/modules/AMLEditor/containers/useUpdateObject';
import DataModelFields from './DataModelFields.vue';

const props = withDefaults(defineProps<{
  model: Model,
  canAddField: boolean,
  disabledAddFieldText?: string,
  isReadOnly?: boolean,
  highlightedField?: string
}>(), {
  isReadOnly: false,
  highlightedField: undefined,
  disabledAddFieldText: undefined,
});

const isShowDropdown = ref(false);

const { setField } = useUpdateObject();

const fieldsCount = computed(() => {
  const { dimensions, measures, params } = props.model;
  return dimensions.length + measures.length + params.length;
});

const searchText = ref('');
function updateSearchText (val: string) {
  searchText.value = val;
}

function showDropdown () {
  if (!props.canAddField) return;
  isShowDropdown.value = true;
}

async function addField (isMeasure = false) {
  isShowDropdown.value = false;
  const { status, data } = await addModelFieldModal(props.model, isMeasure);
  if (status === 'resolved') setField(data);
}
</script>

<template>
  <div class="flex items-center justify-between px-4 py-2">
    <div class="text-sm font-medium text-gray-900">
      Dimensions, Measures & Params ({{ fieldsCount }})
    </div>
    <div class="flex flex-row items-center justify-end">
      <search-input
        collapsible
        :debounced-time="200"
        class="font-normal text-gray-800"
        placeholder="Search field"
        :model-value="searchText"
        @update:model-value="updateSearchText"
        @click.prevent.stop=""
      />
      <HDropdown
        v-if="!isReadOnly"
        v-model:open="isShowDropdown"
        :options="[
          { key: 'custom-dimension', label: 'Custom dimension', class: 'ci-custom-dimension text-gray-800', action: () => addField(false) },
          { key: 'measure', label: 'Measure', class: 'text-gray-800', action: () => addField(true) },
        ]"
        placement="bottom-end"
      >
        <HTooltip
          :content="disabledAddFieldText"
          :disabled="canAddField"
        >
          <div @click.stop>
            <HButton
              :disabled="!canAddField"
              type="tertiary-highlight"
              icon="add"
              size="sm"
              class="ci-add-field"
              @click.prevent.stop="showDropdown"
            >
              <span>Add field</span>
            </HButton>
          </div>
        </HTooltip>
      </HDropdown>
    </div>
  </div>

  <DataModelFields
    :model="model"
    :search-text="searchText"
    :is-read-only="isReadOnly"
    :highlighted-field="highlightedField"
  />
</template>
