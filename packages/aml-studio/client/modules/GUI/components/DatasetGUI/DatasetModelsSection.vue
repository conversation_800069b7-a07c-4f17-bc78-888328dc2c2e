<script setup lang="ts">
import SearchInput from '@aml-studio/client/modules/common/components/SearchInput.vue';
import { Field } from '@holistics/aml-std';
import { Ref, computed, ref } from 'vue';
import { HButton } from '@holistics/design-system';
import DataSet from '@aml-studio/client/models/Dataset';
import {
  Action, Option, buildFieldOption, buildOptionsForModel,
} from './helper/options/options';
import ModelFieldsSection from './ModelFieldsSection.vue';
import { useGoToModel } from '../../compositions/useGoToModel';

const props = defineProps<{
  dataset: DataSet,
  isReadOnly: boolean;
}>();

const emit = defineEmits<{(e: 'updateModel'): void,
  (event: 'updateDatasetDim', field: Field): void,
  (event: 'deleteDatasetDim', field: Field): void,
}>();

const dmCount = computed(() => props.dataset.dataModels.length);

const searchText = ref('');
function updateSearchText (val: string) {
  searchText.value = val;
}

const { goToModel } = useGoToModel();

function addModel () {
  if (props.isReadOnly) return;

  emit('updateModel');
}

function editDatasetDim (field: Field) {
  emit('updateDatasetDim', field);
}

function deleteDatasetDim (field: Field) {
  emit('deleteDatasetDim', field);
}

const options: Ref<Option[]> = computed(() => {
  return props.dataset.dataModels.map(m => {
    const modelActions = [{
      key: 'gtdm',
      label: 'Go to Data Model',
      icons: ['external-link'],
      action: () => goToModel(m.name),
    }] as Action[];

    const buildFieldOptionFunction = (field: Field) => {
      if (props.isReadOnly) return buildFieldOption({ field, actions: [] });

      const actions: Action[] = field.defined_in_dataset
        ? [
          {
            key: 'edsd',
            label: 'Edit Field',
            icons: 'pencil',
            action: () => editDatasetDim(field),
          },
          {
            key: 'ddsd',
            label: 'Delete',
            icons: 'delete',
            class: 'text-red-600',
            action: () => deleteDatasetDim(field),
          },
        ]
        : [
          {
            key: 'emf',
            label: 'Open model to edit',
            icons: 'external-link',
            action: () => goToModel(m.name),
          },
        ];

      return buildFieldOption({
        field,
        actions,
      });
    };

    return buildOptionsForModel({
      model: m,
      modelActions,
      buildFieldOptionFunction,
    });
  });
});
</script>

<template>
  <div>
    <div class="flex items-center justify-between px-4 py-2">
      <div class="text-sm font-medium text-gray-900">
        Data Models ({{ dmCount }})
      </div>
      <div class="flex">
        <searchInput
          collapsible
          :debounced-time="200"
          class="font-normal text-gray-800"
          placeholder="Search field"
          :model-value="searchText"
          @update:model-value="updateSearchText"
          @click.prevent.stop=""
        />
        <HButton
          v-if="!isReadOnly"
          type="tertiary-highlight"
          icon="add"
          size="sm"
          class="ci-aml-edit-models"
          @click.stop="emit('updateModel')"
        >
          <span>Add & Edit Models</span>
        </HButton>
      </div>
    </div>
    <ModelFieldsSection
      :options="options"
      :nested-level="1"
      :is-read-only="isReadOnly"
      :search-text="searchText"
    >
      <template #empty-section>
        <div
          class="mx-4 border-t pt-3 text-gray-700"
        >
          This Dataset is empty.
          <a
            v-if="!isReadOnly"
            class="cursor-pointer"
            @click.stop="addModel"
          >Add Data Model</a>
        </div>
      </template>
    </ModelFieldsSection>
  </div>
</template>
