<script setup lang="ts">
import {
  Ref, computed, ref,
} from 'vue';
import type { Field } from '@aml-studio/aml/types';
import SearchInput from '@aml-studio/client/modules/common/components/SearchInput.vue';
import DataSet from '@aml-studio/client/models/Dataset';
import { HButton } from '@holistics/design-system';
import { Action, buildFieldOption, Option } from './helper/options/options';
import ModelFieldsSection from './ModelFieldsSection.vue';

const props = defineProps<{
  dataset: DataSet,
  isReadOnly?: boolean,
}>();

const searchText = ref<string>('');
const metricsCount = computed(() => props.dataset.metrics.length);
const createMetricsInDatasetUrl = 'https://docs.holistics.io/docs/metrics-in-datasets#using-metrics';
const emit = defineEmits<{(e: 'updateMetric', field: Field): void,
  (e: 'deleteMetric', field: Field): void,
  (e: 'updateMetric', field: Field): void,
  (e: 'addMetric'): void,
}>();

function updateSearchText (val: string): void {
  searchText.value = val;
}

function updateMetric (field: Field): void {
  emit('updateMetric', field);
}

function deleteMetric (field: Field) {
  emit('deleteMetric', field);
}

const options: Ref<Option[]> = computed(() => {
  return props.dataset.metrics.map(field => {
    const actions = props.isReadOnly ? [] : [
      {
        key: 'em',
        label: 'Edit Metric',
        icons: 'pencil',
        action: () => updateMetric(field),
      },
      {
        key: 'dm',
        label: 'Delete',
        icons: 'delete',
        class: 'text-red-600',
        action: () => deleteMetric(field),
      },
    ] as Action[];

    return buildFieldOption({
      field,
      actions,
    });
  });
});

</script>

<template>
  <div class="metric-section">
    <div class="metric-section-header flex items-center justify-between px-4 py-2">
      <div class="text-sm font-medium text-gray-900">
        Metrics ({{ metricsCount }})
      </div>
      <div class="flex">
        <SearchInput
          collapsible
          :debounced-time="200"
          class="font-normal text-gray-800"
          placeholder="Search field"
          :model-value="searchText"
          @update:model-value="updateSearchText"
          @click.prevent.stop=""
        />
        <HButton
          v-if="!isReadOnly"
          icon="add"
          type="tertiary-highlight"
          size="sm"
          class="text-gray-700"
          @click="emit('addMetric')"
        >
          Add Metric
        </HButton>
      </div>
    </div>
    <ModelFieldsSection
      :options="options"
      :nested-level="0"
      :is-read-only="isReadOnly"
      :search-text="searchText"
    >
      <template #empty-section>
        <div class="mx-4 text-gray-700">
          No metrics found in this Dataset.
          <a
            :href="createMetricsInDatasetUrl"
            target="_blank"
          >Learn more</a>
        </div>
      </template>
    </ModelFieldsSection>
  </div>
</template>
