<script setup lang="ts">
import { cloneDeep, isEqual } from 'lodash';
import {
  computed, nextTick, ref, watch,
} from 'vue';
import {
  HSwitch, HButton, HDropdown, HSelect,
} from '@holistics/design-system';
import { generateUuid } from '@holistics/utils';
import { FloatingDeletePanel, RelationshipErrorMessage } from '@aml-studio/h/components';
import HighlightedText from '@holistics/ds/components/HighlightedText/HighlightedText.vue';
import Dataset from '@aml-studio/client/models/Dataset';
import DimensionSelect from '@aml-studio/client/modules/common/components/DimensionSelect.vue';
import { Join } from '@aml-studio/h/models';
import { standardizeDimensionFromModels } from '@aml-studio/client/utils/buildDimensionTree';
import { RelationshipInfo } from '@aml-studio/aml/types';
import { checkFeatureToggle } from '@aml-studio/h/services';
import { MAX_RELATIONSHIP_INPUT_ROW_COUNT } from '@aml-studio/client/store/modules/uiState/constants';
import relationshipFormModal from './modals/relationshipForm.modal';
import {
  convertJoinConfigToRelationshipRow,
  convertRelationshipRowToRelationshipInfo,
  createToggleRelationshipErrorMessage,
  RelationshipRow,
  sortRelationships,
} from './helper/DatasetRelationshipHelper';
import { useDatasetRelationship } from '../../compositions/useDatasetRelationship';

const props = withDefaults(defineProps<{
  previewDataset: Dataset,
  isReadOnly?: boolean,
  searchText?: string,
}>(), {
  isReadOnly: false,
  searchText: '',
});

const emit = defineEmits<{(e: 'createRelationship', relationshipInfo : RelationshipInfo): void,
  (e: 'updateRelationship', currentRelationship: RelationshipInfo, newRelationship: RelationshipInfo): void,
  (e: 'removeRelationship', relationshipInfo: RelationshipInfo): void,
}>();

const linkTypes = Join.TYPES;

const allowAmbiguousPaths = checkFeatureToggle('data_models:allow_ambiguous_paths');

const {
  ambiguousPaths, hasAmbiguousPaths, hasRelationship, checkAmbiguousPaths,
  relationshipErrorMessage, toggleRelationshipErrorMessage,
} = useDatasetRelationship();

const relationshipTableOptions = computed(() => ({
  sortable: ['type', 'from', 'to'],
  headings: {
    type: 'Type',
    from: 'From',
    to: 'To',
    active: 'Active',
    actions: '',
  },
  showFilter: false,
  rowClassCallback: (row: any) => {
    if (row.isTemp) return 'temp-row';
    return '';
  },
  customSorting: {
    type: sortRelationships('type'),
    from: sortRelationships('from'),
    to: sortRelationships('to'),
  },
  scrollToRowIdOptions: {
    behavior: 'smooth',
    inline: 'start',
    block: 'nearest',
  },
}));

const tableColumns = computed(() => {
  const readOnlyTableColumns = ['type', 'from', 'to', 'active'];

  if (props.isReadOnly) return readOnlyTableColumns;
  return [...readOnlyTableColumns, 'actions'];
});

const dimensions = computed(() => {
  return standardizeDimensionFromModels(props.previewDataset.dataModels);
});

const tempRelationshipData = ref<RelationshipRow[]>([]);
const currentRelationshipData = computed(() => {
  return props.previewDataset.joinConfigs.map((config) => convertJoinConfigToRelationshipRow(config, ambiguousPaths.value, dimensions.value));
});
const allRelationshipData = computed(() => [...currentRelationshipData.value, ...tempRelationshipData.value]);

const relationshipTableRef = ref();
function scrollToRow (rowId: string) {
  relationshipTableRef.value?.scrollToRow(rowId);
}

// update tempRelationshipData if there's changed from dimensions
// currentRelationshipData is already reactive with dimensions (computed)
watch(dimensions, () => {
  tempRelationshipData.value.forEach(row => {
    if (row.from) {
      row.from = dimensions.value.find(({ filePath }) => filePath === row.from?.filePath);
    }
    if (row.to) {
      row.to = dimensions.value.find(({ filePath }) => filePath === row.to?.filePath);
    }
  });
});

const addRelationshipDisabled = computed(() => tempRelationshipData.value.length >= MAX_RELATIONSHIP_INPUT_ROW_COUNT);
function addRelationshipRow () {
  const tempRowId = generateUuid();
  tempRelationshipData.value.push({
    id: tempRowId,
    linkType: 'many_to_one',
    isTemp: true,
    canCreateRelationship: false,
    rawConfig: {
      active: true,
    },
    isAmbiguousPath: false,
  } as RelationshipRow);

  nextTick(() => {
    scrollToRow(tempRowId);
  });
}
function deleteRelationshipRow (row: RelationshipRow) {
  tempRelationshipData.value = tempRelationshipData.value.filter(tempRow => row.id !== tempRow.id);
}
const disableCreateRelationshipRow = (row: RelationshipRow) => !(row.from && row.to && row.linkType);

function canToggleRelationship (joinId: string) {
  const { dataModels, relatedJoins, joinConfigs } = props.previewDataset;

  const newJoinConfig = cloneDeep(joinConfigs.find(joinConfig => joinId === joinConfig.joinId));
  if (!newJoinConfig) return false; // must available
  newJoinConfig.active = true;
  const newJoinConfigs = joinConfigs.map(joinConfig => {
    return joinConfig.joinId === newJoinConfig.joinId ? newJoinConfig : joinConfig;
  });

  checkAmbiguousPaths(dataModels, newJoinConfigs, relatedJoins);

  if (!allowAmbiguousPaths) {
    // ^ although the relationship can't be toggle on due to ambiguous paths error,
    //   still mark and display it as ambiguous path on UI
    //   https://github.com/holistics/holistics/pull/8479#discussion_r1031984471
    const { sourceField, destField } = newJoinConfig;
    const path = [sourceField.model.name, destField.model.name];
    const isAmbiguousPath = ambiguousPaths.value.some(ambiguousPath => isEqual(ambiguousPath, path) || isEqual(ambiguousPath, path.reverse()));
    return !isAmbiguousPath;
  }

  return true;
}

function toggleRelationshipActive (isActive: boolean, row: RelationshipRow) {
  const { from, to } = row;
  if (!from || !to) return;
  if (isActive && !canToggleRelationship(row.rawConfig.joinId)) {
    toggleRelationshipErrorMessage.value = createToggleRelationshipErrorMessage(from.fullPath, to.fullPath);
    return;
  }

  const relationshipInfo = convertRelationshipRowToRelationshipInfo(row);
  emit(
    'updateRelationship',
    relationshipInfo,
    {
      ...relationshipInfo,
      isActive,
    },
  );
}

function createRelationship (row: RelationshipRow) {
  emit('createRelationship', convertRelationshipRowToRelationshipInfo(row));
  deleteRelationshipRow(row);
}

async function updateRelationship (row: RelationshipRow) {
  const relationshipInfo = convertRelationshipRowToRelationshipInfo(row);
  const result = await relationshipFormModal({
    models: props.previewDataset.dataModels,
    selectedRelationship: relationshipInfo,
  });

  const { status, data } = result;
  if (status === 'resolved') {
    emit('updateRelationship', relationshipInfo, data);
  }
}

async function removeRelationship (row: RelationshipRow) {
  emit('removeRelationship', convertRelationshipRowToRelationshipInfo(row));
}

function dismissErrorMessage () {
  toggleRelationshipErrorMessage.value = undefined;
  const { dataModels, joinConfigs, relatedJoins } = props.previewDataset;
  checkAmbiguousPaths(dataModels, joinConfigs, relatedJoins);
}

async function handleFieldUpdate (row: RelationshipRow) {
  if (row.from && row.to && !row.processed && !row.isLoading) {
    row.isLoading = true; // Set loading state
    row.processed = true; // Prevent multiple triggers

    if (!disableCreateRelationshipRow(row)) {
      await createRelationship(row);
      row.isLoading = false;
    } else {
      row.isLoading = false;
    }
  }
}
</script>

<template>
  <Portal
    v-if="!isReadOnly"
    to="addRelationshipBtnTarget"
  >
    <HButton
      class="dataset-add-relationship-btn"
      type="tertiary-highlight"
      icon="add"
      size="sm"
      :disabled="addRelationshipDisabled"
      @click.stop="addRelationshipRow"
    >
      <span>Add Relationship</span>
    </HButton>
  </Portal>
  <div
    v-if="hasRelationship || tempRelationshipData.length > 0"
    class="dataset-relationships-wrapper"
  >
    <h-table
      ref="relationshipTableRef"
      class="mb-2"
      :data="allRelationshipData"
      :columns="tableColumns"
      :options="relationshipTableOptions"
      :search-text="searchText"
    >
      <template #h__active>
        <div class="flex justify-center">
          <!-- Active -->
        </div>
      </template>
      <template #h__from>
        <span class="pl-4">
          From
        </span>
      </template>
      <template #h__to>
        <span class="pl-4">
          To
        </span>
      </template>
      <template #type="{ row }: { row: RelationshipRow }">
        <HSelect
          v-if="row.isTemp"
          v-model="row.linkType"
          label-key="friendlyLabel"
          :options="linkTypes"
          :searchable="false"
          :clearable="false"
          placement="bottom-start"
          floating-class="relationship-input-row-select"
        >
          <template #selected-option="{ option }">
            {{ option.label }}
          </template>
          <template #panel-footer>
            <div class="h-select__row border-t">
              <span>Set up
                <a
                  href="https://docs.holistics.io/docs/relationships#handling-many-to-many-n-n-relationship"
                  target="_blank"
                > many-to-many </a>
                relationships?</span>
            </div>
          </template>
        </HSelect>
        <span
          v-else
          class="link-type"
        >
          {{ row.type }}
        </span>
      </template>
      <template #active="{ row }: { row: RelationshipRow }">
        <div
          v-if="!row.isTemp"
          class="flex w-[50px] justify-center"
        >
          <HSwitch
            :model-value="row.rawConfig.active"
            :disabled="isReadOnly"
            class="mr-0"
            data-ci="switch-relationship"
            size="lg"
            @update:model-value="(value: boolean) => toggleRelationshipActive(value, row)"
          />
        </div>
      </template>
      <template #from="{ row }: { row: RelationshipRow }">
        <dimension-select
          v-if="row.isTemp"
          v-model="row.from"
          class="onboarding-relationship-input-row-from"
          :dimensions="dimensions"
          :clearable="false"
          :has-error="!row.from"
          :disabled="row.isLoading"
          @update:model-value="handleFieldUpdate(row)"
        />
        <HighlightedText
          v-else
          class="pl-4"
          :text="row.from.fullPath"
          :highlight-text="searchText"
        />
        <h-icon
          v-if="row.isAmbiguousPath"
          name="exclamation"
          class="pl-1"
          :class="allowAmbiguousPaths ? 'text-orange-500' : 'text-red-500'"
        />
      </template>
      <template #to="{ row }: { row: RelationshipRow }">
        <div class="flex items-center gap-2">
          <dimension-select
            v-if="row.isTemp"
            v-model="row.to"
            class="onboarding-relationship-input-row-to flex-grow"
            :dimensions="dimensions"
            :clearable="false"
            :has-error="!row.to"
            :disabled="row.isLoading"
            @update:model-value="handleFieldUpdate(row)"
          />
          <highlighted-text
            v-else
            class="flex-grow pl-4"
            :text="row.to.fullPath"
            :highlight-text="searchText"
          />
          <HButton
            v-if="row.isTemp"
            class="onboarding-btn-remove-relationship-input-row"
            unified
            icon="cancel"
            size="sm"
            type="tertiary-highlight"
            @click="deleteRelationshipRow(row)"
          />
        </div>
      </template>
      <template #actions="{ row }: { row: RelationshipRow }">
        <HDropdown
          v-if="!isReadOnly"
          :options="[
            { key: 'edit', label: 'Edit', action: () => updateRelationship(row) },
            { key: 'delete', slot: 'delete' },
          ]"
          placement="bottom-end"
          class="!float-right"
        >
          <HButton
            type="tertiary-default"
            unified
            icon="ellipsis-horizontal"
            size="sm"
            class="dropdown-toggle"
          />

          <template #delete>
            <FloatingDeletePanel
              @delete-panel="removeRelationship(row)"
            >
              <div class="flex cursor-pointer items-start space-x-1 rounded p-2 text-red-500 hover:bg-gray-100 active:bg-gray-400">
                Delete
              </div>

              <template #content>
                Do you really want to delete this relationship?
              </template>
            </FloatingDeletePanel>
          </template>
        </HDropdown>
      </template>
    </h-table>
    <RelationshipErrorMessage
      v-if="hasAmbiguousPaths"
      :message="relationshipErrorMessage ?? ''"
      :dismissible="!!toggleRelationshipErrorMessage"
      @dismiss="dismissErrorMessage"
    />
  </div>
  <div
    v-else
    class="mt-1 text-gray-700"
  >
    No Relationships between Models. Relationship is the "join" between Data Models in a Dataset.
    <a
      target="_blank"
      href="https://docs.holistics.io/docs/relationships#create-40-relationship"
    >
      Learn more
    </a>
  </div>
</template>

<style lang="postcss">
.dataset-relationships-wrapper {
  .h-table {
    table-layout: fixed;

    th:first-child {
      width: 72px;
    }

    th:nth-child(4) {
      width: 72px;
    }

    th:last-child {
      width: 38px;
    }

    td:first-child,
    td:last-child {
      padding-left: 0px !important;

      .link-type {
        padding-left: 8px; /* $spacer * 2 */
      }
    }

    td {
      vertical-align: middle;
      padding-top: 0px !important;
      padding-bottom: 0px !important;
      height: 40px;
      @apply border-b border-gray-300; /* border/default */
    }

    .temp-row td {
      border: none !important;
      padding-top: 8px !important; /* $spacer * 2 */
      padding-bottom: 8px !important; /* $spacer * 2 */
    }
  }
}

.relationship-input-row-select {
  .hui-popper-content {
    width: max-content !important;
  }

  .hui-popper-content {
    padding: 0;
  }

  .h-select__options-item {
    padding: 8px 4px; /* $spacer * 2 $spacer */
  }

  .h-select__row {
    padding: 8px 8px; /* $spacer * 2 $spacer * 2 */
  }
}
</style>
