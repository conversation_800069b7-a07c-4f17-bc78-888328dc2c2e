# typed: true

# DO NOT EDIT MANUALLY
# This is an autogenerated file for types exported from the `rmagick` gem.
# Please instead update this file by running `bin/tapioca gem rmagick`.


# source://rmagick//lib/rmagick_internal.rb#24
module Magick
  private

  def colors; end
  def fonts; end
  def init_formats; end
  def limit_resource(*_arg0); end
  def set_cache_threshold(_arg0); end
  def set_log_event_mask(*_arg0); end
  def set_log_format(_arg0); end

  class << self
    def colors; end
    def fonts; end

    # Describes the image formats supported by ImageMagick.
    # If the optional block is present, calls the block once for each image format.
    # The first argument, +k+, is the format name. The second argument, +v+, is the
    # properties string described below.
    #
    # - +B+ is "*" if the format has native blob support, or " " otherwise.
    # - +R+ is "r" if ImageMagick can read that format, or "-" otherwise.
    # - +W+ is "w" if ImageMagick can write that format, or "-" otherwise.
    # - +A+ is "+" if the format supports multi-image files, or "-" otherwise.
    #
    # @example
    #   p Magick.formats
    #   => {"3FR"=>" r-+", "3G2"=>" r-+", "3GP"=>" r-+", "A"=>"*rw+",
    #   ...
    # @overload formats
    # @overload formats
    #
    # source://rmagick//lib/rmagick_internal.rb#51
    def formats(&block); end

    def init_formats; end
    def limit_resource(*_arg0); end
    def set_cache_threshold(_arg0); end
    def set_log_event_mask(*_arg0); end
    def set_log_format(_arg0); end
  end
end

Magick::AbsQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::AbsoluteErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::AbsoluteIntent = T.let(T.unsafe(nil), Magick::RenderingIntent)
Magick::ActivateAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::AddModulusQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::AddNoisePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::AddQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::AffineDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)

class Magick::AffineMatrix < ::Struct
  def rx; end
  def rx=(_); end
  def ry; end
  def ry=(_); end
  def sx; end
  def sx=(_); end
  def sy; end
  def sy=(_); end
  def tx; end
  def tx=(_); end
  def ty; end
  def ty=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::AffineProjectionDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)

class Magick::AlignType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::AllChannels = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::AllCompliance = T.let(T.unsafe(nil), Magick::ComplianceType)
Magick::AllValues = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::AlphaChannel = T.let(T.unsafe(nil), Magick::ChannelType)

class Magick::AlphaChannelOption < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

class Magick::AnchorType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::AndQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::AnyStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::AnyStyle = T.let(T.unsafe(nil), Magick::StyleType)
Magick::AnyWeight = T.let(T.unsafe(nil), Magick::WeightType)
Magick::ArcDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::ArcsinFunction = T.let(T.unsafe(nil), Magick::MagickFunction)
Magick::ArctanFunction = T.let(T.unsafe(nil), Magick::MagickFunction)

# source://rmagick//lib/rmagick_internal.rb#72
Magick::AreaGeometry = T.let(T.unsafe(nil), Magick::GeometryValue)

Magick::AreaValue = T.let(T.unsafe(nil), Magick::GeometryFlags)

# source://rmagick//lib/rmagick_internal.rb#69
Magick::AspectGeometry = T.let(T.unsafe(nil), Magick::GeometryValue)

Magick::AspectValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::AssociateAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::AtopCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::Average16InterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::Average9InterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::AverageInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::B44ACompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::B44Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::BZipCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::BackgroundAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::BackgroundDispose = T.let(T.unsafe(nil), Magick::DisposeType)
Magick::BackgroundInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::BackgroundVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::BarrelDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::BarrelInverseDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::BartlettFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::BarycentricColorInterpolate = T.let(T.unsafe(nil), Magick::SparseColorMethod)
Magick::BesselFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::BilevelType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::BilinearColorInterpolate = T.let(T.unsafe(nil), Magick::SparseColorMethod)
Magick::BilinearDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::BilinearForwardDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::BilinearInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::BilinearReverseDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::BinomialKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::BlackChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::BlackVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::BlackmanFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::BlendCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::BlendInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::BlueChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::BlurCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::BlurKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::BlurPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::BohmanFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::BoldWeight = T.let(T.unsafe(nil), Magick::WeightType)
Magick::BolderWeight = T.let(T.unsafe(nil), Magick::WeightType)
Magick::BottomHatMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::BottomLeftOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::BottomRightOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::BoxFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::BrightnessPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::BumpmapCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CMYColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::CMYKColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::CatromFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::CatromInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::CenterAlign = T.let(T.unsafe(nil), Magick::AlignType)
Magick::CenterGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::ChangeMaskCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

class Magick::ChannelType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::CharPixel = T.let(T.unsafe(nil), Magick::StorageType)
Magick::CharcoalDrawingPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ChebyshevKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::CheckerTileVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::ChiNegative = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::ChiValue = T.let(T.unsafe(nil), Magick::GeometryFlags)

class Magick::Chromaticity < ::Struct
  def blue_primary; end
  def blue_primary=(_); end
  def green_primary; end
  def green_primary=(_); end
  def red_primary; end
  def red_primary=(_); end
  def to_s; end
  def white_point; end
  def white_point=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

class Magick::ClassType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::ClearCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CloseIntensityMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::CloseMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::CoalesceLayer = T.let(T.unsafe(nil), Magick::LayerMethod)

class Magick::Color < ::Struct
  def color; end
  def color=(_); end
  def compliance; end
  def compliance=(_); end
  def name; end
  def name=(_); end
  def to_s; end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::ColorBurnCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ColorDodgeCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ColorSeparationAlphaType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::ColorSeparationType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::ColorizeCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

class Magick::ColorspaceType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::CometKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::CompareAnyLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::CompareClearLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::CompareOverlayLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::CompassKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)

class Magick::ComplianceType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::CompositeChannels = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::CompositeLayer = T.let(T.unsafe(nil), Magick::LayerMethod)

class Magick::CompositeOperator < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

class Magick::CompressionType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::CondensedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::ConvexHullKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::ConvolveMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::CopyAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::CopyAlphaCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyBlackCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyBlueCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyCyanCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyGreenCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyMagentaCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyRedCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CopyYellowCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::CornersKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::CorrelateMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::CorrelateNormalizeValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::CosineFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::CosineQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::CrossKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::CubicFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::CyanChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::Cylinder2PlaneDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::DXT1Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::DXT3Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::DXT5Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::DarkenCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DarkenIntensityCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DePolarDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::DeactivateAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::DecimalValue = T.let(T.unsafe(nil), Magick::GeometryFlags)

class Magick::DecorationType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::DefaultChannels = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::DespecklePreview = T.let(T.unsafe(nil), Magick::PreviewType)
class Magick::DestroyedImageError < ::StandardError; end
Magick::DiagonalsKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::DiamondKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::DifferenceCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DilateIntensityMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::DilateMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::DirectClass = T.let(T.unsafe(nil), Magick::ClassType)
Magick::DisassociateAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::DiskKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::DisplaceCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DisposeLayer = T.let(T.unsafe(nil), Magick::LayerMethod)

class Magick::DisposeType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::DissolveCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DistanceMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::DistortCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

class Magick::DistortMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

class Magick::DitherMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::DitherVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::DivideDstCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DivideQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::DivideSrcCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DoGKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::DoublePixel = T.let(T.unsafe(nil), Magick::StorageType)

# source://rmagick//lib/rmagick_internal.rb#160
class Magick::Draw
  include ::Magick::DrawAttribute

  def initialize; end

  # Apply coordinate transformations to support scaling (s), rotation (r),
  # and translation (t). Angles are specified in radians.
  #
  # source://rmagick//lib/rmagick_internal.rb#255
  def affine(sx, rx, ry, sy, tx, ty); end

  # Set alpha (make transparent) in image according to the specified
  # colorization rule
  #
  # source://rmagick//lib/rmagick_internal.rb#261
  def alpha(x, y, method); end

  def annotate(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5); end

  # Draw an arc.
  #
  # source://rmagick//lib/rmagick_internal.rb#268
  def arc(start_x, start_y, end_x, end_y, start_degrees, end_degrees); end

  # Draw a bezier curve.
  #
  # source://rmagick//lib/rmagick_internal.rb#276
  def bezier(*points); end

  # Draw a circle
  #
  # source://rmagick//lib/rmagick_internal.rb#286
  def circle(origin_x, origin_y, perim_x, perim_y); end

  # Invoke a clip-path defined by def_clip_path.
  #
  # source://rmagick//lib/rmagick_internal.rb#291
  def clip_path(name); end

  # Define the clipping rule.
  #
  # source://rmagick//lib/rmagick_internal.rb#296
  def clip_rule(rule); end

  # Define the clip units
  #
  # source://rmagick//lib/rmagick_internal.rb#303
  def clip_units(unit); end

  def clone; end

  # Set color in image according to specified colorization rule. Rule is one of
  # point, replace, floodfill, filltoborder,reset
  #
  # source://rmagick//lib/rmagick_internal.rb#311
  def color(x, y, method); end

  def composite(*_arg0); end

  # Specify EITHER the text decoration (none, underline, overline,
  # line-through) OR the text solid background color (any color name or spec)
  #
  # source://rmagick//lib/rmagick_internal.rb#318
  def decorate(decoration); end

  # Define a clip-path. A clip-path is a sequence of primitives
  # bracketed by the "push clip-path <name>" and "pop clip-path"
  # primitives. Upon advice from the IM guys, we also bracket
  # the clip-path primitives with "push(pop) defs" and "push
  # (pop) graphic-context".
  #
  # source://rmagick//lib/rmagick_internal.rb#331
  def define_clip_path(name); end

  def draw(_arg0); end
  def dup; end

  # Draw an ellipse
  #
  # source://rmagick//lib/rmagick_internal.rb#343
  def ellipse(origin_x, origin_y, width, height, arc_start, arc_end); end

  # Let anything through, but the only defined argument
  # is "UTF-8". All others are apparently ignored.
  #
  # source://rmagick//lib/rmagick_internal.rb#352
  def encoding(encoding); end

  # Specify object fill, a color name or pattern name
  #
  # source://rmagick//lib/rmagick_internal.rb#357
  def fill(colorspec); end

  # Specify object fill, a color name or pattern name
  #
  # source://rmagick//lib/rmagick_internal.rb#357
  def fill_color(colorspec); end

  # Specify fill opacity (use "xx%" to indicate percentage)
  #
  # source://rmagick//lib/rmagick_internal.rb#364
  def fill_opacity(opacity); end

  # Specify object fill, a color name or pattern name
  #
  # source://rmagick//lib/rmagick_internal.rb#357
  def fill_pattern(colorspec); end

  # source://rmagick//lib/rmagick_internal.rb#369
  def fill_rule(rule); end

  # Specify text drawing font
  #
  # source://rmagick//lib/rmagick_internal.rb#376
  def font(name); end

  # source://rmagick//lib/rmagick_internal.rb#380
  def font_family(name); end

  # Specify the font size in points. Yes, the primitive is "font-size" but
  # in other places this value is called the "pointsize". Give it both names.
  #
  # source://rmagick//lib/rmagick_internal.rb#472
  def font_size(points); end

  # source://rmagick//lib/rmagick_internal.rb#384
  def font_stretch(stretch); end

  # source://rmagick//lib/rmagick_internal.rb#389
  def font_style(style); end

  # The font weight argument can be either a font weight
  # constant or [100,200,...,900]
  #
  # source://rmagick//lib/rmagick_internal.rb#396
  def font_weight(weight); end

  def get_multiline_type_metrics(*_arg0); end
  def get_type_metrics(*_arg0); end

  # Specify the text positioning gravity, one of:
  # NorthWest, North, NorthEast, West, Center, East, SouthWest, South, SouthEast
  #
  # source://rmagick//lib/rmagick_internal.rb#406
  def gravity(grav); end

  # source://rmagick//lib/rmagick_internal.rb#411
  def image(composite, x, y, width, height, image_file_path); end

  def inspect; end

  # IM 6.5.5-8 and later
  #
  # source://rmagick//lib/rmagick_internal.rb#418
  def interline_spacing(space); end

  # IM 6.4.8-3 and later
  #
  # source://rmagick//lib/rmagick_internal.rb#423
  def interword_spacing(space); end

  # IM 6.4.8-3 and later
  #
  # source://rmagick//lib/rmagick_internal.rb#428
  def kerning(space); end

  # Draw a line
  #
  # source://rmagick//lib/rmagick_internal.rb#433
  def line(start_x, start_y, end_x, end_y); end

  def marshal_dump; end
  def marshal_load(_arg0); end

  # Specify drawing fill and stroke opacities. If the value is a string
  # ending with a %, the number will be multiplied by 0.01.
  #
  # source://rmagick//lib/rmagick_internal.rb#439
  def opacity(opacity); end

  # Draw using SVG-compatible path drawing commands. Note that the
  # primitive requires that the commands be surrounded by quotes or
  # apostrophes. Here we simply use apostrophes.
  #
  # source://rmagick//lib/rmagick_internal.rb#447
  def path(cmds); end

  # Define a pattern. In the block, call primitive methods to
  # draw the pattern. Reference the pattern by using its name
  # as the argument to the 'fill' or 'stroke' methods
  #
  # source://rmagick//lib/rmagick_internal.rb#454
  def pattern(name, x, y, width, height); end

  # Set point to fill color.
  #
  # source://rmagick//lib/rmagick_internal.rb#466
  def point(x, y); end

  # Specify the font size in points. Yes, the primitive is "font-size" but
  # in other places this value is called the "pointsize". Give it both names.
  #
  # source://rmagick//lib/rmagick_internal.rb#472
  def pointsize(points); end

  # Draw a polygon
  #
  # source://rmagick//lib/rmagick_internal.rb#478
  def polygon(*points); end

  # Draw a polyline
  #
  # source://rmagick//lib/rmagick_internal.rb#488
  def polyline(*points); end

  # Return to the previously-saved set of whatever
  # pop('graphic-context') (the default if no arguments)
  # pop('defs')
  # pop('gradient')
  # pop('pattern')
  #
  # source://rmagick//lib/rmagick_internal.rb#503
  def pop(*what); end

  def primitive(_arg0); end

  # Push the current set of drawing options. Also you can use
  # push('graphic-context') (the default if no arguments)
  # push('defs')
  # push('gradient')
  # push('pattern')
  #
  # source://rmagick//lib/rmagick_internal.rb#516
  def push(*what); end

  # Draw a rectangle
  #
  # source://rmagick//lib/rmagick_internal.rb#525
  def rectangle(upper_left_x, upper_left_y, lower_right_x, lower_right_y); end

  # Specify coordinate space rotation. "angle" is measured in degrees
  #
  # source://rmagick//lib/rmagick_internal.rb#533
  def rotate(angle); end

  # Draw a rectangle with rounded corners
  #
  # source://rmagick//lib/rmagick_internal.rb#538
  def roundrectangle(center_x, center_y, width, height, corner_width, corner_height); end

  # Specify scaling to be applied to coordinate space on subsequent drawing commands.
  #
  # source://rmagick//lib/rmagick_internal.rb#546
  def scale(x, y); end

  # source://rmagick//lib/rmagick_internal.rb#550
  def skewx(angle); end

  # source://rmagick//lib/rmagick_internal.rb#554
  def skewy(angle); end

  # Specify the object stroke, a color name or pattern name.
  #
  # source://rmagick//lib/rmagick_internal.rb#559
  def stroke(colorspec); end

  # Specify if stroke should be antialiased or not
  #
  # source://rmagick//lib/rmagick_internal.rb#566
  def stroke_antialias(bool); end

  # Specify the object stroke, a color name or pattern name.
  #
  # source://rmagick//lib/rmagick_internal.rb#559
  def stroke_color(colorspec); end

  # Specify a stroke dash pattern
  #
  # source://rmagick//lib/rmagick_internal.rb#572
  def stroke_dasharray(*list); end

  # Specify the initial offset in the dash pattern
  #
  # source://rmagick//lib/rmagick_internal.rb#584
  def stroke_dashoffset(value = T.unsafe(nil)); end

  # source://rmagick//lib/rmagick_internal.rb#588
  def stroke_linecap(value); end

  # source://rmagick//lib/rmagick_internal.rb#594
  def stroke_linejoin(value); end

  # source://rmagick//lib/rmagick_internal.rb#600
  def stroke_miterlimit(value); end

  # Specify opacity of stroke drawing color
  #  (use "xx%" to indicate percentage)
  #
  # source://rmagick//lib/rmagick_internal.rb#608
  def stroke_opacity(opacity); end

  # Specify the object stroke, a color name or pattern name.
  #
  # source://rmagick//lib/rmagick_internal.rb#559
  def stroke_pattern(colorspec); end

  # Specify stroke (outline) width in pixels.
  #
  # source://rmagick//lib/rmagick_internal.rb#614
  def stroke_width(pixels); end

  # Draw text at position x,y. Add quotes to text that is not already quoted.
  #
  # source://rmagick//lib/rmagick_internal.rb#619
  def text(x, y, text); end

  # Specify text alignment relative to a given point
  #
  # source://rmagick//lib/rmagick_internal.rb#638
  def text_align(alignment); end

  # SVG-compatible version of text_align
  #
  # source://rmagick//lib/rmagick_internal.rb#644
  def text_anchor(anchor); end

  # Specify if rendered text is to be antialiased.
  #
  # source://rmagick//lib/rmagick_internal.rb#650
  def text_antialias(boolean); end

  # Specify color underneath text
  #
  # source://rmagick//lib/rmagick_internal.rb#656
  def text_undercolor(color); end

  # Specify center of coordinate space to use for subsequent drawing
  # commands.
  #
  # source://rmagick//lib/rmagick_internal.rb#662
  def translate(x, y); end

  private

  # source://rmagick//lib/rmagick_internal.rb#225
  def enquote(str); end

  def initialize_copy(_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#234
  def to_opacity(opacity); end

  # source://rmagick//lib/rmagick_internal.rb#243
  def to_string(obj); end
end

# Thse hashes are used to map Magick constant
# values to the strings used in the primitives.
#
# source://rmagick//lib/rmagick_internal.rb#163
Magick::Draw::ALIGN_TYPE_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#168
Magick::Draw::ANCHOR_TYPE_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#173
Magick::Draw::DECORATION_TYPE_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#179
Magick::Draw::FONT_WEIGHT_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#186
Magick::Draw::GRAVITY_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#197
Magick::Draw::PAINT_METHOD_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#204
Magick::Draw::STRETCH_TYPE_NAMES = T.let(T.unsafe(nil), Hash)

# source://rmagick//lib/rmagick_internal.rb#216
Magick::Draw::STYLE_TYPE_NAMES = T.let(T.unsafe(nil), Hash)

module Magick::DrawAttribute
  def affine=(_arg0); end
  def align=(_arg0); end
  def decorate=(_arg0); end
  def density=(_arg0); end
  def encoding=(_arg0); end
  def fill=(_arg0); end
  def fill_pattern=(_arg0); end
  def font=(_arg0); end
  def font_family=(_arg0); end
  def font_stretch=(_arg0); end
  def font_style=(_arg0); end
  def font_weight=(_arg0); end
  def gravity=(_arg0); end
  def interline_spacing=(_arg0); end
  def interword_spacing=(_arg0); end
  def kerning=(_arg0); end
  def pointsize=(_arg0); end
  def rotation=(_arg0); end
  def stroke=(_arg0); end
  def stroke_pattern=(_arg0); end
  def stroke_width=(_arg0); end
  def text_antialias=(_arg0); end
  def tile=(_arg0); end
  def undercolor=(_arg0); end
end

Magick::DstAtopCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DstCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DstInCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DstOutCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DstOverCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::DullPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::EastGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::EdgeDetectPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::EdgeInMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::EdgeMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::EdgeOutMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::EdgeVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::EdgesKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::EndAnchor = T.let(T.unsafe(nil), Magick::AnchorType)

class Magick::EndianType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

class Magick::Enum
  include ::Comparable

  def initialize(_arg0, _arg1); end

  def <=>(_arg0); end
  def ===(_arg0); end
  def to_i; end
  def to_s; end
  def |(_arg0); end
end

Magick::ErodeIntensityMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::ErodeMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::EuclideanKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::ExclusionCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ExpandedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::ExponentialQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::ExtraCondensedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::ExtraExpandedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::ExtractAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
class Magick::FatalImageMagickError < ::StandardError; end
Magick::FaxCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::FillToBorderMethod = T.let(T.unsafe(nil), Magick::PaintMethod)

class Magick::FilterType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::FlattenLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::FloatPixel = T.let(T.unsafe(nil), Magick::StorageType)
Magick::FloodfillMethod = T.let(T.unsafe(nil), Magick::PaintMethod)
Magick::FloydSteinbergDitherMethod = T.let(T.unsafe(nil), Magick::DitherMethod)

class Magick::Font < ::Struct
  def description; end
  def description=(_); end
  def encoding; end
  def encoding=(_); end
  def family; end
  def family=(_); end
  def format; end
  def format=(_); end
  def foundry; end
  def foundry=(_); end
  def name; end
  def name=(_); end
  def stretch; end
  def stretch=(_); end
  def style; end
  def style=(_); end
  def to_s; end
  def weight; end
  def weight=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::ForgetGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::FreiChenKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::FuzzErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::GIFInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::GRAYColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::GammaPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::GaussianFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::GaussianKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::GaussianNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::GaussianNoiseQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)

# source://rmagick//lib/rmagick_internal.rb#75
class Magick::Geometry
  # @raise [ArgumentError]
  # @return [Geometry] a new instance of Geometry
  #
  # source://rmagick//lib/rmagick_internal.rb#88
  def initialize(width = T.unsafe(nil), height = T.unsafe(nil), x = T.unsafe(nil), y = T.unsafe(nil), flag = T.unsafe(nil)); end

  # Returns the value of attribute flag.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def flag; end

  # Sets the attribute flag
  #
  # @param value the value to set the attribute flag to.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def flag=(_arg0); end

  # Returns the value of attribute height.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def height; end

  # Sets the attribute height
  #
  # @param value the value to set the attribute height to.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def height=(_arg0); end

  # Convert object to a geometry string
  #
  # source://rmagick//lib/rmagick_internal.rb#139
  def to_s; end

  # Returns the value of attribute width.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def width; end

  # Sets the attribute width
  #
  # @param value the value to set the attribute width to.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def width=(_arg0); end

  # Returns the value of attribute x.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def x; end

  # Sets the attribute x
  #
  # @param value the value to set the attribute x to.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def x=(_arg0); end

  # Returns the value of attribute y.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def y; end

  # Sets the attribute y
  #
  # @param value the value to set the attribute y to.
  #
  # source://rmagick//lib/rmagick_internal.rb#86
  def y=(_arg0); end

  class << self
    # source://rmagick//lib/rmagick_internal.rb#123
    def from_s(str); end
  end
end

# source://rmagick//lib/rmagick_internal.rb#76
Magick::Geometry::FLAGS = T.let(T.unsafe(nil), Array)

# source://rmagick//lib/rmagick_internal.rb#118
Magick::Geometry::H = T.let(T.unsafe(nil), Regexp)

# source://rmagick//lib/rmagick_internal.rb#121
Magick::Geometry::RE = T.let(T.unsafe(nil), Regexp)

# source://rmagick//lib/rmagick_internal.rb#77
Magick::Geometry::RFLAGS = T.let(T.unsafe(nil), Hash)

# Construct an object from a geometry string
#
# source://rmagick//lib/rmagick_internal.rb#117
Magick::Geometry::W = T.let(T.unsafe(nil), Regexp)

# source://rmagick//lib/rmagick_internal.rb#119
Magick::Geometry::X = T.let(T.unsafe(nil), Regexp)

# source://rmagick//lib/rmagick_internal.rb#120
Magick::Geometry::Y = T.let(T.unsafe(nil), Regexp)

class Magick::GeometryFlags < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

# Geometry class and related enum constants
#
# source://rmagick//lib/rmagick_internal.rb#64
class Magick::GeometryValue < ::Magick::Enum; end

class Magick::GradientFill
  def initialize(_arg0, _arg1, _arg2, _arg3, _arg4, _arg5); end

  def fill(_arg0); end
end

class Magick::GravityType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::GrayChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::GrayChannels = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::GrayVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::GrayscaleAlphaType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::GrayscalePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::GrayscaleType = T.let(T.unsafe(nil), Magick::ImageType)

# source://rmagick//lib/rmagick_internal.rb#71
Magick::GreaterGeometry = T.let(T.unsafe(nil), Magick::GeometryValue)

Magick::GreaterValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::GreenChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::Group4Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::HCLColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HCLpColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HSBColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HSIColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HSLColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HSVColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HWBColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::HammingFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::HannFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::HanningFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::HardLightCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::HardMixCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

# Example fill class. Fills the image with the specified background
# color, then crosshatches with the specified crosshatch color.
# See Magick::Draw examples.
#
# source://rmagick//lib/rmagick_internal.rb#1857
class Magick::HatchFill
  # @return [HatchFill] a new instance of HatchFill
  #
  # source://rmagick//lib/rmagick_internal.rb#1858
  def initialize(bgcolor, hatchcolor = T.unsafe(nil), dist = T.unsafe(nil)); end

  # required
  #
  # source://rmagick//lib/rmagick_internal.rb#1864
  def fill(img); end
end

Magick::HeightValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::HermiteFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::HitAndMissMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::HorizontalTileEdgeVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::HorizontalTileVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::HueChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::HueCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::HuePreview = T.let(T.unsafe(nil), Magick::PreviewType)

# source://rmagick//lib/rmagick_internal.rb#25
Magick::IMAGEMAGICK_VERSION = T.let(T.unsafe(nil), String)

# Define IPTC record number:dataset tags for use with Image#get_iptc_dataset
#
# source://rmagick//lib/rmagick_internal.rb#668
module Magick::IPTC; end

# source://rmagick//lib/rmagick_internal.rb#688
module Magick::IPTC::Application; end

# source://rmagick//lib/rmagick_internal.rb#735
Magick::IPTC::Application::Abstract = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#708
Magick::IPTC::Application::Action_Advised = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#746
Magick::IPTC::Application::Audio_Duration = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#747
Magick::IPTC::Application::Audio_Outcue = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#744
Magick::IPTC::Application::Audio_Sampling_Rate = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#745
Magick::IPTC::Application::Audio_Sampling_Resolution = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#743
Magick::IPTC::Application::Audio_Type = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#720
Magick::IPTC::Application::Author = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#722
Magick::IPTC::Application::Author_Position = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#719
Magick::IPTC::Application::By_Line = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#721
Magick::IPTC::Application::By_Line_Title = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#736
Magick::IPTC::Application::Caption = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#738
Magick::IPTC::Application::Caption_Writer = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#697
Magick::IPTC::Application::Category = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#723
Magick::IPTC::Application::City = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#734
Magick::IPTC::Application::Contact = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#701
Magick::IPTC::Application::Content_Location_Code = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#702
Magick::IPTC::Application::Content_Location_Name = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#733
Magick::IPTC::Application::Copyright_Notice = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#727
Magick::IPTC::Application::Country_Primary_Location_Code = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#728
Magick::IPTC::Application::Country_Primary_Location_Name = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#731
Magick::IPTC::Application::Credit = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#712
Magick::IPTC::Application::Date_Created = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#714
Magick::IPTC::Application::Digital_Creation_Date = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#715
Magick::IPTC::Application::Digital_Creation_Time = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#693
Magick::IPTC::Application::Edit_Status = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#737
Magick::IPTC::Application::Editor = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#694
Magick::IPTC::Application::Editorial_Update = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#705
Magick::IPTC::Application::Expiration_Date = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#706
Magick::IPTC::Application::Expiration_Time = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#699
Magick::IPTC::Application::Fixture_Identifier = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#730
Magick::IPTC::Application::Headline = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#741
Magick::IPTC::Application::Image_Orientation = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#740
Magick::IPTC::Application::Image_Type = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#700
Magick::IPTC::Application::Keywords = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#742
Magick::IPTC::Application::Language_Identifier = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#750
Magick::IPTC::Application::ObjectData_Preview_Data = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#748
Magick::IPTC::Application::ObjectData_Preview_File_Format = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#749
Magick::IPTC::Application::ObjectData_Preview_File_Format_Version = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#718
Magick::IPTC::Application::Object_Cycle = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#691
Magick::IPTC::Application::Object_Name = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#690
Magick::IPTC::Application::Object_Type_Reference = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#729
Magick::IPTC::Application::Original_Transmission_Reference = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#716
Magick::IPTC::Application::Originating_Program = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#717
Magick::IPTC::Application::Program_Version = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#725
Magick::IPTC::Application::Province = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#739
Magick::IPTC::Application::Rasterized_Caption = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#689
Magick::IPTC::Application::Record_Version = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#710
Magick::IPTC::Application::Reference_Date = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#711
Magick::IPTC::Application::Reference_Number = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#709
Magick::IPTC::Application::Reference_Service = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#703
Magick::IPTC::Application::Release_Date = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#704
Magick::IPTC::Application::Release_Time = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#732
Magick::IPTC::Application::Source = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#707
Magick::IPTC::Application::Special_Instructions = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#726
Magick::IPTC::Application::State = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#724
Magick::IPTC::Application::Sub_Location = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#696
Magick::IPTC::Application::Subject_Reference = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#698
Magick::IPTC::Application::Supplemental_Category = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#713
Magick::IPTC::Application::Time_Created = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#692
Magick::IPTC::Application::Title = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#695
Magick::IPTC::Application::Urgency = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#670
module Magick::IPTC::Envelope; end

# source://rmagick//lib/rmagick_internal.rb#684
Magick::IPTC::Envelope::ARM_Identifier = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#685
Magick::IPTC::Envelope::ARM_Version = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#681
Magick::IPTC::Envelope::Coded_Character_Set = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#679
Magick::IPTC::Envelope::Date_Sent = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#672
Magick::IPTC::Envelope::Destination = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#676
Magick::IPTC::Envelope::Envelope_Number = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#678
Magick::IPTC::Envelope::Envelope_Priority = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#673
Magick::IPTC::Envelope::File_Format = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#674
Magick::IPTC::Envelope::File_Format_Version = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#671
Magick::IPTC::Envelope::Model_Version = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#677
Magick::IPTC::Envelope::Product_ID = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#675
Magick::IPTC::Envelope::Service_Identifier = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#680
Magick::IPTC::Envelope::Time_Sent = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#682
Magick::IPTC::Envelope::UNO = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#683
Magick::IPTC::Envelope::Unique_Name_of_Object = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#760
module Magick::IPTC::ObjectData; end

# source://rmagick//lib/rmagick_internal.rb#761
Magick::IPTC::ObjectData::Subfile = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#764
module Magick::IPTC::Post_ObjectData_Descriptor; end

# source://rmagick//lib/rmagick_internal.rb#765
Magick::IPTC::Post_ObjectData_Descriptor::Confirmed_ObjectData_Size = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#753
module Magick::IPTC::Pre_ObjectData_Descriptor; end

# source://rmagick//lib/rmagick_internal.rb#755
Magick::IPTC::Pre_ObjectData_Descriptor::Max_Subfile_Size = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#757
Magick::IPTC::Pre_ObjectData_Descriptor::Maximum_ObjectData_Size = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#756
Magick::IPTC::Pre_ObjectData_Descriptor::ObjectData_Size_Announced = T.let(T.unsafe(nil), String)

# source://rmagick//lib/rmagick_internal.rb#754
Magick::IPTC::Pre_ObjectData_Descriptor::Size_Mode = T.let(T.unsafe(nil), String)

# Ruby-level Magick::Image methods
#
# source://rmagick//lib/rmagick_internal.rb#771
class Magick::Image
  include ::Comparable

  def initialize(*_arg0); end

  def <=>(_arg0); end
  def [](_arg0); end
  def []=(_arg0, _arg1); end
  def __display__(*_arg0); end
  def _dump(_arg0); end
  def adaptive_blur(*_arg0); end
  def adaptive_blur_channel(*_arg0); end
  def adaptive_resize(*_arg0); end
  def adaptive_sharpen(*_arg0); end
  def adaptive_sharpen_channel(*_arg0); end
  def adaptive_threshold(*_arg0); end
  def add_compose_mask(_arg0); end
  def add_noise(_arg0); end
  def add_noise_channel(*_arg0); end
  def add_profile(_arg0); end
  def affine_transform(_arg0); end
  def affinity(*_arg0); end
  def alpha(*_arg0); end
  def alpha?; end

  # Provide an alternate version of Draw#annotate, for folks who
  # want to find it in this class.
  #
  # source://rmagick//lib/rmagick_internal.rb#778
  def annotate(draw, width, height, x, y, text, &block); end

  def auto_gamma_channel(*_arg0); end
  def auto_level_channel(*_arg0); end
  def auto_orient; end
  def auto_orient!; end
  def background_color; end
  def background_color=(_arg0); end
  def base_columns; end
  def base_filename; end
  def base_rows; end
  def bias; end
  def bias=(_arg0); end
  def bilevel_channel(*_arg0); end
  def black_point_compensation; end
  def black_point_compensation=(_arg0); end
  def black_threshold(*_arg0); end
  def blend(*_arg0); end
  def blue_shift(*_arg0); end
  def blur_channel(*_arg0); end
  def blur_image(*_arg0); end
  def border(_arg0, _arg1, _arg2); end
  def border!(_arg0, _arg1, _arg2); end
  def border_color; end
  def border_color=(_arg0); end
  def bounding_box; end
  def change_geometry(_arg0); end
  def change_geometry!(_arg0); end
  def changed?; end
  def channel(_arg0); end
  def channel_compare(*_arg0); end
  def channel_depth(*_arg0); end
  def channel_entropy(*_arg0); end
  def channel_extrema(*_arg0); end
  def channel_mean(*_arg0); end
  def charcoal(*_arg0); end
  def check_destroyed; end
  def chop(_arg0, _arg1, _arg2, _arg3); end
  def chromaticity; end
  def chromaticity=(_arg0); end
  def class_type; end
  def class_type=(_arg0); end
  def clone; end
  def clut_channel(*_arg0); end

  # Set all pixels that are neighbors of x,y and are not the border color
  # to the fill color
  #
  # source://rmagick//lib/rmagick_internal.rb#800
  def color_fill_to_border(x, y, fill); end

  def color_flood_fill(_arg0, _arg1, _arg2, _arg3, _arg4); end

  # Set all pixels that have the same color as the pixel at x,y and
  # are neighbors to the fill color
  #
  # source://rmagick//lib/rmagick_internal.rb#793
  def color_floodfill(x, y, fill); end

  def color_histogram; end

  # Set the color at x,y
  #
  # source://rmagick//lib/rmagick_internal.rb#785
  def color_point(x, y, fill); end

  def color_profile; end
  def color_profile=(_arg0); end

  # Set all pixels to the fill color. Very similar to Image#erase!
  # Accepts either String or Pixel arguments
  #
  # source://rmagick//lib/rmagick_internal.rb#806
  def color_reset!(fill); end

  def colorize(*_arg0); end
  def colormap(*_arg0); end
  def colors; end
  def colorspace; end
  def colorspace=(_arg0); end
  def columns; end
  def compare_channel(*_arg0); end
  def compose; end
  def compose=(_arg0); end
  def composite(*_arg0); end
  def composite!(*_arg0); end
  def composite_affine(_arg0, _arg1); end
  def composite_channel(*_arg0); end
  def composite_channel!(*_arg0); end
  def composite_mathematics(*_arg0); end
  def composite_tiled(*_arg0); end
  def composite_tiled!(*_arg0); end
  def compress_colormap!; end
  def compression; end
  def compression=(_arg0); end
  def contrast(*_arg0); end
  def contrast_stretch_channel(*_arg0); end
  def convolve(_arg0, _arg1); end
  def convolve_channel(*_arg0); end
  def copy; end
  def crop(*_arg0); end
  def crop!(*_arg0); end

  # Force an image to exact dimensions without changing the aspect ratio.
  # Resize and crop if necessary. (Thanks to Jerett Taylor!)
  # Preserve aliases used < RMagick 2.0.1
  #
  # source://rmagick//lib/rmagick_internal.rb#968
  def crop_resized(ncols, nrows = T.unsafe(nil), gravity = T.unsafe(nil)); end

  # source://rmagick//lib/rmagick_internal.rb#972
  def crop_resized!(ncols, nrows = T.unsafe(nil), gravity = T.unsafe(nil)); end

  # Used by ImageList methods - see ImageList#cur_image
  #
  # source://rmagick//lib/rmagick_internal.rb#821
  def cur_image; end

  def cycle_colormap(_arg0); end
  def decipher(_arg0); end
  def define(_arg0, _arg1); end
  def delay; end
  def delay=(_arg0); end
  def delete_compose_mask; end
  def delete_profile(_arg0); end
  def density; end
  def density=(_arg0); end
  def depth; end
  def deskew(*_arg0); end
  def despeckle; end
  def destroy!; end
  def destroyed?; end
  def difference(_arg0); end
  def directory; end
  def dispatch(*_arg0); end
  def displace(*_arg0); end
  def display; end
  def dispose; end
  def dispose=(_arg0); end
  def dissolve(*_arg0); end
  def distort(*_arg0); end
  def distortion_channel(*_arg0); end
  def dup; end

  # Iterate over IPTC record number:dataset tags, yield for each non-nil dataset
  #
  # source://rmagick//lib/rmagick_internal.rb#879
  def each_iptc_dataset; end

  # Thanks to Russell Norris!
  #
  # source://rmagick//lib/rmagick_internal.rb#826
  def each_pixel; end

  def each_profile; end
  def edge(*_arg0); end
  def emboss(*_arg0); end
  def encipher(_arg0); end
  def endian; end
  def endian=(_arg0); end
  def enhance; end
  def equalize; end
  def equalize_channel(*_arg0); end
  def erase!; end
  def excerpt(_arg0, _arg1, _arg2, _arg3); end
  def excerpt!(_arg0, _arg1, _arg2, _arg3); end
  def export_pixels(*_arg0); end
  def export_pixels_to_str(*_arg0); end
  def extent(*_arg0); end
  def extract_info; end
  def extract_info=(_arg0); end
  def filename; end
  def filesize; end
  def filter; end
  def filter=(_arg0); end
  def find_similar_region(*_arg0); end
  def flip; end
  def flip!; end
  def flop; end
  def flop!; end
  def format; end
  def format=(_arg0); end
  def frame(*_arg0); end
  def function_channel(*_arg0); end
  def fuzz; end
  def fuzz=(_arg0); end
  def fx(*_arg0); end
  def gamma; end
  def gamma=(_arg0); end
  def gamma_channel(*_arg0); end
  def gamma_correct(*_arg0); end
  def gaussian_blur(*_arg0); end
  def gaussian_blur_channel(*_arg0); end
  def geometry; end
  def geometry=(_arg0); end

  # Retrieve EXIF data by entry or all. If one or more entry names specified,
  # return the values associated with the entries. If no entries specified,
  # return all entries and values. The return value is an array of [name,value]
  # arrays.
  #
  # source://rmagick//lib/rmagick_internal.rb#837
  def get_exif_by_entry(*entry); end

  # Retrieve EXIF data by tag number or all tag/value pairs. The return value is a hash.
  #
  # source://rmagick//lib/rmagick_internal.rb#853
  def get_exif_by_number(*tag); end

  # Retrieve IPTC information by record number:dataset tag constant defined in
  # Magick::IPTC, above.
  #
  # source://rmagick//lib/rmagick_internal.rb#874
  def get_iptc_dataset(ds); end

  def get_pixels(_arg0, _arg1, _arg2, _arg3); end
  def gravity; end
  def gravity=(_arg0); end
  def gray?; end
  def grey?; end
  def histogram?; end
  def image_type; end
  def image_type=(_arg0); end
  def implode(*_arg0); end
  def import_pixels(*_arg0); end
  def inspect; end
  def interlace; end
  def interlace=(_arg0); end
  def iptc_profile; end
  def iptc_profile=(_arg0); end
  def iterations; end
  def iterations=(_arg0); end

  # (Thanks to Al Evans for the suggestion.)
  #
  # source://rmagick//lib/rmagick_internal.rb#903
  def level(black_point = T.unsafe(nil), white_point = T.unsafe(nil), gamma = T.unsafe(nil)); end

  def level2(*_arg0); end
  def level_channel(*_arg0); end
  def level_colors(*_arg0); end
  def levelize_channel(*_arg0); end
  def linear_stretch(*_arg0); end
  def liquid_rescale(*_arg0); end
  def magnify; end
  def magnify!; end
  def marshal_dump; end
  def marshal_load(_arg0); end
  def mask(*_arg0); end
  def matte_color; end
  def matte_color=(_arg0); end

  # Make transparent any neighbor pixel that is not the border color.
  #
  # source://rmagick//lib/rmagick_internal.rb#954
  def matte_fill_to_border(x, y); end

  def matte_flood_fill(*_arg0); end

  # Make transparent any pixel that matches the color of the pixel
  # at (x,y) and is a neighbor.
  #
  # source://rmagick//lib/rmagick_internal.rb#946
  def matte_floodfill(x, y); end

  # Make the pixel at (x,y) transparent.
  #
  # source://rmagick//lib/rmagick_internal.rb#926
  def matte_point(x, y); end

  # Make transparent all pixels that are the same color as the
  # pixel at (x, y).
  #
  # source://rmagick//lib/rmagick_internal.rb#937
  def matte_replace(x, y); end

  # Make all pixels transparent.
  #
  # source://rmagick//lib/rmagick_internal.rb#961
  def matte_reset!; end

  def mean_error_per_pixel; end
  def median_filter(*_arg0); end
  def mime_type; end
  def minify; end
  def minify!; end
  def modulate(*_arg0); end
  def monochrome?; end
  def montage; end
  def morphology(_arg0, _arg1, _arg2); end
  def morphology_channel(_arg0, _arg1, _arg2, _arg3); end
  def motion_blur(*_arg0); end
  def negate(*_arg0); end
  def negate_channel(*_arg0); end
  def normalize; end
  def normalize_channel(*_arg0); end
  def normalized_maximum_error; end
  def normalized_mean_error; end
  def number_colors; end
  def offset; end
  def offset=(_arg0); end
  def oil_paint(*_arg0); end
  def opaque(_arg0, _arg1); end
  def opaque?; end
  def opaque_channel(*_arg0); end
  def ordered_dither(*_arg0); end
  def orientation; end
  def orientation=(_arg0); end
  def page; end
  def page=(_arg0); end
  def paint_transparent(*_arg0); end
  def palette?; end
  def pixel_color(*_arg0); end
  def pixel_interpolation_method; end
  def pixel_interpolation_method=(_arg0); end
  def polaroid(*_arg0); end
  def posterize(*_arg0); end
  def preview(_arg0); end
  def profile!(_arg0, _arg1); end
  def properties; end
  def quality; end
  def quantize(*_arg0); end
  def quantum_depth; end
  def quantum_operator(*_arg0); end
  def radial_blur(_arg0); end
  def radial_blur_channel(*_arg0); end
  def raise(*_arg0); end
  def random_threshold_channel(*_arg0); end
  def recolor(_arg0); end
  def reduce_noise(_arg0); end
  def remap(*_arg0); end
  def rendering_intent; end
  def rendering_intent=(_arg0); end
  def resample(*_arg0); end
  def resample!(*_arg0); end
  def resize(*_arg0); end
  def resize!(*_arg0); end

  # Force an image to exact dimensions without changing the aspect ratio.
  # Resize and crop if necessary. (Thanks to Jerett Taylor!)
  #
  # source://rmagick//lib/rmagick_internal.rb#968
  def resize_to_fill(ncols, nrows = T.unsafe(nil), gravity = T.unsafe(nil)); end

  # source://rmagick//lib/rmagick_internal.rb#972
  def resize_to_fill!(ncols, nrows = T.unsafe(nil), gravity = T.unsafe(nil)); end

  # Convenience method to resize retaining the aspect ratio.
  # (Thanks to Robert Manni!)
  #
  # source://rmagick//lib/rmagick_internal.rb#988
  def resize_to_fit(cols, rows = T.unsafe(nil)); end

  # source://rmagick//lib/rmagick_internal.rb#995
  def resize_to_fit!(cols, rows = T.unsafe(nil)); end

  def roll(_arg0, _arg1); end
  def rotate(*_arg0); end
  def rotate!(*_arg0); end
  def rows; end
  def sample(*_arg0); end
  def sample!(*_arg0); end
  def scale(*_arg0); end
  def scale!(*_arg0); end
  def scene; end
  def segment(*_arg0); end
  def selective_blur_channel(*_arg0); end
  def separate(*_arg0); end
  def sepiatone(*_arg0); end
  def set_channel_depth(_arg0, _arg1); end
  def shade(*_arg0); end
  def shadow(*_arg0); end
  def sharpen(*_arg0); end
  def sharpen_channel(*_arg0); end
  def shave(_arg0, _arg1); end
  def shave!(_arg0, _arg1); end
  def shear(_arg0, _arg1); end
  def sigmoidal_contrast_channel(*_arg0); end
  def signature; end
  def sketch(*_arg0); end
  def solarize(*_arg0); end
  def sparse_color(*_arg0); end
  def splice(*_arg0); end
  def spread(*_arg0); end
  def start_loop; end
  def start_loop=(_arg0); end
  def stegano(_arg0, _arg1); end
  def stereo(_arg0); end
  def store_pixels(_arg0, _arg1, _arg2, _arg3, _arg4); end
  def strip!; end
  def swirl(_arg0); end

  # Replace neighboring pixels to border color with texture pixels
  #
  # source://rmagick//lib/rmagick_internal.rb#1009
  def texture_fill_to_border(x, y, texture); end

  def texture_flood_fill(_arg0, _arg1, _arg2, _arg3, _arg4); end

  # Replace matching neighboring pixels with texture pixels
  #
  # source://rmagick//lib/rmagick_internal.rb#1003
  def texture_floodfill(x, y, texture); end

  def threshold(_arg0); end
  def thumbnail(*_arg0); end
  def thumbnail!(*_arg0); end
  def ticks_per_second; end
  def ticks_per_second=(_arg0); end
  def tint(*_arg0); end
  def to_blob; end
  def to_color(_arg0); end
  def total_colors; end
  def total_ink_density; end
  def transparent(*_arg0); end
  def transparent_chroma(*_arg0); end
  def transparent_color; end
  def transparent_color=(_arg0); end
  def transpose; end
  def transpose!; end
  def transverse; end
  def transverse!; end
  def trim(*_arg0); end
  def trim!(*_arg0); end
  def undefine(_arg0); end
  def unique_colors; end
  def units; end
  def units=(_arg0); end
  def unsharp_mask(*_arg0); end
  def unsharp_mask_channel(*_arg0); end

  # Construct a view. If a block is present, yield and pass the view
  # object, otherwise return the view object.
  #
  # source://rmagick//lib/rmagick_internal.rb#1015
  def view(x, y, width, height); end

  def vignette(*_arg0); end
  def virtual_pixel_method; end
  def virtual_pixel_method=(_arg0); end
  def watermark(*_arg0); end
  def wave(*_arg0); end
  def wet_floor(*_arg0); end
  def white_threshold(*_arg0); end
  def write(_arg0); end
  def x_resolution; end
  def x_resolution=(_arg0); end
  def y_resolution; end
  def y_resolution=(_arg0); end

  private

  def initialize_copy(_arg0); end

  class << self
    def _load(_arg0); end
    def capture(*_arg0); end
    def constitute(_arg0, _arg1, _arg2, _arg3); end
    def from_blob(_arg0); end
    def ping(_arg0); end
    def read(_arg0); end
    def read_inline(_arg0); end
  end
end

class Magick::Image::DrawOptions
  include ::Magick::DrawAttribute

  def initialize; end
end

class Magick::Image::Info
  def initialize; end

  def [](*_arg0); end
  def []=(*_arg0); end
  def antialias; end
  def antialias=(_arg0); end
  def attenuate; end
  def attenuate=(_arg0); end
  def authenticate; end
  def authenticate=(_arg0); end
  def background_color; end
  def background_color=(_arg0); end
  def border_color; end
  def border_color=(_arg0); end
  def caption; end
  def caption=(_arg0); end
  def channel(*_arg0); end
  def colorspace; end
  def colorspace=(_arg0); end
  def comment; end
  def comment=(_arg0); end
  def compression; end
  def compression=(_arg0); end
  def define(*_arg0); end
  def delay; end
  def delay=(_arg0); end
  def density; end
  def density=(_arg0); end
  def depth; end
  def depth=(_arg0); end
  def dispose; end
  def dispose=(_arg0); end
  def dither; end
  def dither=(_arg0); end
  def endian; end
  def endian=(_arg0); end
  def extract; end
  def extract=(_arg0); end
  def filename; end
  def filename=(_arg0); end
  def fill; end
  def fill=(_arg0); end
  def font; end
  def font=(_arg0); end
  def format; end
  def format=(_arg0); end
  def freeze; end
  def fuzz; end
  def fuzz=(_arg0); end
  def gravity; end
  def gravity=(_arg0); end
  def image_type; end
  def image_type=(_arg0); end
  def interlace; end
  def interlace=(_arg0); end
  def label; end
  def label=(_arg0); end
  def matte_color; end
  def matte_color=(_arg0); end
  def monochrome; end
  def monochrome=(_arg0); end
  def number_scenes; end
  def number_scenes=(_arg0); end
  def orientation; end
  def orientation=(_arg0); end
  def origin; end
  def origin=(_arg0); end
  def page; end
  def page=(_arg0); end
  def pointsize; end
  def pointsize=(_arg0); end
  def quality; end
  def quality=(_arg0); end
  def sampling_factor; end
  def sampling_factor=(_arg0); end
  def scene; end
  def scene=(_arg0); end
  def server_name; end
  def server_name=(_arg0); end
  def size; end
  def size=(_arg0); end
  def stroke; end
  def stroke=(_arg0); end
  def stroke_width; end
  def stroke_width=(_arg0); end
  def texture=(_arg0); end
  def tile_offset; end
  def tile_offset=(_arg0); end
  def transparent_color; end
  def transparent_color=(_arg0); end
  def undefine(_arg0, _arg1); end
  def undercolor; end
  def undercolor=(_arg0); end
  def units; end
  def units=(_arg0); end
  def view; end
  def view=(_arg0); end
end

class Magick::Image::PolaroidOptions
  include ::Magick::DrawAttribute

  def initialize; end

  def border_color=(_arg0); end
  def shadow_color=(_arg0); end
end

# Magick::Image::View class
#
# source://rmagick//lib/rmagick_internal.rb#1029
class Magick::Image::View
  # @return [View] a new instance of View
  #
  # source://rmagick//lib/rmagick_internal.rb#1033
  def initialize(img, x, y, width, height); end

  # source://rmagick//lib/rmagick_internal.rb#1046
  def [](*args); end

  # Returns the value of attribute dirty.
  #
  # source://rmagick//lib/rmagick_internal.rb#1031
  def dirty; end

  # Sets the attribute dirty
  #
  # @param value the value to set the attribute dirty to.
  #
  # source://rmagick//lib/rmagick_internal.rb#1031
  def dirty=(_arg0); end

  # Returns the value of attribute height.
  #
  # source://rmagick//lib/rmagick_internal.rb#1030
  def height; end

  # Store changed pixels back to image
  #
  # source://rmagick//lib/rmagick_internal.rb#1053
  def sync(force = T.unsafe(nil)); end

  # Get update from Rows - if @dirty ever becomes
  # true, don't change it back to false!
  #
  # source://rmagick//lib/rmagick_internal.rb#1060
  def update(rows); end

  # Returns the value of attribute width.
  #
  # source://rmagick//lib/rmagick_internal.rb#1030
  def width; end

  # Returns the value of attribute x.
  #
  # source://rmagick//lib/rmagick_internal.rb#1030
  def x; end

  # Returns the value of attribute y.
  #
  # source://rmagick//lib/rmagick_internal.rb#1030
  def y; end
end

# Magick::Image::View::Pixels
# Defines channel attribute getters/setters
#
# source://rmagick//lib/rmagick_internal.rb#1068
class Magick::Image::View::Pixels < ::Array
  include ::Observable

  # source://rmagick//lib/rmagick_internal.rb#1074
  def blue; end

  # source://rmagick//lib/rmagick_internal.rb#1077
  def blue=(v); end

  # source://rmagick//lib/rmagick_internal.rb#1074
  def green; end

  # source://rmagick//lib/rmagick_internal.rb#1077
  def green=(v); end

  # source://rmagick//lib/rmagick_internal.rb#1074
  def opacity; end

  # source://rmagick//lib/rmagick_internal.rb#1077
  def opacity=(v); end

  # source://rmagick//lib/rmagick_internal.rb#1074
  def red; end

  # source://rmagick//lib/rmagick_internal.rb#1077
  def red=(v); end
end

# Magick::Image::View::Rows
#
# source://rmagick//lib/rmagick_internal.rb#1088
class Magick::Image::View::Rows
  include ::Observable

  # @return [Rows] a new instance of Rows
  #
  # source://rmagick//lib/rmagick_internal.rb#1091
  def initialize(view, width, height, rows); end

  # source://rmagick//lib/rmagick_internal.rb#1098
  def [](*args); end

  # source://rmagick//lib/rmagick_internal.rb#1116
  def []=(*args); end

  # A pixel has been modified. Tell the view.
  #
  # source://rmagick//lib/rmagick_internal.rb#1132
  def update(pixel); end

  private

  # source://rmagick//lib/rmagick_internal.rb#1141
  def cols(*args); end

  # iterator called from subscript methods
  #
  # source://rmagick//lib/rmagick_internal.rb#1219
  def each; end
end

# class Magick::Image
#
# source://rmagick//lib/rmagick_internal.rb#1236
class Magick::ImageList
  include ::Comparable
  include ::Enumerable

  # Initialize new instances
  #
  # @return [ImageList] a new instance of ImageList
  #
  # source://rmagick//lib/rmagick_internal.rb#1551
  def initialize(*filenames, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1304
  def &(other); end

  # source://rmagick//lib/rmagick_internal.rb#1316
  def *(other); end

  # source://rmagick//lib/rmagick_internal.rb#1304
  def +(other); end

  # source://rmagick//lib/rmagick_internal.rb#1304
  def -(other); end

  # source://rmagick//lib/rmagick_internal.rb#1325
  def <<(obj); end

  # Compare ImageLists
  # Compare each image in turn until the result of a comparison
  # is not 0. If all comparisons return 0, then
  #   return if A.scene != B.scene
  #   return A.length <=> B.length
  #
  # source://rmagick//lib/rmagick_internal.rb#1337
  def <=>(other); end

  # source://rmagick//lib/rmagick_internal.rb#1356
  def [](*args); end

  # source://rmagick//lib/rmagick_internal.rb#1366
  def []=(*args); end

  def __display__(*_arg0); end

  # override Enumerable#collect
  #
  # source://rmagick//lib/rmagick_internal.rb#1408
  def __map__(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1417
  def __map__!(&block); end

  # Ensure respond_to? answers correctly when we are delegating to Image
  def __respond_to__?(*_arg0); end

  # ImageMagick used affinity in 6.4.3, switch to remap in 6.4.4.
  def affinity(*_arg0); end

  def animate(*_arg0); end
  def append(_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def at(*args, &block); end

  def average; end

  # source://rmagick//lib/rmagick_internal.rb#1396
  def clear; end

  # source://rmagick//lib/rmagick_internal.rb#1401
  def clone; end

  def coalesce; end

  # override Enumerable#collect
  #
  # source://rmagick//lib/rmagick_internal.rb#1408
  def collect(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1417
  def collect!(&block); end

  def combine(*_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#1446
  def compact; end

  # source://rmagick//lib/rmagick_internal.rb#1455
  def compact!; end

  def composite_layers(*_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#1462
  def concat(other); end

  # Make a deep copy
  #
  # source://rmagick//lib/rmagick_internal.rb#1424
  def copy; end

  # Return the current image
  #
  # source://rmagick//lib/rmagick_internal.rb#1432
  def cur_image; end

  def deconstruct; end

  # Set same delay for all images
  #
  # @raise [ArgumentError]
  #
  # source://rmagick//lib/rmagick_internal.rb#1470
  def delay=(d); end

  # source://rmagick//lib/rmagick_internal.rb#1476
  def delete(obj, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1484
  def delete_at(ndx); end

  # source://rmagick//lib/rmagick_internal.rb#1491
  def delete_if(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1809
  def destroy!; end

  # @return [Boolean]
  #
  # source://rmagick//lib/rmagick_internal.rb#1814
  def destroyed?; end

  def display; end

  # source://rmagick//lib/rmagick_internal.rb#1498
  def dup; end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def each(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def each_index(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def empty?(*args, &block); end

  # @return [Boolean]
  #
  # source://rmagick//lib/rmagick_internal.rb#1505
  def eql?(other); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def fetch(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1521
  def fill(*args, &block); end

  # Override Enumerable's find_all
  #
  # source://rmagick//lib/rmagick_internal.rb#1531
  def find_all(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def first(*args, &block); end

  def flatten_images; end

  # source://rmagick//lib/rmagick_internal.rb#1541
  def from_blob(*blobs, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def hash(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def include?(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def index(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1800
  def indexes(*args); end

  # source://rmagick//lib/rmagick_internal.rb#1800
  def indices(*args); end

  # source://rmagick//lib/rmagick_internal.rb#1561
  def insert(index, *args); end

  # Call inspect for all the images
  #
  # source://rmagick//lib/rmagick_internal.rb#1570
  def inspect; end

  # Set the number of iterations of an animated GIF
  #
  # source://rmagick//lib/rmagick_internal.rb#1576
  def iterations=(n); end

  # source://rmagick//lib/rmagick_internal.rb#1582
  def last(*args); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def length(*args, &block); end

  # override Enumerable#collect
  # ImageList#map took over the "map" name. Use alternatives.
  #
  # source://rmagick//lib/rmagick_internal.rb#1408
  def map(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1417
  def map!(&block); end

  # Custom marshal/unmarshal for Ruby 1.8.
  #
  # source://rmagick//lib/rmagick_internal.rb#1596
  def marshal_dump; end

  # source://rmagick//lib/rmagick_internal.rb#1602
  def marshal_load(ary); end

  # The ImageList class supports the Magick::Image class methods by simply sending
  # the method to the current image. If the method isn't explicitly supported,
  # send it to the current image in the array. If there are no images, send
  # it up the line. Catch a NameError and emit a useful message.
  #
  # source://rmagick//lib/rmagick_internal.rb#1612
  def method_missing(meth_id, *args, &block); end

  def montage; end
  def morph(_arg0); end
  def mosaic; end

  # Create a new image and add it to the end
  #
  # source://rmagick//lib/rmagick_internal.rb#1628
  def new_image(cols, rows, *fill, &info_blk); end

  def optimize_layers(_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#1632
  def partition(&block); end

  # Ping files and concatenate the new images
  #
  # source://rmagick//lib/rmagick_internal.rb#1644
  def ping(*files, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1653
  def pop; end

  # source://rmagick//lib/rmagick_internal.rb#1660
  def push(*objs); end

  def quantize(*_arg0); end

  # Read files and concatenate the new images
  #
  # source://rmagick//lib/rmagick_internal.rb#1670
  def read(*files, &block); end

  # override Enumerable's reject
  #
  # source://rmagick//lib/rmagick_internal.rb#1680
  def reject(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1689
  def reject!(&block); end

  def remap(*_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#1697
  def replace(other); end

  # @return [Boolean]
  #
  # source://rmagick//lib/rmagick_internal.rb#1709
  def respond_to?(meth_id, priv = T.unsafe(nil)); end

  # source://rmagick//lib/rmagick_internal.rb#1719
  def reverse; end

  # source://rmagick//lib/rmagick_internal.rb#1727
  def reverse!; end

  # source://rmagick//lib/rmagick_internal.rb#1734
  def reverse_each(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def rindex(*args, &block); end

  # Returns the value of attribute scene.
  #
  # source://rmagick//lib/rmagick_internal.rb#1239
  def scene; end

  # Allow scene to be set to nil
  #
  # source://rmagick//lib/rmagick_internal.rb#1286
  def scene=(n); end

  # Override Enumerable's find_all
  #
  # source://rmagick//lib/rmagick_internal.rb#1531
  def select(&block); end

  # source://rmagick//lib/rmagick_internal.rb#1739
  def shift; end

  # source://rmagick//lib/rmagick_internal.rb#1384
  def size(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1746
  def slice(*args); end

  # source://rmagick//lib/rmagick_internal.rb#1761
  def slice!(*args); end

  # source://rmagick//lib/rmagick_internal.rb#1391
  def sort!(*args, &block); end

  # source://rmagick//lib/rmagick_internal.rb#1768
  def ticks_per_second=(t); end

  # source://rmagick//lib/rmagick_internal.rb#1773
  def to_a; end

  def to_blob; end

  # source://rmagick//lib/rmagick_internal.rb#1777
  def uniq; end

  # source://rmagick//lib/rmagick_internal.rb#1785
  def uniq!(*_args); end

  # source://rmagick//lib/rmagick_internal.rb#1793
  def unshift(obj); end

  # source://rmagick//lib/rmagick_internal.rb#1800
  def values_at(*args); end

  def write(_arg0); end

  # source://rmagick//lib/rmagick_internal.rb#1304
  def |(other); end

  protected

  # source://rmagick//lib/rmagick_internal.rb#1251
  def assert_image(obj); end

  # Ensure array is always an array of Magick::Image objects
  #
  # source://rmagick//lib/rmagick_internal.rb#1256
  def assert_image_array(ary); end

  # Find old current image, update scene number
  # current is the id of the old current image.
  #
  # source://rmagick//lib/rmagick_internal.rb#1263
  def set_current(current); end

  private

  # source://rmagick//lib/rmagick_internal.rb#1243
  def get_current; end
end

class Magick::ImageList::Montage
  def initialize; end

  def background_color=(_arg0); end
  def border_color=(_arg0); end
  def border_width=(_arg0); end
  def compose=(_arg0); end
  def filename=(_arg0); end
  def fill=(_arg0); end
  def font=(_arg0); end
  def frame=(_arg0); end
  def freeze; end
  def geometry=(_arg0); end
  def gravity=(_arg0); end
  def matte_color=(_arg0); end
  def pointsize=(_arg0); end
  def shadow=(_arg0); end
  def stroke=(_arg0); end
  def texture=(_arg0); end
  def tile=(_arg0); end
  def title=(_arg0); end
end

class Magick::ImageMagickError < ::StandardError
  def initialize(*_arg0); end

  def magick_location; end
end

class Magick::ImageType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::ImplodePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ImpulseNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::ImpulseNoiseQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::InCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::IndexChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::IntegerInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)

class Magick::InterlaceType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::InverseColorInterpolate = T.let(T.unsafe(nil), Magick::SparseColorMethod)
Magick::ItalicStyle = T.let(T.unsafe(nil), Magick::StyleType)
Magick::IterativeDistanceMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::JBIG1Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::JBIG2Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::JPEG2000Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::JPEGCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::JPEGInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::JPEGPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::JincFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::KaiserFilter = T.let(T.unsafe(nil), Magick::FilterType)

class Magick::KernelInfo
  def initialize(_arg0); end

  def clone; end
  def dup; end
  def scale(_arg0, _arg1); end
  def scale_geometry(_arg0); end
  def unity_add(_arg0); end

  class << self
    def builtin(_arg0, _arg1); end
  end
end

class Magick::KernelInfoType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::KirschKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::LCHColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LCHabColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LCHuvColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LMSColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LSBEndian = T.let(T.unsafe(nil), Magick::EndianType)
Magick::LShiftQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::LZMACompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::LZWCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::LabColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LagrangeFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::Lanczos2Filter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::Lanczos2SharpFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::LanczosFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::LanczosRadiusFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::LanczosSharpFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::LaplacianKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::LaplacianNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::LaplacianNoiseQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)

class Magick::LayerMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::LeftAlign = T.let(T.unsafe(nil), Magick::AlignType)
Magick::LeftBottomOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::LeftTopOrientation = T.let(T.unsafe(nil), Magick::OrientationType)

# source://rmagick//lib/rmagick_internal.rb#70
Magick::LessGeometry = T.let(T.unsafe(nil), Magick::GeometryValue)

Magick::LessValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::LightenCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::LightenIntensityCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::LighterWeight = T.let(T.unsafe(nil), Magick::WeightType)
Magick::LineEndsKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::LineInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::LineJunctionsKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::LineThroughDecoration = T.let(T.unsafe(nil), Magick::DecorationType)
Magick::LinearBurnCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::LinearDodgeCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::LinearGRAYColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LinearLightCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::LoGKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::LogColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::LogQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::LongPixel = T.let(T.unsafe(nil), Magick::StorageType)
Magick::Long_version = T.let(T.unsafe(nil), String)
Magick::LosslessJPEGCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::LuminizeCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::LuminosityChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::LuvColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::MAGICKCORE_QUANTUM_DEPTH = T.let(T.unsafe(nil), Integer)
Magick::MANAGED_MEMORY = T.let(T.unsafe(nil), TrueClass)
Magick::MSBEndian = T.let(T.unsafe(nil), Magick::EndianType)
Magick::MagentaChannel = T.let(T.unsafe(nil), Magick::ChannelType)

class Magick::MagickFunction < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::Magick_features = T.let(T.unsafe(nil), String)
Magick::Magick_version = T.let(T.unsafe(nil), String)
Magick::ManhattanKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::MaskVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::MathematicsCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::MaxQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::MeanAbsoluteErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::MeanErrorPerPixelErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::MeanQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::MeanSquaredErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::MedianQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::MergeLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::MeshInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)

class Magick::MetricType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::MiddleAnchor = T.let(T.unsafe(nil), Magick::AnchorType)
Magick::MinQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)

# source://rmagick//lib/rmagick_internal.rb#73
Magick::MinimumGeometry = T.let(T.unsafe(nil), Magick::GeometryValue)

Magick::MinimumValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::MinusDstCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::MinusSrcCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::MirrorVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::MitchellFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::ModulateCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ModulusAddCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ModulusSubtractCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

class Magick::MorphologyMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::MosaicLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::MultiplicativeGaussianNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::MultiplicativeNoiseQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::MultiplyCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::MultiplyQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::NearestInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::NoCompliance = T.let(T.unsafe(nil), Magick::ComplianceType)
Magick::NoCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::NoCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::NoDecoration = T.let(T.unsafe(nil), Magick::DecorationType)
Magick::NoDitherMethod = T.let(T.unsafe(nil), Magick::DitherMethod)
Magick::NoInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::NoValue = T.let(T.unsafe(nil), Magick::GeometryFlags)

class Magick::NoiseType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::NoneDispose = T.let(T.unsafe(nil), Magick::DisposeType)
Magick::NormalStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::NormalStyle = T.let(T.unsafe(nil), Magick::StyleType)
Magick::NormalWeight = T.let(T.unsafe(nil), Magick::WeightType)
Magick::NormalizeValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::NormalizedCrossCorrelationErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::NorthEastGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::NorthGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::NorthWestGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::OHTAColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::ObliqueStyle = T.let(T.unsafe(nil), Magick::StyleType)
Magick::OctagonKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::OctagonalKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::OffAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::OilPaintPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::OnAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::OpacityChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::OpaqueAlpha = T.let(T.unsafe(nil), Integer)
Magick::OpaqueAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::OpenIntensityMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::OpenMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::OptimizeImageLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::OptimizeLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::OptimizePlusLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::OptimizeTransLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::OptimizeType = T.let(T.unsafe(nil), Magick::ImageType)

# Collects non-specific optional method arguments
#
# source://rmagick//lib/rmagick_internal.rb#1825
class Magick::OptionalMethodArguments
  # @return [OptionalMethodArguments] a new instance of OptionalMethodArguments
  #
  # source://rmagick//lib/rmagick_internal.rb#1826
  def initialize(img); end

  # set(key, val) corresponds to -set option:key val
  #
  # source://rmagick//lib/rmagick_internal.rb#1836
  def define(key, val = T.unsafe(nil)); end

  # accepts Pixel object or color name
  #
  # source://rmagick//lib/rmagick_internal.rb#1841
  def highlight_color=(color); end

  # accepts Pixel object or color name
  #
  # source://rmagick//lib/rmagick_internal.rb#1847
  def lowlight_color=(color); end

  # miscellaneous options like -verbose
  #
  # source://rmagick//lib/rmagick_internal.rb#1831
  def method_missing(mth, val); end
end

Magick::OrQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)

class Magick::OrientationType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::OutCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::OverCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::OverlayCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::OverlineDecoration = T.let(T.unsafe(nil), Magick::DecorationType)
Magick::PNGInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::PadSpread = T.let(T.unsafe(nil), Magick::SpreadMethod)

class Magick::PaintMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::PaletteAlphaType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::PaletteBilevelAlphaType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::PaletteType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::PartitionInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::ParzenFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::PeakAbsoluteErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::PeakSignalToNoiseRatioErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::PeaksKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::PegtopLightCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

# source://rmagick//lib/rmagick_internal.rb#68
Magick::PercentGeometry = T.let(T.unsafe(nil), Magick::GeometryValue)

Magick::PercentValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::PerceptualHashErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::PerceptualIntent = T.let(T.unsafe(nil), Magick::RenderingIntent)
Magick::PerspectiveDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::PerspectiveProjectionDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::PinLightCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

# Magick::ImageList
#
# source://rmagick//lib/rmagick_internal.rb#1819
class Magick::Pixel
  include ::Comparable
  include ::Observable

  def initialize(*_arg0); end

  def <=>(_arg0); end
  def ===(_arg0); end
  def alpha; end
  def alpha=(_arg0); end
  def black; end
  def black=(_arg0); end
  def blue; end
  def blue=(_arg0); end
  def clone; end
  def cyan; end
  def cyan=(_arg0); end
  def dup; end
  def eql?(_arg0); end
  def fcmp(*_arg0); end
  def green; end
  def green=(_arg0); end
  def hash; end
  def intensity; end
  def magenta; end
  def magenta=(_arg0); end
  def marshal_dump; end
  def marshal_load(_arg0); end
  def red; end
  def red=(_arg0); end
  def to_color(*_arg0); end
  def to_hsla; end
  def to_s; end
  def yellow; end
  def yellow=(_arg0); end

  private

  def initialize_copy(_arg0); end

  class << self
    def from_color(_arg0); end
    def from_hsla(*_arg0); end
  end
end

class Magick::PixelInterpolateMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::PixelsPerCentimeterResolution = T.let(T.unsafe(nil), Magick::ResolutionType)
Magick::PixelsPerInchResolution = T.let(T.unsafe(nil), Magick::ResolutionType)
Magick::PizCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::Plane2CylinderDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::PlaneInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::PlusCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::PlusKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)

class Magick::Point < ::Struct
  def x; end
  def x=(_); end
  def y; end
  def y=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::PointFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::PointMethod = T.let(T.unsafe(nil), Magick::PaintMethod)
Magick::PoissonNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::PoissonNoiseQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::PolarDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::PolynomialDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::PolynomialFunction = T.let(T.unsafe(nil), Magick::MagickFunction)
Magick::PowQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)

class Magick::PreviewType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::PreviousDispose = T.let(T.unsafe(nil), Magick::DisposeType)
Magick::PrewittKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)

class Magick::Primary < ::Struct
  def to_s; end
  def x; end
  def x=(_); end
  def y; end
  def y=(_); end
  def z; end
  def z=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::PseudoClass = T.let(T.unsafe(nil), Magick::ClassType)
Magick::PsiNegative = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::PsiValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::Pxr24Compression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::QuadraticFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::QuantizePreview = T.let(T.unsafe(nil), Magick::PreviewType)

class Magick::QuantumExpressionOperator < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::QuantumPixel = T.let(T.unsafe(nil), Magick::StorageType)
Magick::QuantumRange = T.let(T.unsafe(nil), Integer)
Magick::RGBChannels = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::RGBColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::RLECompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::RShiftQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::RaisePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::RandomNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::RandomVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::Rec601YCbCrColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::Rec709YCbCrColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)

class Magick::Rectangle < ::Struct
  def height; end
  def height=(_); end
  def to_s; end
  def width; end
  def width=(_); end
  def x; end
  def x=(_); end
  def y; end
  def y=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::RectangleKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::RedChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::ReduceNoisePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ReflectSpread = T.let(T.unsafe(nil), Magick::SpreadMethod)
Magick::RelativeIntent = T.let(T.unsafe(nil), Magick::RenderingIntent)
Magick::RemoveAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::RemoveDupsLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::RemoveZeroLayer = T.let(T.unsafe(nil), Magick::LayerMethod)

class Magick::RenderingIntent < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::RepeatSpread = T.let(T.unsafe(nil), Magick::SpreadMethod)
Magick::ReplaceCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ReplaceMethod = T.let(T.unsafe(nil), Magick::PaintMethod)
Magick::ResetMethod = T.let(T.unsafe(nil), Magick::PaintMethod)
Magick::ResizeDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)

class Magick::ResolutionType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::RhoValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::RidgesKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::RiemersmaDitherMethod = T.let(T.unsafe(nil), Magick::DitherMethod)
Magick::RightAlign = T.let(T.unsafe(nil), Magick::AlignType)
Magick::RightBottomOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::RightTopOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::RingKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::RobertsKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::RobidouxFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::RobidouxSharpFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::RollPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::RootMeanSquareQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::RootMeanSquaredErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::RotatePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::SRGBColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::SVGCompliance = T.let(T.unsafe(nil), Magick::ComplianceType)
Magick::SaturateCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::SaturationChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::SaturationIntent = T.let(T.unsafe(nil), Magick::RenderingIntent)
Magick::SaturationPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ScRGBColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::ScaleRotateTranslateDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::ScreenCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)

class Magick::Segment < ::Struct
  def to_s; end
  def x1; end
  def x1=(_); end
  def x2; end
  def x2=(_); end
  def y1; end
  def y1=(_); end
  def y2; end
  def y2=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::SegmentPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::SemiCondensedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::SemiExpandedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::SentinelDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::SeparatorValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::SetAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::SetQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::ShadePreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ShapeAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::SharpenPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ShearPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ShepardsColorInterpolate = T.let(T.unsafe(nil), Magick::SparseColorMethod)
Magick::ShepardsDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::ShortPixel = T.let(T.unsafe(nil), Magick::StorageType)
Magick::SigmaValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::SincFastFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::SincFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::SineQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::SinusoidFunction = T.let(T.unsafe(nil), Magick::MagickFunction)
Magick::SkeletonKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::SmoothMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::SobelKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::SoftLightCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::SolarizePreview = T.let(T.unsafe(nil), Magick::PreviewType)

# Fill class with solid monochromatic color
#
# source://rmagick//lib/rmagick_internal.rb#1878
class Magick::SolidFill
  # @return [SolidFill] a new instance of SolidFill
  #
  # source://rmagick//lib/rmagick_internal.rb#1879
  def initialize(bgcolor); end

  # source://rmagick//lib/rmagick_internal.rb#1883
  def fill(img); end
end

Magick::SouthEastGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::SouthGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::SouthWestGravity = T.let(T.unsafe(nil), Magick::GravityType)

class Magick::SparseColorMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::SpiffPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::SplineFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::SplineInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)

class Magick::SpreadMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::SpreadPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::SquareKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::SrcAtopCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::SrcCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::SrcInCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::SrcOutCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::SrcOverCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::StartAnchor = T.let(T.unsafe(nil), Magick::AnchorType)

class Magick::StorageType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

class Magick::StretchType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

class Magick::StyleType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::SubtractQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::SumQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::SwirlPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::SyncChannels = T.let(T.unsafe(nil), Magick::ChannelType)

class Magick::TextureFill
  def initialize(_arg0); end

  def fill(_arg0); end
end

Magick::ThickenMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::ThinSEKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::ThinningMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::ThresholdBlackQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::ThresholdCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::ThresholdPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::ThresholdQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::ThresholdWhiteQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::TileVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::TopHatMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::TopLeftOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::TopRightOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::TransparentAlpha = T.let(T.unsafe(nil), Integer)
Magick::TransparentAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::TransparentColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::TransparentVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::TriangleFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::TrimBoundsLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::TrueAlphaChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::TrueColorAlphaType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::TrueColorType = T.let(T.unsafe(nil), Magick::ImageType)

class Magick::TypeMetric < ::Struct
  def ascent; end
  def ascent=(_); end
  def bounds; end
  def bounds=(_); end
  def descent; end
  def descent=(_); end
  def height; end
  def height=(_); end
  def max_advance; end
  def max_advance=(_); end
  def pixels_per_em; end
  def pixels_per_em=(_); end
  def to_s; end
  def underline_position; end
  def underline_position=(_); end
  def underline_thickness; end
  def underline_thickness=(_); end
  def width; end
  def width=(_); end

  class << self
    def [](*_arg0); end
    def inspect; end
    def keyword_init?; end
    def members; end
    def new(*_arg0); end
  end
end

Magick::UltraCondensedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::UltraExpandedStretch = T.let(T.unsafe(nil), Magick::StretchType)
Magick::UndefinedAlign = T.let(T.unsafe(nil), Magick::AlignType)
Magick::UndefinedAlphaChannel = T.let(T.unsafe(nil), Magick::AlphaChannelOption)
Magick::UndefinedChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::UndefinedClass = T.let(T.unsafe(nil), Magick::ClassType)
Magick::UndefinedColorInterpolate = T.let(T.unsafe(nil), Magick::SparseColorMethod)
Magick::UndefinedColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::UndefinedCompliance = T.let(T.unsafe(nil), Magick::ComplianceType)
Magick::UndefinedCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::UndefinedCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::UndefinedDispose = T.let(T.unsafe(nil), Magick::DisposeType)
Magick::UndefinedDistortion = T.let(T.unsafe(nil), Magick::DistortMethod)
Magick::UndefinedDitherMethod = T.let(T.unsafe(nil), Magick::DitherMethod)
Magick::UndefinedEndian = T.let(T.unsafe(nil), Magick::EndianType)
Magick::UndefinedErrorMetric = T.let(T.unsafe(nil), Magick::MetricType)
Magick::UndefinedFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::UndefinedFunction = T.let(T.unsafe(nil), Magick::MagickFunction)
Magick::UndefinedGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::UndefinedIntent = T.let(T.unsafe(nil), Magick::RenderingIntent)
Magick::UndefinedInterlace = T.let(T.unsafe(nil), Magick::InterlaceType)
Magick::UndefinedInterpolatePixel = T.let(T.unsafe(nil), Magick::PixelInterpolateMethod)
Magick::UndefinedKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::UndefinedLayer = T.let(T.unsafe(nil), Magick::LayerMethod)
Magick::UndefinedMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::UndefinedOrientation = T.let(T.unsafe(nil), Magick::OrientationType)
Magick::UndefinedPixel = T.let(T.unsafe(nil), Magick::StorageType)
Magick::UndefinedPreview = T.let(T.unsafe(nil), Magick::PreviewType)
Magick::UndefinedQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::UndefinedResolution = T.let(T.unsafe(nil), Magick::ResolutionType)
Magick::UndefinedSpread = T.let(T.unsafe(nil), Magick::SpreadMethod)
Magick::UndefinedType = T.let(T.unsafe(nil), Magick::ImageType)
Magick::UndefinedVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::UnderlineDecoration = T.let(T.unsafe(nil), Magick::DecorationType)
Magick::UniformNoise = T.let(T.unsafe(nil), Magick::NoiseType)
Magick::UniformNoiseQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::UnityKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::UnrecognizedDispose = T.let(T.unsafe(nil), Magick::DisposeType)
Magick::UserDefinedKernel = T.let(T.unsafe(nil), Magick::KernelInfoType)
Magick::Version = T.let(T.unsafe(nil), String)
Magick::VerticalTileEdgeVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::VerticalTileVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)

class Magick::VirtualPixelMethod < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::VividLightCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::VoronoiColorInterpolate = T.let(T.unsafe(nil), Magick::SparseColorMethod)
Magick::VoronoiMorphology = T.let(T.unsafe(nil), Magick::MorphologyMethod)
Magick::WavePreview = T.let(T.unsafe(nil), Magick::PreviewType)

class Magick::WeightType < ::Magick::Enum
  def initialize(_arg0, _arg1); end

  def inspect; end

  class << self
    def values; end
  end
end

Magick::WelchFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::WelshFilter = T.let(T.unsafe(nil), Magick::FilterType)
Magick::WestGravity = T.let(T.unsafe(nil), Magick::GravityType)
Magick::WhiteVirtualPixelMethod = T.let(T.unsafe(nil), Magick::VirtualPixelMethod)
Magick::WidthValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::X11Compliance = T.let(T.unsafe(nil), Magick::ComplianceType)
Magick::XNegative = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::XPMCompliance = T.let(T.unsafe(nil), Magick::ComplianceType)
Magick::XValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::XYZColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::XiNegative = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::XiValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::XorCompositeOp = T.let(T.unsafe(nil), Magick::CompositeOperator)
Magick::XorQuantumOperator = T.let(T.unsafe(nil), Magick::QuantumExpressionOperator)
Magick::XyYColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YCCColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YCbCrColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YDbDrColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YIQColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YNegative = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::YPbPrColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YUVColorspace = T.let(T.unsafe(nil), Magick::ColorspaceType)
Magick::YValue = T.let(T.unsafe(nil), Magick::GeometryFlags)
Magick::YellowChannel = T.let(T.unsafe(nil), Magick::ChannelType)
Magick::ZipCompression = T.let(T.unsafe(nil), Magick::CompressionType)
Magick::ZipSCompression = T.let(T.unsafe(nil), Magick::CompressionType)
