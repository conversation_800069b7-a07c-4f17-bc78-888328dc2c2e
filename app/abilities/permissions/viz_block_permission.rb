# frozen_string_literal: true

# typed: false
module Permissions::VizBlockPermission
  include Permissions::BasePermission

  define_permission :viz_block_permission do |user|

    if user.admin?
      can :read, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard)
      end

      can :explore, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard)
      end
    end

    if user.analyst?
      can :read, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard) && can?(:read, vb.data_set)
      end

      can :explore, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard) && can?(:read, vb.data_set)
      end
    end

    if FeatureToggle.active?(::EmbedLink::FT_DATA_EXPLORE, user.tenant_id) && user.embed_user?
      can :explore, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard) && can?(:read, vb.data_set)
      end
    end

    if user.explorer? || user.viewer?
      can :explore, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard) && can?(:read, vb.data_set)
      end
    end

    if user.explorer? || user.viewer? || user.business_user? || user.public_user?
      # Explorer, Business user, viewer, public user can read viz blocks on a shared dashboard
      can :read, DashboardsV4::Materialized::VizBlock do |vb|
        can?(:read, vb.dashboard)
      end
    end


    export_permission(user, DashboardsV4::Materialized::VizBlock)
  end
end

