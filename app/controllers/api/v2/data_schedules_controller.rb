# typed: true
# frozen_string_literal: true

module Api::V2
  class DataSchedulesController < ApiController
    include ObjectUsageHandler
    include TypeIdSerializer

    before_action :check_object_exceed_limit, only: %i[create update]

    ALLOWED_SOURCES = %w[QueryReport Dashboard MetricSheet].freeze

    def index
      authorize! :list, EmailSchedule

      ds_index_query = EmailSchedules::IndexQuery.new(
        current_tenant.id,
        dest_type: params[:dest_type],
        source_type: params[:source_type],
        source_id: params[:source_id],
        before: pagination_params[:before],
        after: pagination_params[:after],
        limit: pagination_params[:limit],
      )

      list = ds_index_query.execute
      cursors = ds_index_query.build_cursors(list)

      list = permission_filter(list, :read)
      serialized = list.map do |ds|
        Api::V2::DataScheduleSerializer.new(
          ds,
          params: { current_user: current_user, current_ability: current_ability },
        ).as_json
      end

      render_json(data_schedules: serialized, cursors: cursors)
    end

    def show
      ds = EmailSchedule.find(params.require(:id))
      authorize! :read, ds
      serialized_es = Api::V2::DataScheduleSerializer.new(
        ds,
        params: {
          current_user: current_user,
          current_ability: current_ability,
        },
      ).as_json
      render_json(data_schedule: serialized_es)
    end

    def create
      authorize! :create, EmailSchedule
      ds_params = data_schedule_params

      authorize_source_permission(ds_params)
      data_schedule =
        DataDelivery::DelivererBuilders::DataSchedule.new(
          params: ds_params,
          schedule_params: ds_params[:schedule],
          current_user: current_user,
          current_ability: current_ability,
        ).create

      data_schedule.create_activity(
        {
          action: :create,
          parameters: ds_params.to_unsafe_h,
        }.merge(public_activity_params),
      )

      serialized = Api::V2::DataScheduleSerializer.new(
        data_schedule,
        params: {
          current_user: current_user,
          current_ability: current_ability,
        },
      ).as_json
      render_json({ data_schedule: serialized }, status: :created)
    end
    rate_limit(
      create: {
        rate: 'level2',
      },
    )

    def update
      ds_params = data_schedule_params

      ds = EmailSchedule.find(params.require(:id))
      authorize! :update, ds
      data_schedule =
        DataDelivery::DelivererBuilders::DataSchedule.new(
          params: ds_params,
          schedule_params: ds_params[:schedule],
          current_user: current_user,
          current_ability: current_ability,
        ).update(ds)

      data_schedule.create_activity(
        {
          action: :update,
          parameters: ds_params.to_unsafe_h,
        }.merge(public_activity_params),
      )

      serialized = Api::V2::DataScheduleSerializer.new(
        data_schedule,
        params: {
          current_user: current_user,
          current_ability: current_ability,
        },
      ).as_json
      render_json(data_schedule: serialized)
    end

    def destroy
      ds = EmailSchedule.find(params.require(:id))
      authorize! :destroy, ds

      ds.create_activity(
        :destroy,
        public_activity_params,
      )

      ds.destroy!
      render_json(message: 'success')
    end

    def submit_execute
      job =
        if params[:test_data_schedule]
          ds_params = data_schedule_params(:test_data_schedule)
          origin_ds = EmailSchedule.find_by(id: ds_params[:id]) if ds_params[:id]

          authorize! :execute, origin_ds  if origin_ds

          ds_params[:test_only] = true
          test_ds = DataDelivery::DelivererBuilders::DataSchedule.new(
            params: ds_params,
            schedule_params: ds_params[:schedule],
            current_user: current_user,
            current_ability: current_ability,
          ).create_test(origin_ds)

          authorize! :execute, test_ds

          DataSchedules::TestExecution.execute(test_ds, current_user)
        else
          ds = EmailSchedule.find(params.require(:id))
          authorize! :execute, ds

          async_options = Notifications.email_schedule_failure_notif(ds).merge(
            user_id: current_user.id,
            self_cache: true,
            worker_options: {
              queue: Job.worker_queue_for_schedules(ds.tenant_id),
            },
          )
          ds.async(async_options).execute
        end

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    private

    def data_schedule_params(attribute_name = :data_schedule)
      strong_params_for(params, attribute_name, keys: %i[source_id source_type],
                                                json_keys: %i[dest schedule dynamic_filter_presets],)
    end

    def authorize_source_permission(params)
      source_type = params[:source_type]
      source_id = params[:source_id]

      raise Holistics::InvalidRequest, "Source #{source_type} not allowed" unless source_type.in?(ALLOWED_SOURCES)

      source = typeid_to_object(source_type, source_id)
      authorize! :read, source

      source
    end
  end
end
