# frozen_string_literal: true
# typed: true

module Api::V2
  class DashboardsController < ApiController
    include Concerns::EmbedPortalHelper

    MAX_OPENAI_PROMPT_SIZE = 1024 * 1024 # 1MB

    def show
      dashboard = Dashboard.find(params.require(:id))
      authorize! :read, dashboard

      include_embed_info = params[:include_embed_info].is_true?
      if include_embed_info && !current_ability.can?(:list, EmbedLink)
        raise Holistics::PermissionDenied,
              'You are not allowed to list embed info. Please disable `include_embed_info`.'
      end

      include_pin_status = FeatureToggle.active?(Dashboard::FT_PIN_DASHBOARD, current_user.tenant_id) &&
                           params[:include_pin_status]

      serialized = Api::V2::DashboardSerializer.new(
        dashboard,
        params: {
          ability: current_ability,
          include_embed_info: include_embed_info,
          include_pin_status: include_pin_status,
          include_project_info: true,
          include_render_options: true,
        },
      ).as_json

      dynamic_filters = dashboard_filters(dashboard)[:dynamic_filters]

      track_view_dashboard(dashboard)

      serialized_dashboard = serialized.merge(dynamic_filters: dynamic_filters)

      render_json(dashboard: serialized_dashboard)
    end

    def destroy
      dashboard = Dashboard.find(params.require(:id))
      authorize! :destroy, dashboard

      dashboard.destroy!
      render_json(message: 'success')
    end

    def index
      authorize! :list, Dashboard

      index_query = ::Dashboards::IndexQuery.coerce_from(
        tenant_id: current_user.tenant_id,
        **pagination_params,
      )

      dashboards, cursors = index_query.execute
      dashboards = permission_filter(dashboards, :read)

      include_embed_info = params[:include_embed_info].is_true?
      if include_embed_info && !current_ability.can?(:list, EmbedLink)
        raise Holistics::PermissionDenied,
              'You are not allowed to list embed info. Please disable `include_embed_info`.'
      end

      preload_dashboard!(dashboards, include_embed_info)

      serialized_dashboards = dashboards.map do |dashboard|
        Api::V2::DashboardSerializer.new(
          dashboard,
          params: { current_user: current_user,
                    ability: current_ability,
                    include_widgets: true,
                    include_url: true,
                    include_embed_info: include_embed_info, },
        ).as_json
      end

      render_json(dashboards: serialized_dashboards, cursors: cursors)
    end

    def submit_create_canvas_dashboard
      category_id = params.require(:category_id)
      category =
        if category_id.zero?
          ReportCategory.root_category(current_user: current_user)
        else
          ReportCategory.find_by!(id: category_id, tenant_id: current_tenant.id)
        end
      authorize! :create_item, category

      project = ::AmlStudio::Project.find_by!(id: params.require(:project_id), tenant_id: current_tenant.id)
      unless project.edit_on_production_enabled?
        raise Holistics::PermissionDenied, 'This project does not enable edit on production'
      end

      datasets = params[:datasets].map { |ds| ds.permit!.to_h }
      definition = params.require(:definition).permit!.to_h

      ::AmlStudio::UpdateReportingObject::DashboardPayloadValidation
        .authorize_creation!(current_user, definition, datasets)

      async_options = {
        user_id: current_user.id,
        tenant_id: current_user.tenant_id,
        include_job_id: true,
        merge_duplicate: true,
        self_cache: false,
      }
      job = ::AmlStudio::UpdateReportingObject::CreateDashboard.new(
        user: current_user,
        project: project,
        definition: definition,
        datasets: datasets,
        category_id: category_id,
      ).async(async_options).call

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    def submit_update_canvas_dashboard
      dashboard = Dashboard.find_by!(id: params.require(:id))
      authorize! :live_update, dashboard

      dashboard_update_events = params.require(:dashboard_update_events).map do |event|
        event.permit!.to_h
      end
      datasets = params[:datasets].map { |ds| ds.permit!.to_h }

      ::AmlStudio::UpdateReportingObject::DashboardPayloadValidation
        .authorize_update!(current_user, dashboard, dashboard_update_events, datasets)

      async_options = {
        user_id: current_user.id,
        tenant_id: current_user.tenant_id,
        include_job_id: true,
        merge_duplicate: true,
        self_cache: false,
      }
      job = ::AmlStudio::UpdateReportingObject::UpdateDashboard.new(
        user: current_user,
        dashboard: dashboard,
        dashboard_update_events: dashboard_update_events,
        datasets: datasets,
        from_commit_hash: params[:repo_version],
      ).async(async_options).call

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    def submit_delete_canvas_dashboards
      dashboards = Dashboard.where(id: params.require(:ids)).to_a
      dashboards.each do |dashboard|
        authorize! :live_update, dashboard
        authorize! :destroy, dashboard
      end

      async_options = {
        user_id: current_user.id,
        tenant_id: current_user.tenant_id,
        include_job_id: true,
        merge_duplicate: true,
        self_cache: false,
      }

      job = Dashboard.async(async_options).delete_dashboards(dashboards)

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    def dashboard_stats
      dashboard = Dashboard.find_by!(id: params.require(:id))

      authorize! :read_stats, dashboard

      frequent_viewers = dashboard.fetch_frequent_viewers
      serialized_dashboard_stats = Api::V2::DashboardStatsSerializer.new(dashboard,
                                                                         params: { frequent_viewers: frequent_viewers },).as_json
      render_json(dashboard_stats: serialized_dashboard_stats)
    end

    # @tag #visualization/soft_preview
    def grid_preview
      dashboard = Dashboard.find(params.required(:id))
      authorize! :read, dashboard

      serialized_dashboard = Api::V2::DashboardGridPreviewSerializer.new(dashboard).as_json
      render_json(dashboard_grid_preview: serialized_dashboard)
    end

    def build_url
      dashboard = Dashboard.find_by!(id: params.require(:id))
      authorize! :read, dashboard

      # construct and persist new filter states
      filter_states = params[:filter_states].map(&:to_unsafe_h)
      state = build_dynamic_filter_state(dashboard, filter_states)

      url = Filterables::GenerateUrl.new(
        filterable: dashboard,
      ).call({ DynamicFilterState::URL_PARAM => state&.hashid }.compact)

      render_json(fstate_hash: state&.hashid, dashboard_url: url)
    end

    def submit_preload
      dashboard = Dashboard.find_by!(id: params.required(:id))
      authorize! :bust_cache, dashboard if params[:bust_cache]
      authorize! :read, dashboard

      async_options = {
        user_id: current_user.id,
        include_job_id: true,
        tag: public_queue_tag || 'prefetch',
        merge_duplicate: true,
        self_cache: true,
      }
      job = dashboard.async(async_options).preload(current_user.id, params)

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    def generate_canvas_dashboard
      raise Holistics::InvalidRequest unless FeatureToggle.active?(::Dashboard::FT_V4_AI_CODEGEN, current_tenant)

      authorize! :create, Dashboard

      generate_params = strong_params_for(params, :payload, json_keys: [:dataset, :dimensions, :measures])

      dataset_id = generate_params[:dataset][:id]
      if dataset_id.is_a?(Integer) && (dataset = DataSet.find_by!(id: dataset_id))
        authorize! :read, dataset
      end

      prompt_text = "Generate an informative dashboard using:
      - Dimensions: #{generate_params[:dimensions].join(', ')}
      - Measures: #{generate_params[:measures].join(', ')}
      "
      prompt = {
        dataset: generate_params[:dataset],
        prompt: prompt_text,
      }.to_json

      if prompt.bytesize > MAX_OPENAI_PROMPT_SIZE
        raise Holistics::AppError,
              "Prompt size is over limit of #{MAX_OPENAI_PROMPT_SIZE / 1024.0}KB"
      end

      service = NodejsUtils::OpenAiAssistant.new(
        access_key: ENV.fetch('OPENAI_ACCESS_TOKEN', ''),
        assistant_id: ENV.fetch('OPENAI_CANVAS_DASHBOARD_GENERATOR_ASSISTANT_ID', ''),
      )
      job = T.unsafe(service)
             .async(tenant_id: current_user.tenant_id, user_id: current_user.id, self_cache: false)
             .call(prompt)

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    def list_metadata
      authorize! :read, Dashboard

      permission_action = params[:permission_action] || :read
      filter_options = {
        version: params[:version],
      }.compact
      dashboard_ids = Dashboard.filter_permission_ids(current_user, permission_action, **filter_options)

      metadata = ActiveRecord::Services::RelationHelpers.pluck_to_hash(
        Dashboard.where(id: dashboard_ids),
        [:id, :title, :uname, :version, :project_id],
      )
      render_json(dashboards_metadata: metadata)
    end

    def create_dashboard_for_external_user
      embed_portal_configs = validate_embed_portal_flow!

      is_personal = !!params[:is_personal]
      project = ::AmlStudio::Project.find_by!(id: embed_portal_configs.project_id)

      datasets = params[:datasets].map { |ds| ds.permit!.to_h }
      definition = params.require(:definition).permit!.to_h

      ::AmlStudio::UpdateReportingObject::DashboardPayloadValidation
        .authorize_creation!(current_user, definition, datasets)

      dashboard = Dashboard.new(tenant_id: current_user.tenant_id, owner: current_user, project_id: project.id,
                                category_id: Dashboard::DEFAULT_EMBED_CATEGORY_ID,)
      dashboard.build_external_user_item(tenant: current_user.tenant, owner: current_user, is_personal: is_personal)
      dashboard.readonly!
      authorize! :create, dashboard

      async_options = {
        user_id: current_user.id,
        tenant_id: current_user.tenant_id,
        include_job_id: true,
        merge_duplicate: true,
        self_cache: false,
      }

      job = ::AmlStudio::UpdateReportingObject::CreateDashboard.new(
        user: current_user,
        project: project,
        definition: definition.merge(uname: "ugo_#{definition['uname']}"),
        datasets: datasets,
        category_id: ::Dashboard::DEFAULT_EMBED_CATEGORY_ID,
        folder_path: ::AmlStudio::Project.storage_path_for_ugo_dashboard(T.must(current_user.external_user),
                                                                         is_personal,),
        is_personal: is_personal,
      ).async(async_options).call

      serialized = Api::V2::AsyncResultSerializer.new(job).as_json
      render_json(serialized)
    end

    private

    sig { params(dashboards: T::Array[Dashboard], include_embed_info: Boolean).void }
    def preload_dashboard!(dashboards, include_embed_info)
      preload_associations = [:personal_item, { tenant: :tenant_domain }]
      preload_associations.push(:embed_links) if include_embed_info

      ActiveRecord::Associations::Preloader.new.preload(dashboards, preload_associations)
    end

    def track_view_dashboard(dashboard)
      should_count_user_view = auth_method == Api::V2::ApiController::AuthMethod::Session && params[:view_dashboard].is_true? && !current_user.public_user?
      dashboard.track_view(should_count_user_view)
    end

    def dashboard_filters(dashboard)
      fetch_filters_params = ActionController::Parameters.new(
        dynamic_filter_holdable_id: dashboard.id,
        dynamic_filter_holdable_type: 'Dashboard',
      )

      dynamic_filters_ability = DynamicFiltersAbility.new(current_user)
      upsert_ability_from_controller_params(dynamic_filters_ability)

      DynamicFiltersOperations::SubmitFetchFilters.new(
        fetch_filters_params,
        current_user,
        ability: dynamic_filters_ability,
      ).process
    end

    sig do
      params(
        dashboard: Dashboard,
        filter_states: T::Array[Hash],
      ).returns(T.nilable(DynamicFilterState))
    end
    def build_dynamic_filter_state(dashboard, filter_states)
      return nil if filter_states.blank?

      dynamic_filters_map = DynamicFilter.where(id: filter_states.map { |s| s[:dynamic_filter_id] })
                                         .pluck(:id, :dynamic_filter_holdable_id, :dynamic_filter_holdable_type)
                                         .to_h { |h| [h[0], h] }

      dynamic_filter_states = filter_states.map.with_index do |filter_state, _i|
        # validate dynamic filter belongs to current dashboard
        dynamic_filter = dynamic_filters_map[filter_state[:dynamic_filter_id]]

        if dynamic_filter[1] != dashboard.id || dynamic_filter[2] != 'Dashboard'
          raise Holistics::InvalidOperation, "Dynamic filter #{dynamic_filter[0]} does not belong to current dashboard."
        end

        filter_state.merge(selected_condition: filter_state[:condition]).except(:condition)
      end
      DynamicFilterState.create!(data: dynamic_filter_states, tenant: current_tenant)
    end

    rate_limit(
      index: {
        rate: 'level2',
      },
      submit_preload: {
        rate: 'level2',
      },
      generate_canvas_dashboard: {
        rate: 'level3',
      },
    )
  end
end
