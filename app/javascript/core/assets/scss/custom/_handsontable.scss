// Legacy Handsontable Styles

.pivot-table-wrapper, .data-table-wrapper {
  &.hide-table {
    .handsontable, .table-pagination .table-header {
      visibility: hidden;
    }
  }

  td {
    white-space: pre-wrap;
    word-break: break-word;
  }

  thead {
    td, th {
      height: $absolute-big-screen-cell-height !important;
    }
  }
  @media only screen and (max-width: 1440px) {
    thead {
      td, th {
        height: $absolute-small-screen-cell-height !important;
      }
    }
  }

  .single-line-row {
    td, th, .empty-cell {
      height: $absolute-big-screen-cell-height !important;
    }
    td, td span.cell-wrapper span.inner {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    td span.cell-wrapper span.inner  {
      max-width: calc(100% - 20px);
    }

    .conditional-format {
      // conditional format border make cell 1px higher. need to -1px to make it equal other cells
      height: $absolute-big-screen-cell-height - 1px !important;
    }
    @media only screen and (max-width: 1440px) {
      td, th, .empty-cell {
        height: $absolute-small-screen-cell-height !important;
      }
      .conditional-format {
        height: $absolute-small-screen-cell-height - 1px !important;
      }
    }
  }

  .has-highlighted-cells {
    td:not(.highlighted-cell) {
      opacity: 0.2;
    }
    .highlighted-cell:not(.remove-top) {
      box-shadow: 0 -1px 0 0 #DEDEDE; // fake border-top
    }
  }

  .has-cross-filter {
    .ht_master, .ht_clone_left {
      td {
        cursor: pointer;
      }
    }
  }

  .conditional-format {
    border: 1px solid #DEDEDE;
  }

  // probably handsontable bugs, clone bottom is shown even there is no fixed row bottom
  &.hidden-clone-bottom-table {
    .ht_clone_bottom.handsontable {
      visibility: hidden;
    }
  }
}

.retention-heatmap, .pivot-table, .data-table {
  &,
  *,
  *:before,
  *:after {
    box-sizing: content-box !important;
  }

  .htDimmed {
    color: $color-dark-2;
  }

  td, th {
    font-size: $font-size-xs;
    line-height: $line-height-xs !important;
    border-color: #DEDEDE !important;
    vertical-align: middle;
  }

  &:first-child tr {
    th {
      border-bottom: 1px solid #DEDEDE !important;
    }
    td {
      border-top: 0;
    }
  }

  .ht_clone_top, .ht_clone_left, .ht_clone_top_left_corner, .ht_clone_bottom_left_corner {
    z-index: 10;
  }

  // infinite scroll's CSS:
  // for loading cell, merge cell
  td {
    div.loading-cell {
      background: rgb(235,235,235);
      height: 13px;
      border-radius: $border-radius-xl;
      animation-name: shine;
      animation-duration: 2s;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
    }
    &.remove-bottom {
      border-bottom-width: 0px;
    }
    &.remove-top {
      border-top-width: 0px;
    }
    &.remove-left {
      border-left-width: 0px;
    }
    &.remove-right {
      border-right-width: 0px;
    }
  }

  // need to set these in both handsontable settings and css due to this bug https://github.com/handsontable/handsontable/issues/4454#issuecomment-519405266
  @media only screen and (max-width: 1440px) {
    td, th {
      font-size: $font-size-xxs;
      line-height: $line-height-xxs !important;
    }
  }
}


// DataTable css
.data-table-wrapper {
  .data-table {
    width: 100%;
    overflow: hidden;
    background-color: white;

    thead th .relative {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  thead th .relative {
    display: flex;
    justify-content: center;
    align-items: center;

    .colHeader {
      flex: 1;
    }
  }

  td span.top-row {
    font-style: italic;
  }

  /* All headers */  /* Row headers */
  .handsontable th {
    background-color: $color-white;
  }
  .ht_clone_left th {
    text-align: left;
    padding-left: $spacer * 2;
    background-color: $color-white;
  }

  /* Column headers */
  .ht_clone_top th {
    background-color: $bg-light-l2 !important;
    font-weight: $font-weight-bold;
  }

  /* Every even row, not highted one */
  .ht_master, .ht_clone_left {
    tr  {
      & > td,
      & > th:not(.ht__highlight) {
        background-color: white;
        border-bottom-color: white !important;
      }
    }
    tr:nth-of-type(even) {
      & > td,
      & > th:not(.ht__highlight) {
        background-color: $bg-light-l2;
        border-bottom-color: $bg-light-l2 !important;
      }
    }

    tr:last-child {
      & > td,
      & > th:not(.ht__highlight) {
        border-bottom-color: #DEDEDE !important;
      }
    }
  }

  .ht_clone_top_left_corner .wtHolder thead th {
    background-color: $bg-light-l2 !important;
  }

  // Ensure that content does not wrap with a new line, we want constant row height
  .htCore td {
    // white-space: nowrap;
    // text-overflow: ellipsis;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  .ht_master tr:hover > td {
    background-color: #efefef;
  }

  // total avg row
  .ht_clone_bottom td {
    border-bottom: 1px solid #DEDEDE !important;
  }
  .ht_clone_bottom_left_corner tr {
    th {
      text-align: left;
      padding-left: $spacer * 2;
      font-weight: $font-weight-bold;
    }

    &:not(:last-child) {
      th {
        border-bottom: 1px solid #DEDEDE !important;
      }
    }
  }

  .metric-sheet-table {
    .ht_clone_left.handsontable {
      tr {
        background: transparent;
        td.header-row {
          // hide row text + its right border
          border-right: 0px;
          opacity: 0;
        }
      }
      .wtBorder {
        // disable highlight border when select row header
        display: none !important;
      }
    }
    td.header-row {
      // make row header to be truncate
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}


// Pivot table css
.pivot-table-wrapper {
  .total-value, .sub-total-col-value, .h-sub-total-cell, .sub-total-field, .column-total, .column-field, .column-value, .measure-field, .row-field  {
    font-weight: $default-h-total-font-weight;
  }

  // column headers
  .column-field, .column-value {
    background: $bg-light-d1 !important;
    &:not(.remove-bottom) {
      border-bottom: 1px solid $color-dark-5 !important;
    }
    &:not(.remove-right) {
      border-right: 1px solid $color-dark-5 !important;
    }
  }

  // row headers
  .measure-field, .row-field {
    background: $bg-light-l2 !important;
  }

  .sub-total-field.row-value, .sub-total-row-value {
    background: $bg-light-l2;
    border-top: 1px solid #DEDEDE !important;
    border-bottom: 1px solid #DEDEDE !important;
  }
  .sub-total-field.column-value:not(.remove-right) {
    border-right: 1px solid $color-dark-5 !important;
  }
  .sub-total-field.row-value:not(.remove-right) {
    border-right: 1px solid $color-dark-5 !important;
  }

  // column value
  .column-value, .measure-field {
    text-align: center;
    &.total-field {
      vertical-align: center;
    }
  }

  // row-value
  .row-value, .measure-value {
    &.column-total {
      border-top: 1px solid $color-dark-5 !important;
      &.total-field:not(.remove-right) {
        border-right: 1px solid $color-dark-5 !important;
      }
    }
  }

  .ht_clone_left td.row-value[rowspan] {
    vertical-align: baseline;
    padding-top: $spacer;

    // hack to compensate for padding-top ^. https://holistics.slack.com/archives/C02DFANDBQV/p1657168789574779?thread_ts=1649896056.247839&cid=C02DFANDBQV
    height: $absolute-big-screen-cell-height - $spacer !important;
    @media only screen and (max-width: 1440px) {
      height: $absolute-small-screen-cell-height - $spacer !important;
    }
  }

  // zebra cell background for the closest category cells to value
  .ht_clone_left tr:nth-of-type(even) > td:last-child {
    background-color: $bg-light-l2;
  }

  .ht_master tr:nth-of-type(even) > td {
    background-color: $bg-light-l2;
  }

  .ht_master tr:hover > td {
    background-color: #efefef;
  }

  // all header group right border
  .ht_clone_top {
    tbody tr:first-child td:not(.remove-right) {
      border-right: 1px solid $color-dark-5 !important;
    }
  }
  .ht_clone_top_left_corner {
    td:not(.row-field):not(.remove-right), td.row-field:last-child {
      border-right: 1px solid $color-dark-5 !important;
    }
  }

  // center sortable row total column
  .ht_clone_top {
    table {
      .measure-field.sortable.custom-header {
        .cell-wrapper {
          justify-content: center;
        }
      }
    }
  }

  .highlight-bold-border-right:not(.remove-right) {
    border-right: 1px solid $color-dark-5 !important;
  }
  .highlight-bold-border-bottom:not(.remove-bottom) {
    border-bottom: 1px solid $color-dark-5 !important;
    &.row-value, &.measure-value{
      border-bottom: 1px solid #DEDEDE !important;
    }
  }
}

// Retention heatmap css
.retention-heatmap {
  .handsontable {
    // general style for all cells
    tr {
      &:not(:last-child) {
        th, td {
          border-bottom: 0;
        }
      }
    }

    .custom-header {
      font-weight: $font-weight-bold;
    }

    tr:nth-of-type(even) > td {
      background-color: $bg-light-l2;
    }

    tr > td:first-child {
      border-left: 1px solid #DEDEDE;
    }

    tr > td {
      border-right: 1px solid #DEDEDE !important;
    }

    tr > td:nth-child(2) {
      border-right: 1px solid $color-dark-5 !important;
    }

    tr > th {
      border-right: 1px solid #DEDEDE !important;
    }

    tr > th:first-child {
      border-right: 1px solid $color-dark-5;
      border-left: 1px solid #DEDEDE;
    }

    tr > th:nth-child(2) {
      border-right: 1px solid $color-dark-5 !important;
      border-left: none !important;
    }

    th:last-child {
      border-right: 1px solid #DEDEDE !important;
    }
  }
}

.metric-sheet-table {
  .ht_clone_top_left_corner {
    th:nth-child(1) {
      border-right: 0;
    }
  }

  tr:nth-of-type(even) > td {
    background-color: $bg-light-l2;
  }

  tr {
    & > td:nth-child(2),
    & > th:nth-child(2) {
      border-right: 1px solid $color-dark-5 !important;
    }
  }

  .table-header-cell {
    height: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    font-size: $font-size-xs;
    color: $color-dark-4;
  }

  .top-left-header {
    align-items: flex-start;
  }
}


// For conversion funnel
.conversion-funnel-table {
  width: 100%;
  max-width: 100%;
  @apply border;

  th {
    @apply font-semibold text-gray-800;
  }

  > thead,
  > tbody {
    > tr {
      > th,
      > td {
        height: $absolute-big-screen-cell-height !important;
        line-height: $line-height-xs;
        font-size: $font-size-xs;
        padding: 0 $table-cell-padding;
        color: $color-dark-2;
        vertical-align: middle;
      }

      @media only screen and (max-width: 1440px) {
        > th,
        > td {
          height: $absolute-small-screen-cell-height !important;
          line-height: $line-height-xxs;
          font-size: $font-size-xxs;
        }
      }
    }
  }

  > thead > tr {
    background-color: $bg-light-l2;

    > th {
      border-bottom: 1px solid #DEDEDE;
      border-right: 1px solid #DEDEDE;
    }
    > th:last-child {
      border-right: 0;
    }
  }

  > tbody {
    > tr {
      cursor: pointer;

      &.selected {
        @apply font-semibold;
      }
      &:hover {
        background-color: $table-bg-hover;
      }

      > td {
        border-top: 0;
        border-right: 1px solid #DEDEDE;
      }
      > td:last-child {
        border-left: 0;
      }
    }

    > tr:nth-of-type(odd) {
      background-color: white;
    }
    > tr:nth-of-type(even) {
      @apply bg-gray-100;
    }
  }
}