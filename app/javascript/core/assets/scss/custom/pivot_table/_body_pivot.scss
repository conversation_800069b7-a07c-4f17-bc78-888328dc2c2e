.h-pivot {
  .h-header-total-title-cell:not(
    .h-range-selection-cell,
    :focus
  ) {
    background: var(--ag-background-color, theme('colors.white')) !important;
  }

  .h-last-cell-in-row-group:not(.ag-row-last .ag-cell, .h-range-selection-cell) {
    @apply focus:border-b-blue-700;
    border-bottom-color: var(--h-table-grid-color, var(--h-table-border-color, $default-border-color));
    border-bottom-width: var(--h-table-row-border-width, var(--h-table-border-width, 1px));

    &.h-conditional-formatting-border {
      @apply focus:border-b-blue-700;
    }
  }

  .ag-row {
    &:not(.ag-row-last, .ag-row-pinned:last-child) {
      .ag-cell:not(
        .h-pivot-row-header-cell, // Exclude row headers handled in _row_header_pivot.scss
        .h-range-selection-cell,
        .h-last-cell-in-row-group,
        :focus,
        .h-conditional-formatting-border
      ) {
        border-bottom-color: var(--ag-background-color, theme('colors.white'));
      }
    }
  }
}
