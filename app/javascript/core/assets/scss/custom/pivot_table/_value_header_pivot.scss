.h-pivot {
  .ag-header {
    .ag-header-cell {
      min-height: calc(var(--ag-row-height) + var(--h-table-border-width, 1px)) !important;

      &:after {
        border-right: var(--ag-header-column-border);
      }
    }

    .ag-header-row {
      &.ag-header-row-column {
        /* Extra height comes from border-bottom of .ag-header */
        min-height: calc(var(--ag-row-height) + var(--h-table-border-width, 1px)) !important;
      }

      &:not(:first-child) {
        .ag-header-cell:not(.ag-header-span-height.ag-header-span-total, .ag-header-parent-hidden) {
          border-top: 0;
        }
      }
    }

    .h-pivot-value-header-cell {
      background: var(--h-table-header-bg-color, var(--h-table-fallback-header-bg-color, $default-header-bg-color));
      font-size: var(--ag-header-font-size, var(--ag-font-size));
      font-weight: var(--ag-header-font-weight);
      color: var(--ag-header-text-color, var(--ag-text-color));
    }
  }
}
