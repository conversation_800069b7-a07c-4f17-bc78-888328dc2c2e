.h-pivot {
  .col-header {
    .pivot-column-fields-and-row-fields-header {
      @apply justify-start;
    }

    .pivot-column-values-and-measure-fields-header {
      @apply justify-center;
    }
  }

  .ag-header {
    .ag-header-row {
      &:not(:first-child) {
        .ag-header-group-cell.ag-header-group-cell-with-group {
          border-top: 0;
        }
      }
    }

    // Specific styles for column headers
    .h-pivot-column-header-cell {
      --ag-header-cell-hover-background-color: transparent; // Disable hover effect

      &.h-pivot-column-header-value-cell { // Group headers for column values
        background: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-col-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-col-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        var(--h-table-fallback-sub-header-bg-color, $default-sub-header-bg-color)))))));
          font-size: var(--h-specific-table-sub-header-font-size,
              var(--h-specific-table-col-header-font-size,
                var(--h-specific-fallback-table-sub-header-font-size,
                  var(--h-dashboard-table-sub-header-font-size,
                    var(--h-dashboard-table-col-header-font-size,
                      var(--h-dashboard-fallback-table-sub-header-font-size,
                        var(--h-table-sub-header-font-size, var(--ag-font-size, 12px))))))));
          font-weight: var(--h-specific-table-sub-header-font-weight,
              var(--h-specific-table-col-header-font-weight,
                var(--h-specific-fallback-table-sub-header-font-weight,
                  var(--h-dashboard-table-sub-header-font-weight,
                    var(--h-dashboard-table-col-header-font-weight,
                      var(--h-dashboard-fallback-table-sub-header-font-weight,
                        var(--h-table-sub-header-font-weight, theme('fontWeight.semibold'))))))));
          color: var(--h-specific-table-sub-header-font-color,
              var(--h-specific-table-col-header-font-color,
                var(--h-specific-fallback-table-sub-header-font-color,
                  var(--h-dashboard-table-sub-header-font-color,
                    var(--h-dashboard-table-col-header-font-color,
                      var(--h-dashboard-fallback-table-sub-header-font-color,
                        var(--h-table-sub-header-font-color, var(--ag-header-text-color, theme('colors.gray.800')))))))));
      }

      &.h-pivot-column-header-field-cell { // Group headers for column fields (measure names)
        background: var(--h-specific-table-header-bg-color,
              var(--h-specific-table-col-header-bg-color,
                var(--h-specific-fallback-table-header-bg-color,
                  var(--h-dashboard-table-header-bg-color,
                    var(--h-dashboard-table-col-header-bg-color,
                      var(--h-dashboard-fallback-table-header-bg-color,
                        var(--h-table-fallback-header-bg-color, $default-header-bg-color)))))));
          font-size: var(--h-specific-table-header-font-size,
              var(--h-specific-table-col-header-font-size,
                var(--h-specific-fallback-table-header-font-size,
                  var(--h-dashboard-table-header-font-size,
                    var(--h-dashboard-table-col-header-font-size,
                      var(--h-dashboard-fallback-table-header-font-size,
                        var(--h-table-header-font-size, var(--ag-font-size, 12px))))))));
          font-weight: var(--h-specific-table-header-font-weight,
              var(--h-specific-table-col-header-font-weight,
                var(--h-specific-fallback-table-header-font-weight,
                  var(--h-dashboard-table-header-font-weight,
                    var(--h-dashboard-table-col-header-font-weight,
                      var(--h-dashboard-fallback-table-header-font-weight,
                        var(--h-table-header-font-weight, theme('fontWeight.bold'))))))));
          color: var(--h-specific-table-header-font-color,
              var(--h-specific-table-col-header-font-color,
                var(--h-specific-fallback-table-header-font-color,
                  var(--h-dashboard-table-header-font-color,
                    var(--h-dashboard-table-col-header-font-color,
                      var(--h-dashboard-fallback-table-header-font-color,
                        var(--h-table-header-font-color, var(--ag-header-text-color, theme('colors.gray.800')))))))));
      }

      // Total column group headers
      &.h-header-total-column-group, &.h-header-total-column-group-last {
        background: var(--h-specific-table-header-bg-color,
              var(--h-specific-table-col-header-bg-color,
                var(--h-specific-fallback-table-header-bg-color,
                  var(--h-dashboard-table-header-bg-color,
                    var(--h-dashboard-table-col-header-bg-color,
                      var(--h-dashboard-fallback-table-header-bg-color,
                        var(--h-table-fallback-header-bg-color, $default-header-bg-color)))))));
          font-size: var(--h-specific-table-header-font-size,
              var(--h-specific-table-col-header-font-size,
                var(--h-specific-fallback-table-header-font-size,
                  var(--h-dashboard-table-header-font-size,
                    var(--h-dashboard-table-col-header-font-size,
                      var(--h-dashboard-fallback-table-header-font-size,
                        var(--h-table-header-font-size, var(--ag-font-size, 12px))))))));
          font-weight: var(--h-specific-table-header-font-weight,
              var(--h-specific-table-col-header-font-weight,
                var(--h-specific-fallback-table-header-font-weight,
                  var(--h-dashboard-table-header-font-weight,
                    var(--h-dashboard-table-col-header-font-weight,
                      var(--h-dashboard-fallback-table-header-font-weight,
                        var(--h-table-header-font-weight, theme('fontWeight.bold'))))))));
          color: var(--h-specific-table-header-font-color,
              var(--h-specific-table-col-header-font-color,
                var(--h-specific-fallback-table-header-font-color,
                  var(--h-dashboard-table-header-font-color,
                    var(--h-dashboard-table-col-header-font-color,
                      var(--h-dashboard-fallback-table-header-font-color,
                        var(--h-table-header-font-color, var(--ag-header-text-color, theme('colors.gray.800')))))))));
      }
    }

    .ag-header-group-cell {
      &.ag-header-group-cell-with-group:not(.h-header-total-column-group) {
        @apply border-b;
        border-bottom-color: var(--h-table-header-row-border-color, var(--h-table-grid-color, var(--h-table-border-color, $default-border-color)));
        border-bottom-width: var(--h-table-header-row-border-width, var(--h-table-border-width, 1px));
      }

      &.h-header-total-column-group, &.h-header-total-column-group-last {
        @apply border-r-0;
      }
    }
  }
}
