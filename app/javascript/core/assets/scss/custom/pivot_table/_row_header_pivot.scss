.h-pivot {
  .ag-header {
    .h-pivot-row-header-cell {
      background: var(--h-specific-table-header-bg-color,
            var(--h-specific-table-row-header-bg-color,
              var(--h-specific-fallback-table-header-bg-color,
                var(--h-dashboard-table-header-bg-color,
                  var(--h-dashboard-table-row-header-bg-color,
                    var(--h-dashboard-fallback-table-header-bg-color,
                      $default-header-bg-color ))))));
        font-size: var(--h-specific-table-header-font-size,
            var(--h-specific-table-row-header-font-size,
              var(--h-specific-fallback-table-header-font-size,
                var(--h-dashboard-table-header-font-size,
                  var(--h-dashboard-table-row-header-font-size,
                    var(--h-dashboard-fallback-table-header-font-size,
                      var(--ag-header-font-size, var(--ag-font-size))))))));
        font-weight: var(--h-specific-table-header-font-weight,
            var(--h-specific-table-row-header-font-weight,
              var(--h-specific-fallback-table-header-font-weight,
                var(--h-dashboard-table-header-font-weight,
                  var(--h-dashboard-table-row-header-font-weight,
                    var(--h-dashboard-fallback-table-header-font-weight,
                      var(--ag-header-font-weight, theme('fontWeight.semibold'))))))));
        color: var(--h-specific-table-header-font-color,
            var(--h-specific-table-row-header-font-color,
              var(--h-specific-fallback-table-header-font-color,
                var(--h-dashboard-table-header-font-color,
                  var(--h-dashboard-table-row-header-font-color,
                    var(--h-dashboard-fallback-table-header-font-color,
                      var(--ag-header-text-color, var(--ag-text-color))))))));

      &::after {
        border-right-width: var(
          --h-table-row-header-border-width,
          var(
            --h-table-column-border-width,
            var(--h-table-border-width, 1px)
          )
        );
      }
    }
  }

  .ag-row {
    .h-pivot-row-header-cell:not(.h-range-selection-cell) {
      --ag-row-hover-color: var(
        --h-table-row-header-hover-color,
        var(--h-table-hover-color, #EDEDED)
      );
      font-size: var(--h-specific-table-sub-header-font-size,
            var(--h-specific-table-row-header-font-size,
              var(--h-specific-fallback-table-sub-header-font-size,
                var(--h-dashboard-table-sub-header-font-size,
                  var(--h-dashboard-table-row-header-font-size,
                    var(--h-dashboard-fallback-table-sub-header-font-size,
                      var(--ag-font-size)))))));
      font-weight: var(--h-specific-table-sub-header-font-weight,
            var(--h-specific-table-row-header-font-weight,
              var(--h-specific-fallback-table-sub-header-font-weight,
                var(--h-dashboard-table-sub-header-font-weight,
                  var(--h-dashboard-table-row-header-font-weight,
                    var(--h-dashboard-fallback-table-sub-header-font-weight,
                      theme('fontWeight.semibold')))))));
      color: var(--h-specific-table-sub-header-font-color,
            var(--h-specific-table-row-header-font-color,
              var(--h-specific-fallback-table-sub-header-font-color,
                var(--h-dashboard-table-sub-header-font-color,
                  var(--h-dashboard-table-row-header-font-color,
                    var(--h-dashboard-fallback-table-sub-header-font-color,
                      var(--ag-header-text-color, var(--ag-text-color))))))));

      border-right-width: var( // Right border for row header cells
        --h-table-row-header-border-width,
        var(
          --h-table-column-border-width,
          var(--h-table-border-width, 1px)
        )
      );

      &.h-sub-total-cell {
        font-weight: var(--h-table-row-header-font-weight, $default-h-total-font-weight);
      }
    }

    &:not(.ag-row-last, .ag-row-pinned:last-child) {
      .ag-cell.h-pivot-row-header-cell:not(
        .h-range-selection-cell,
        .h-last-cell-in-row-group,
        :focus,
        .h-conditional-formatting-border
      ) {
        border-bottom-color: var(--h-table-sub-header-bg-color, var(--h-table-row-header-bg-color, var(--h-table-fallback-sub-header-bg-color, $default-sub-header-bg-color)));
        border-bottom-color: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        $default-sub-header-bg-color ))))));
      }
    }

    &.ag-row-even {
      .h-pivot-row-header-cell:not(.h-range-selection-cell) {
        background: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        $default-sub-header-bg-color ))))));
      }
    }
    &.ag-row-odd {
      .h-pivot-row-header-cell.h-aggregated-cell:not(.h-range-selection-cell) {
        background: var(--h-specific-table-sub-header-bg-color,
              var(--h-specific-table-row-header-bg-color,
                var(--h-specific-fallback-table-sub-header-bg-color,
                  var(--h-dashboard-table-sub-header-bg-color,
                    var(--h-dashboard-table-row-header-bg-color,
                      var(--h-dashboard-fallback-table-sub-header-bg-color,
                        $default-sub-header-bg-color ))))));
      }
      .h-pivot-row-header-cell:not(.h-range-selection-cell ) {
        background: var(--h-table-sub-header-bg-color, var(--h-table-fallback-sub-header-bg-color, var(--ag-odd-row-background-color, $default-sub-header-bg-color)));
      }
    }

    &:has(.h-sub-total-cell) {
      .h-pivot-row-header-cell:not(.h-range-selection-cell) {
        @apply bg-white;
        background: var(--h-table-sub-header-bg-color, var(--h-table-row-header-bg-color, var(--h-table-fallback-sub-header-bg-color, $default-sub-header-bg-color)));
      }
    }
  }

  :not(.h-last-cell-in-column-group) {
    // Skip floating bottom container to prevent column total cells from inheriting row header styles
    &.h-following-span:not(.ag-floating-bottom-container .h-following-span) { // Cells hidden by rowspan
      @apply text-transparent;
      background: var(--h-specific-table-sub-header-bg-color,
            var(--h-specific-table-row-header-bg-color,
              var(--h-specific-fallback-table-sub-header-bg-color,
                var(--h-dashboard-table-sub-header-bg-color,
                  var(--h-dashboard-table-row-header-bg-color,
                    var(--h-dashboard-fallback-table-sub-header-bg-color,
                      $default-sub-header-bg-color ))))));

      &.h-range-selection-cell {
        @apply bg-blue-50;
      }

      // multiple lines
      .ag-cell-wrapper {
        @apply hidden;
      }
    }

    // Skip floating bottom container to prevent column total cells from inheriting row header styles
    &.h-start-span:not(.ag-floating-bottom-container .h-start-span) { // First cell in a rowspan
      color: var(ag-text-color, theme('colors.gray.800'));
      background: var(--h-specific-table-sub-header-bg-color,
            var(--h-specific-table-row-header-bg-color,
              var(--h-specific-fallback-table-sub-header-bg-color,
                var(--h-dashboard-table-sub-header-bg-color,
                  var(--h-dashboard-table-row-header-bg-color,
                    var(--h-dashboard-fallback-table-sub-header-bg-color,
                      $default-sub-header-bg-color ))))));

      &.h-range-selection-cell {
        @apply bg-blue-50 text-blue-700;
      }
    }
  }
}
