.h-metric-sheet {
  .ag-header {
    .ag-header-cell {
      background: var(--ag-header-background-color, theme('colors.gray.100'));

      .metric-sheet-title {
        line-height: normal;
      }

      .metric-sheet-sub-title {
        font-size: var(--h-table-subtitle-font-size, var(--ag-font-size, theme('fontSize.2xs')));
        color: var(--h-table-subtitle-font-color, var(--ag-text-color, theme('colors.gray.600')));
        font-weight: var(--h-table-subtitle-font-weight, theme('fontWeight.normal'));
        line-height: normal;
      }
    }
  }
}
