.h-table {
  @apply ag-standard;

  /* The last column should use table border color instead of grid border color*/
  .ag-cell {
    &.ag-column-last {
      // override the AG-Grid border style
      border-right-color: var(--ag-border-color, $default-border-color);
      border-right-width: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px));
    }
  }

  // Note: AG-Grid adds header border styling using pseudo-elements
  // instead of the actual border property
  .ag-header-cell {
    &.ag-column-last::after {
      --ag-header-column-border: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px)) solid var(--ag-border-color, $default-border-color);
    }
  }
  .ag-header-group-cell {
    &.ag-column-last:not(.ag-header-span-height.ag-header-group-cell-no-group)::after {
      --ag-header-column-border: var(--h-table-wrapper-border-width, var(--h-table-border-width, 1px)) solid var(--ag-border-color, $default-border-color);
    }
  }
}

// Component-specific imports placed last to ensure they cascade over general .ag-standard styles
@import 'header_data_table';
