// General styles that not AG-Grid or Handsontable specific

.h-table-header-dropdown {
  .selected-sort-option {
    @apply text-blue-500 font-medium;
  }
}

// let table display full (because no need to handle pagintaion bar anymore
.widget-viz-container, .widget-content {
  .hot-table-wrapper:not(.is-pptr) {
    flex: 1;
  }
}

// only show scroll when hovering dashboard widget
.widget-viz-container, .widget-content {
  .normal-web {
    .data-table-wrapper, .metric-sheet-table {
      .ht_master {
        .wtHolder {
          &::-webkit-scrollbar {
            display: none
          }
          scrollbar-width: none;
        }
      }
      .conversion-funnel {
        &::-webkit-scrollbar {
          display: none
        }
        scrollbar-width: none;
      }
      // when hovering
      &:hover {
        .ht_master {
          .wtHolder {
            &::-webkit-scrollbar {
              display: block;
            }
            scrollbar-width: auto;
          }
        }
        .conversion-funnel {
          &::-webkit-scrollbar {
            display: block;
          }
          scrollbar-width: auto;
        }
      }
    }
    // pivot has sticky row and resize table need more cost -> just make scrollbar transparent
    // .pivot-table-wrapper:not(:hover)  {
    //   .ht_master {
    //     .wtHolder {
    //       &::-webkit-scrollbar,
    //       &::-webkit-scrollbar-track,
    //       &::-webkit-scrollbar-track-piece,
    //       &::-webkit-scrollbar-button,
    //       &::-webkit-scrollbar-thumb,
    //       &::-webkit-resizer,
    //       &::-webkit-scrollbar-corner {
    //         background: transparent !important;
    //         background-color: transparent !important;
    //       }
    //     }
    //     // firefox
    //     scrollbar-color: transparent transparent !important;
    //   }
    // }
  }
}


@keyframes shine {
  0% {transition:none;}
  50% {background-color:#ccc;transition: all 0.3s ease-out;}
  100% {transition:none;}
}
