.ag-standard {
  .ag-pinned-left-cols-container {
    background: var(--ag-background-color, theme('colors.white'));
  }

  .ag-pinned-left-header,
  .ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-left-spacer:not(.ag-scroller-corner) {
    border-right: var(--ag-pinned-column-border);
  }

  .ag-pinned-left-cols-container,
  .ag-pinned-left-floating-top,
  .ag-pinned-left-floating-bottom {
    :not(.ag-cell-range-right) {
      .ag-cell.ag-cell-last-left-pinned {
        @apply focus:border-r-blue-400;
        border-right: var(--ag-pinned-column-border);

        &.h-range-selection-cell {
          @apply border-r-blue-700;
        }
      }
    }
  }
}
