.ag-standard {
  // AG Grid automatically calculates row height for header group cells in wrapper instead of cell
  .ag-floating-bottom .ag-cell {
    // 1px comes from ag-grid
    padding-top: calc(var(--h-table-padding-top, $default-vertical-padding) - var(--h-table-border-width, 1px));
    padding-bottom: calc(var(--h-table-padding-bottom, $default-vertical-padding) - var(--h-table-border-width, 1px) - 1px);
  }

  .ag-floating-top {
    @apply border-b-0;

    .ag-row:last-child {
      border-bottom: var(--ag-pinned-row-border);
    }

    // TODO: remove when AG-Grid release fix for AG-14241
    // AG-Grid has issues with auto row-height calculation in floating rows. See issue: https://github.com/ag-grid/ag-grid/issues/10057
    // Force text truncate to prevent incorrect row height and text overflow issues.
    // See more in: https://holistics.slack.com/archives/C04K5UHB84B/p1741601057072289
    .ag-cell {
      @apply truncate;
    }
  }

  .ag-floating-bottom {
    @apply border-t-0;

    // bottom total average rows container
    .ag-floating-bottom-container, .ag-pinned-left-floating-bottom {
      border-top: var(--ag-pinned-row-border);
    }

    // Force text truncate (see TODO 14241)
    .ag-cell {
      @apply truncate;
    }
  }

  .ag-row-pinned {
    @apply font-semibold;
    background: var(--ag-background-color, theme('colors.white'));

    &:not(:last-child) {
      border-bottom: var(--ag-pinned-row-border);
    }
  }
}
