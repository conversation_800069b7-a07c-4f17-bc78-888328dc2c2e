<template>
  <div class="flex flex-col gap-2 pt-4">
    <SearchBox
      v-model="searchTerm"
      class="max-w-md"
      placeholder="Search"
      clearable
    />

    <HTable
      v-h-loading.body="loading ? 'Fetching embed portals...' : undefined"
      class="embed-portals-table"
      :columns="columns"
      :data="embedPortals"
      :options="{
        showFilter: false,
        usePagination: false,
        sortable: ['name'],
        columnsClasses: {
          name: '!align-middle',
          description: 'w-[60%] !align-middle'
        }
      }"
    >
      <template #name="{ row }">
        <a
          class="text-link flex gap-0.5"
          @click="previewPortal(row as EmbedPortal)"
        >
          <HIcon name="embed-portal" />{{ row.uname }}
        </a>
      </template>
      <template #description="{ row }">
        <HTooltip :content="row.description">
          <div class="truncate">
            {{ row.description }}
          </div>
        </HTooltip>
      </template>
      <template #actions="{ row }">
        <div class="flex gap-0.5">
          <HTooltip
            :content="previewTooltipContent"
            disable-hoverable-content
          >
            <HButton
              :disabled="!embedEnabled || !embedCredentials.length"
              data-ci="embed-portal-preview-button"
              type="tertiary-default"
              size="sm"
              unified
              icon="play-no-circle"
              @click="previewPortal(row as EmbedPortal)"
            />
          </HTooltip>

          <HTooltip content="Go to definition">
            <a
              :href="buildEmbedPortalUrl(row.uname)"
              target="_blank"
              class="text-link flex gap-0.5"
            >
              <HIcon name="external-link" />
            </a>
          </HTooltip>
        </div>
      </template>
    </HTable>
    <h-pagination-cursor
      :pagination="pagination"
    />
  </div>
</template>
<script setup lang="ts">
import EmbedPortalSandboxModal from '@/modules/EmbedLinks/components/EmbedPortal/modals/EmbedPortalSandboxModal.vue';
import {
  HTable, HButton, HTooltip, useModal, HIcon,
} from '@holistics/design-system';
import {
  ref, onMounted, watch,
  computed,
} from 'vue';
import { toast } from '@aml-studio/h/services';
import { usePagination } from '@holistics/ds';
import SearchBox from '@/core/components/ui/SearchBox.vue';
import { useEmbedCredentials } from '@/modules/EmbedLinks/composables/useEmbedCredentials';
import { PageState } from '@holistics/ds/components/PaginationCursor/types';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import { until } from '@vueuse/core';
import { buildAmlObjectUrl } from '@aml-studio/client/utils/amlUrlScheme';
import { AMLRouteAction } from '@aml-studio/client/constants/route.constant';
import { get } from 'lodash';
import { fetchEmbedPortals as _fetchEmbedPortals } from '../../../../EmbedPortal/services/embedPortal';
import { EmbedPortal } from '../../../../EmbedPortal/types';

const { embedCredentials, isFetchingEmbedCredentials } = useEmbedCredentials();

const store = useStore();
const route = useRoute();
const router = useRouter();

const projectId = get(window, 'H.current_tenant.projects[0].id');
const embedEnabled = computed<number>(() => store.getters['tenantSubscription/embedEnabled']);

const searchTerm = ref('');
const loading = ref(false);
const columns = ['name', 'description', 'actions'];
const embedPortals = ref<EmbedPortal[]>([]);

const previewTooltipContent = computed(() => {
  if (!embedEnabled.value) {
    return 'Enable embedded analytics to preview';
  }

  if (!embedCredentials.value.length) {
    return 'Please create embed credentials to preview the portal';
  }

  return 'Preview';
});

async function fetchEmbedPortals (params: PageState) {
  loading.value = true;
  try {
    const result = await _fetchEmbedPortals({
      paginationParams: params,
      searchTerm: searchTerm.value,
    });
    loading.value = false;

    embedPortals.value = result.data?.embedPortals || [];
    if (result.data) {
      return result.data;
    }
  } catch (error) {
    toast.danger('Failed to fetch embed portals');
  } finally {
    loading.value = false;
  }

  return {
    embedPortals: [],
    cursor: { next: undefined, previous: undefined },
  };
}

const pagination = usePagination({
  fetchData: fetchEmbedPortals,
  defaultLimit: 10,
  toPreviousPage: true,
  isEmptyResponse: (result) => (result?.embedPortals as EmbedPortal[]).length <= 0,
});

const { open } = useModal();

async function previewPortal (row: EmbedPortal) {
  if (!embedCredentials.value.length) {
    toast.info('No embed credentials found', {
      description: 'Please create an embed credentials to preview the portal',
    });
    return;
  }

  open(EmbedPortalSandboxModal, {
    embedPortalId: row.id,
    embedCredentials: embedCredentials.value[0],
  });
}

async function openSandboxModalFromQueryParams () {
  const { embed_portal_id: embedPortalId } = route.query;

  if (!embedPortalId) {
    return;
  }

  if (!embedEnabled.value) {
    toast.info('Embedded analytics is not enabled. Enable in the Embed Settings tab to preview the portal');

    // Remove the query params
    router.replace({ query: { embed_portal_id: undefined } });
    return;
  }

  await until(isFetchingEmbedCredentials).toBe(false, {
    timeout: 5000,
    throwOnTimeout: true,
  });

  if (!embedCredentials.value.length) {
    toast.info('No embed credentials found. Create embed credentials in the Embed Settings tab to preview the portal');

    // Remove the query params
    router.replace({ query: { embed_portal_id: undefined } });
    return;
  }

  open(EmbedPortalSandboxModal, {
    embedPortalId: embedPortalId as unknown as number,
    embedCredentials: embedCredentials.value[0],
  });

  // Remove the query params
  router.replace({ query: { embed_portal_id: undefined } });
}

function buildEmbedPortalUrl (embedPortalName: string) {
  if (!projectId) {
    return '';
  }

  return buildAmlObjectUrl({
    projectId,
    fqName: embedPortalName,
    action: AMLRouteAction.SWITCH_TO_PRODUCTION_MODE,
  });
}

watch(searchTerm, async (_) => {
  const succeeded = pagination.toFirstPage();

  if (!succeeded) {
    pagination.fetchData();
  }
});

onMounted(async () => {
  await pagination.fetchData();
  openSandboxModalFromQueryParams();
});

</script>

<style lang="postcss">
.embed-portals-table {
  table {
    @apply table-fixed;
  }
}
</style>
