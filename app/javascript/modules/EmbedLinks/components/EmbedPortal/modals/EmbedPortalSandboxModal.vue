<template>
  <HModal
    v-model:shown="shown"
    size="fullscreen"
    no-footer
    class="flex flex-col overflow-hidden"
    data-ci="preview-embed-portal-modal"
    auto-focus-element
    prevent-click-outside
    prevent-press-escape
    prevent-close-animation-disabled
  >
    <template #header>
      <div class="px-3 py-4">
        <div class="text-sm font-medium">
          Embed Portal Sandbox
        </div>
        <div class="mt-1 flex items-center gap-1 text-2xs text-gray-600">
          ID: {{ embedPortal?.uname }}
        </div>
      </div>
    </template>
    <template #body>
      <div
        v-if="embedPortal"
        v-h-loading.body="isFetchingEmbedPortal"
        class="flex size-full min-w-0 overflow-hidden"
      >
        <EmbedPortalPayloadBuilder
          v-model="embedPayload"
          v-model:expiry-duration="expiryDuration"
          class="w-[450px]"
          :embed-portal="embedPortal"
          @preview-portal="previewPortal"
          @update:expiry-duration="refreshExpireTime"
        />
        <EmbedPortalPreview
          :key="iframeSource"
          :embed-credentials="props.embedCredentials"
          :embed-payload="embedPayload"
          :iframe-source="iframeSource"
          class="flex-1 border-l"
          @refresh-embed-portal="previewPortal"
        />
      </div>
      <HBanner
        v-else-if="fetchEmbedPortalError"
        type="danger"
        title="Embed Portal Error"
      >
        {{ fetchEmbedPortalError }}
      </HBanner>
    </template>
  </HModal>
</template>

<script setup lang="ts">
import EmbedPortalPayloadBuilder from '@/modules/EmbedLinks/components/EmbedPortal/PayloadBuilder/EmbedPortalPayloadBuilder.vue';
import { HModal, HBanner } from '@holistics/design-system';
import { defineModel, onMounted, ref } from 'vue';
import RowLevelPermissionRule from '@/modules/DataSets/models/RowLevelPermissionRule';
import { snakeCaseToCamelCase } from '@aml-studio/client/utils/helpers';
import Utils from '@/es6/utils';
import JsonWebToken from '@/es6/json_web_token';
import type { EmbedCredentials, EmbedPortal, EmbedPortalPayload } from '@/modules/EmbedPortal/types';
import EmbedPortalPreview from '../EmbedPortalPreview.vue';
import { fetchEmbedPortal } from '../services/fetchEmbedPortal.ajax';

const DEFAULT_EXPIRY_DURATION = 15 * 60; // 15 minutes

const props = defineProps<{
  embedPortalId: number,
  embedCredentials: EmbedCredentials,
}>();

const iframeSource = ref<string>('');
const targetUrl = `${Utils.baseUrl()}/embed`;
const embedPortal = ref<EmbedPortal>();

const shown = defineModel<boolean>('shown', { required: true });
const expiryDuration = ref<number | null>(DEFAULT_EXPIRY_DURATION);

const getTokenExpFromDuration = (durationInSecond: number) => {
  return new Date().getTime() / 1000 + durationInSecond;
};

const embedPayload = ref<EmbedPortalPayload>({
  object_name: '',
  object_type: 'EmbedPortal',
  embed_org_id: undefined,
  embed_user_id: undefined,
  user_attributes: {},
  permissions: {
    org_workspace_role: 'no_access',
    enable_personal_workspace: false,
  },
  exp: getTokenExpFromDuration(DEFAULT_EXPIRY_DURATION),
  settings: {
    default_timezone: null,
    allow_to_change_dashboard_timezone: false,
    allow_to_export_raw_data: false,
  },
});

function refreshExpireTime () {
  embedPayload.value.exp = expiryDuration.value ? getTokenExpFromDuration(expiryDuration.value) : undefined;
}

async function generateToken () {
  refreshExpireTime();
  return JsonWebToken.sign(embedPayload.value, props.embedCredentials.secretKey);
}

async function previewPortal () {
  const token = await generateToken();
  iframeSource.value = `${targetUrl}/${props.embedCredentials.hashCode}?_token=${token}`;
}

const isFetchingEmbedPortal = ref(false);
const fetchEmbedPortalError = ref<string>();

async function _fetchEmbedPortal () {
  isFetchingEmbedPortal.value = true;
  const { success, data, error } = await fetchEmbedPortal(props.embedPortalId);
  if (success) {
    embedPortal.value = {
      ...snakeCaseToCamelCase(data.embed_portal, true),
      rowLevelPermissionRules: data.embed_portal.row_level_permission_rules.map((r: any) => new RowLevelPermissionRule(r)),
    } as EmbedPortal;
    embedPayload.value.object_name = embedPortal.value.uname;
  } else {
    fetchEmbedPortalError.value = error;
  }
  isFetchingEmbedPortal.value = false;
}

onMounted(() => {
  _fetchEmbedPortal();
});

</script>
