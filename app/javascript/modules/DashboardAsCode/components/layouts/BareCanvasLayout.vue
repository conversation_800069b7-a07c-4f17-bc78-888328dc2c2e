<template>
  <div
    class="dac-canvas-layout"
    :style="{ width: `${layout.width}px`, height: `${layout.height}px` }"
  >
    <Canvas
      class="dac-canvas bg-white shadow"
      :class="canvasTheme.class"
      :style="canvasTheme.style"
      :width="layout.width"
      :height="layout.height"
      :items="blockPositions"
    >
      <template #item="{ uname }">
        <slot
          name="block"
          :block="findBlock(uname)"
        />
      </template>
    </Canvas>
  </div>
</template>

<script setup lang="ts">
import { computed, toRef } from 'vue';
import type { BlockPosition, CanvasLayout } from '@holistics/aml-std';
import Canvas from '../canvas/Canvas.vue';
import { setLayoutActions } from '../../composables/useLayoutActions';
import type { LayoutEmits, LayoutProps } from './props';
import { generateDefaultBlockPosition } from '../../utils/layouts';
import { useDashboard } from '../../composables/useDashboard';
import { useDashboardTheme } from '../../composables/useDashboardTheme';

const props = defineProps<LayoutProps & {
  layout: CanvasLayout
}>();

defineEmits<LayoutEmits>();

const blockPositions = computed(() => {
  return props.definition.blocks.reduce<Record<string, BlockPosition>>((acc, curr) => {
    const blockPosition = props.layout.blocks[curr.uname];
    if (blockPosition) {
      acc[curr.uname] = blockPosition;
    } else if (!props.inclusive) {
      acc[curr.uname] = generateDefaultBlockPosition(props.layout, curr);
    }
    return acc;
  }, {});
});

function findBlock (uname: string) {
  const block = props.definition.blocks.find(b => b.uname === uname);
  if (!block) {
    throw new Error(`Can't find block: ${block}`);
  }
  return block;
}

setLayoutActions([]);

// theme
const { dashboard } = useDashboard();
const { resolveCanvasTheme } = useDashboardTheme(toRef(() => dashboard.value.definition));
const canvasTheme = computed(() => resolveCanvasTheme(props.layout));
</script>

<style lang="postcss">
.dac-canvas-layout {
  /* in canvas layout, let the ic expands its container width */
  .dac-ic-block {
    .h-dynamic-filter {
      width: unset !important;
    }
  }
}
</style>
