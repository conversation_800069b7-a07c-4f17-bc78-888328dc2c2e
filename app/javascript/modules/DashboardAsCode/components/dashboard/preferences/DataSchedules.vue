<template>
  <div
    v-h-loading.body="isFetching"
    class="flex h-full"
  >
    <DataScheduleTutorial
      v-if="isEmpty(decoratedSchedules)"
      :creation-options="creationOptions"
    />
    <div
      v-else
      class="flex flex-1 flex-col"
    >
      <div>
        <span class="font-medium">
          Schedules
        </span>
        <HPopover
          trigger="hover"
          placement="top"
          floating-class="popover-data-schedule"
        >
          <h-icon
            class="popover-info"
            name="info"
          />
          <template #content>
            <span>Schedules help you with scheduling and delivering dashboards automatically to others. </span>
            <a
              :href="docsUrl"
              target="_blank"
            >Learn more</a>
          </template>
        </HPopover>
      </div>
      <div class="data-schedule-list vue-datatable-wrapper">
        <div
          v-if="isFetching"
          class="pt-2"
        >
          <h-icon
            name="circle-notch"
            spin
          />
          <span>Loading</span>
        </div>
        <HTable
          v-else
          class="[&_table]:h-fit [&_tr]:h-full"
          :data="decoratedSchedules"
          :columns="columns"
          :options="listingOptions"
        >
          <template #destination="{ row }">
            <DataScheduleCell
              class="flex h-full items-center"
              :data="row.dest"
              :dest-type="row.dest.type"
              cell-type="destinations"
              :is-v3="true"
            />
          </template>
          <template #schedule_title="{ row }">
            <ScheduleLabel
              class="flex h-full items-center"
              :schedule="row.schedule"
              :source-id="row.id"
              @update-schedule="() => onScheduleUpdated(row)"
            />
          </template>
          <template #last_run="{ row }">
            <LastRun
              v-model="row.last_run_job"
              class="flex h-full items-center"
              :source-type="'EmailSchedule'"
              source-method="execute"
              :source-id="row.id"
            />
          </template>
          <template #info="{ row }">
            <double-column-tooltip
              placement="top"
              offset="5"
              class="filter-tooltip ml-1 !flex h-full items-center"
              trigger="hover"
              :map="getFilterTooltip(row.dynamic_filter_presets)"
            >
              <h-icon name="info" />
              <template #header>
                <div class="mb-1 text-gray-50">
                  Filters Applied
                </div>
              </template>
              <template #footer>
                <div class="tooltip-footer mt-1">
                  <hr class="-mx-2 my-1 border-gray-700">
                  <div>
                    <span class="pr-1 text-gray-500">Creator: </span> <span class="text-gray-300">{{ row.creator_name }}</span>
                  </div>
                  <dest-extra-info
                    :dest-type="row.dest_type"
                    :dest="row.dest"
                  />
                </div>
              </template>
            </double-column-tooltip>
          </template>
          <template #actions="{ row }">
            <HDropdown
              v-if="row.permissions.can_crud || row.permissions.can_execute"
              class="flex h-full items-center"
              :options="generateActions(row)"
            >
              <HButton
                type="tertiary-default"
                unified
                icon="ellipsis-horizontal"
                size="sm"
                class="ci-actions"
              />

              <template #delete="{ onClose }">
                <FloatingDeletePanel
                  :title="'id ' + row.id"
                  type="email schedule"
                  class="!block"
                  @delete-panel="onClose(true); deleteDataSchedule(row)"
                >
                  <div class="ci-es-delete flex cursor-pointer items-start space-x-1 rounded p-2 text-red-500 hover:bg-gray-100 active:bg-gray-400">
                    <h-icon
                      name="delete"
                      class="mr-1"
                    />
                    Delete
                  </div>
                </FloatingDeletePanel>
              </template>
            </HDropdown>
          </template>
        </HTable>
      </div>
      <div
        v-if="!isFetching && source"
        class="mt-auto flex justify-end"
      >
        <HButton
          v-show="!isFetching && dataSchedules.length"
          type="tertiary-highlight"
          class="ci-test-run-es font-medium"
          @click="sendAll"
        >
          Send all
        </HButton>
        <HDropdown
          :options="creationOptions"
          placement="top-end"
        >
          <HButton
            type="primary-highlight"
            class="ci-new-schedule-btn ml-2"
          >
            New Schedule
          </HButton>
        </HDropdown>
      </div>
    </div>
  </div>
  <DataScheduleModal
    v-if="currentDataSchedule"
    v-model:shown="shown"
    :source="source"
    :data-schedule="currentDataSchedule"
    :dashboard-filter-conditions="dashboardFilterConditions"
    @dismiss="onResolve"
    @resolve="onResolve"
  />
</template>
<script lang="ts">
/* eslint-disable import/first */
import {
  computed, onMounted, ref, defineAsyncComponent,
} from 'vue';

const DataScheduleModal = defineAsyncComponent(() => import('@/modules/DataDelivery/components/DataScheduleModal.vue'));
</script>
<script setup lang="ts">
import {
  HPopover, HDropdown, HButton, HTable, DropdownOption,
} from '@holistics/design-system';
import {
  fetchDataSchedules, updateDataSchedule, getDataSchedule, executeAll, executeDataSchedule, destroyDataSchedule,
} from '@/modules/DataDelivery/services/dataSchedules.ajax';
import { isEmpty, filter, isObject } from 'lodash';
import LastRun from '@/vue_components/last_run.vue';
import DataScheduleCell from '@/vue_components/data_schedules/data_schedule_cell.vue';
import ScheduleLabel from '@/vue_components/schedule/schedule_label.vue';
import FloatingDeletePanel from '@/core/components/ui/FloatingDeletePanel.vue';
import usageReminderModal from '@/modules/AppAlerts/services/modals/usageReminder.modal';
import DestExtraInfo from '@/modules/DataSchedule/components/DestExtraInfo.vue';
import { DEST_LIST } from '@/modules/DataSchedule/constants/dests';
import DoubleColumnTooltip from '@/core/components/ui/DoubleColumnTooltip/DoubleColumnTooltip.vue';
import generateDocsLink from '@/modules/FeatureToggleGroup/utils/generateDocsLink';
import type Condition from '@/modules/Viz/submodules/VizFilters/models/Condition';
import store from '@/entryPoint/store';
import { handleAjaxError } from '@/core/services/ajax';
import { success } from '@/core/services/notifier';
import {
  DataSchedule, InitialDataSchedule, DataDeliverySource, DestType,
} from '@/modules/DataDelivery/types';
import { confirm } from '@/core/services/modal';
import DataScheduleTutorial from '@/modules/DataDelivery/components/DataScheduleTutorial.vue';

const props = defineProps<{
  source: DataDeliverySource
  dashboardFilterConditions: Record<string | number, Condition>
}>();
const dataSchedules = ref<Record<string, any>[]>([]);
const isFetching = ref(false);
const docsUrl = generateDocsLink('/docs/data-schedules');
const shown = ref(false);
const currentDataSchedule = ref<any>(null);

async function fetchAllDataSchedules () {
  try {
    isFetching.value = true;
    ({ data_schedules: dataSchedules.value } = await fetchDataSchedules({ sourceId: props.source.data.id, sourceType: props.source.sourceType }));
  } catch (error) {
    handleAjaxError(error);
  } finally {
    isFetching.value = false;
  }
}

onMounted(() => {
  fetchAllDataSchedules();
});

const isExceedTenantUsage = computed(() => {
  return store.getters['tenantSubscription/isExceedTenantUsage'];
});

async function openDataScheduleModal (dataSchedule: InitialDataSchedule | DataSchedule) {
  currentDataSchedule.value = dataSchedule;
  shown.value = true;
}

const creationOptions = computed(() => {
  return DEST_LIST
    .filter((dest) => dest.checkEnabled({ sourceType: props.source.sourceType, version: String(props.source.data.version) }))
    .map((dest) => ({
      key: dest.type,
      label: `Add ${dest.displayName} Schedule`,
      icons: dest.icon,
      class: `ci-new-${dest.kebabType}`,
      action: () => openDataScheduleModal({ dest: { type: dest.type as unknown as DestType } } as InitialDataSchedule),
    })) as DropdownOption[];
});
const columns = ['destination', 'schedule_title', 'last_run', 'info', 'actions'];

const decoratedSchedules = computed(() => {
  return filter(dataSchedules.value, s => isObject(s.schedule)).map(ds => {
    if (!ds.dynamic_filter_presets) {
      ds.dynamic_filter_presets = [];
    }
    return ds;
  });
});

function upsertDataSchedule (newDataSchedule: DataSchedule) {
  const idx = dataSchedules.value.findIndex((ds) => ds.id === newDataSchedule.id);
  if (idx >= 0) {
    dataSchedules.value.splice(idx, 1, newDataSchedule);
  } else {
    dataSchedules.value.push(newDataSchedule);
  }
}

function onResolve (data?: null | DataSchedule) {
  currentDataSchedule.value = null;
  if (data) {
    upsertDataSchedule(data);
  }
  shown.value = false;
}

async function onScheduleUpdated (dataSchedule: DataSchedule) {
  try {
    isFetching.value = true;
    const { data_schedule: updatedDataSchedule } = await getDataSchedule(dataSchedule.id);
    upsertDataSchedule(updatedDataSchedule);
  } catch (error) {
    handleAjaxError(error, 'Failed to update this Data Schedule');
  } finally {
    isFetching.value = false;
  }
}

function getFilterTooltip (presets: { dynamic_filter_label: string, condition_display_value: string }[]) {
  return presets.reduce((acc, preset) => {
    acc[preset.dynamic_filter_label] = preset.condition_display_value;

    return acc;
  }, {} as Record<string, string>);
}

async function sendAll () {
  const confirmed = await confirm(
    'Send all?',
    'This will execute all Data Schedules.',
  );
  if (confirmed) {
    try {
      await executeAll(props.source);
      fetchAllDataSchedules();
      success('All schedules successfully queued for execution!');
    } catch (error) {
      handleAjaxError(error, 'Failed to send schedules');
    }
  }
}

async function runDataSchedule (id: number) {
  try {
    const { job } = await executeDataSchedule({ id });
    const executedDataSchedule = dataSchedules.value.find((ds) => ds.id === id);
    if (executedDataSchedule) {
      executedDataSchedule.last_run_job = job;
    }
    success('All schedules successfully queued for execution!');
  } catch (error) {
    handleAjaxError(error, 'Failed to send schedules');
  }
}

async function toggleDataSchedule (dataSchedule: any) {
  if (isExceedTenantUsage.value && dataSchedule.paused) {
    usageReminderModal();
    return;
  }
  try {
    const { data_schedule: updatedDataSchedule } = await updateDataSchedule({
      ...dataSchedule,
      schedule: {
        ...dataSchedule.schedule,
        paused: !dataSchedule.schedule.paused,
      },
    });
    upsertDataSchedule(updatedDataSchedule);
    success('Data Schedule has been updated successfully!');
  } catch (err) {
    handleAjaxError(err, 'Failed to update this Data Schedule');
  }
}

function editDataSchedule (dataSchedule: DataSchedule) {
  openDataScheduleModal(dataSchedule);
}

async function deleteDataSchedule (dataSchedule: DataSchedule) {
  isFetching.value = true;
  try {
    await destroyDataSchedule(dataSchedule.id);
    const idx = dataSchedules.value.findIndex((ds) => ds.id === dataSchedule.id);
    dataSchedules.value.splice(idx, 1);
    success('Data Schedule has been deleted successfully!');
  } catch (error) {
    handleAjaxError(error, 'Failed to update this Data Schedule');
  }
  isFetching.value = false;
}

function generateActions (dataSchedule: any) {
  return [
    dataSchedule.permissions.can_crud && {
      key: 'send',
      label: 'Run Schedule',
      icons: 'send',
      class: 'ci-es-send',
      action: () => runDataSchedule(dataSchedule.id),
    },
    dataSchedule.permissions.can_crud && {
      key: 'pause',
      label: dataSchedule.schedule.paused ? 'Run' : 'Pause',
      icons: dataSchedule.schedule.paused ? 'play' : 'pause',
      class: 'ci-es-pause',
      action: () => toggleDataSchedule(dataSchedule),
    },
    dataSchedule.permissions.can_crud && {
      key: 'edit',
      label: 'Edit',
      icons: 'edit',
      class: 'ci-es-edit',
      action: () => editDataSchedule(dataSchedule),
    },
    dataSchedule.permissions.can_crud && {
      key: 'delete',
      slot: 'delete',
    },
  ].filter(Boolean);
}
const listingOptions = computed(() => {
  return {
    sortable: ['destination', 'schedule_title'],
    orderBy: {
      column: 'schedule_title',
      ascending: false,
    },
    headings: {
      schedule_title: 'Schedule',
      destination: 'Destinations',
      last_run: 'Last Run',
      info: 'Info',
      actions: ' ',
    },
    columnsClasses: {
      schedule_title: 'schedule',
      destination: 'dest',
      last_run: 'last-run',
      info: 'information pr-0',
      actions: 'settings px-0',
    },
    texts: {
      noResults: 'No results found :(',
    },
    filterable: true,
    perPage: 10,
  };
});
</script>
