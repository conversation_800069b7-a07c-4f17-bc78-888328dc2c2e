<template>
  <div
    class="dac-viz-block flex size-full flex-col"
    data-ci="viz-block"
  >
    <div
      v-if="!block.settings?.hide_label || expanded"
      class="mb-1 flex h-5 items-center space-x-1"
    >
      <div
        :class="expanded ? 'w-[75%] truncate' : 'cursor-pointer truncate hover:underline'"
        @click="onClickTitle"
      >
        <span
          class="dac-viz-block-label truncate"
          :style="labelTheme.style"
          :class="labelTheme.class"
        >
          {{ blockTitle }}
        </span>
      </div>
      <HTooltip
        v-if="hasNonRelationalDataModelsError"
        :content="'Invalid AML code, please open the code mode or dual mode to fix it'"
        placement="top"
      >
        <h-icon
          name="exclamation-triangle"
          class="text-red-700"
        />
      </HTooltip>
      <HTooltip
        v-if="block.description"
        placement="top"
        class="!flex items-center"
      >
        <h-icon
          name="info"
        />
        <template #content>
          <VueMarkdown :source="block.description" />
        </template>
      </HTooltip>
    </div>
    <div
      v-else-if="!blockTitle"
      class="mb-1 h-5"
      aria-hidden="true"
    />
    <DrillDownControls
      v-if="hasDrillDownData"
      :model-value="drillDownManager.currentPosition(props.block.uname)"
      :options="drillDownHistoryOptions"
      @reset="drillReset"
      @update:model-value="(val) => {
        drillDownManager.updatePosition(props.block.uname, val);
        trackInteractDrillDownHistory(currentUser.id, props.block.viz.viz_setting.viz_type);
      }"
    />
    <div
      v-if="(!shouldLoadWidget && !expanded) || !vizDependenciesReady"
      v-h-loading.skeleton="!vizDependenciesReady"
      class="flex size-full rounded-sm bg-gray-50"
      data-ci="ci-viz-block-body-placeholder"
    />
    <template v-else>
      <VizResult
        ref="vizRef"
        class="dac-viz-container flex-1"
        :style="vizTheme.style"
        :source="vizSource"
        :selected-values="crossFilterSelectedValues"
        :allow-cross-filter="canPerformCrossFilter"
        :viz-setting="appliedVizSetting"
        :data-set="dataset"
        :project-id="extraDetails.projectId"
        :loading-options="vizResultLoadingOptions"
        :allow-drillthrough="isDrillthroughEnabled"
        allow-drill-down
        allow-view-underlying-data
        update-trigger="auto"
        show-context-menu
        :is-production="!extraDetails.inDevMode"
        :options="vizOptions"
        :zoom-level="zoomLevel"
        :parent-hspan="hspan"
        :infinite-scroll="!expanded"
        :refresh-cancelable="refreshCancelable"
        :edit-mode="editMode"
        :adhoc-settings="vizAdhocSettings"
        :drill-down-metadata="drillDownMetadata"
        :interaction-config="interactionConfig"
        :cache-manager="cacheManager"
        :cache-key="vizSource.id"
        @updated="onVizResultUpdated"
        @updating="onVizUpdatingChange"
        @submit-res="onSubmitRes"
        @pivoted="setPivotedData"
        @executed-query="setExecutedQuery"
        @job-status="setJobStatus"
        @job-poller="setJobPoller"
        @cross-filter="onCrossFilter"
        @drillthrough="onDrillthrough"
        @on-explore-data="exploreData"
        @cancel-refresh="cancelRefresh"
        @rendered="onRendered"
        @on-job-done="onJobDone"
        @transform="onVizSettingTransform"
        @on-sort-changed="onSortChanged"
        @store-adhoc-settings="setAdhocSettings"
        @non-relational-data-models="hasMissingRelationships"
        @view-underlying-data="viewUnderlyingData"
        @undo-changes="undoAdhocInteractions"
        @drill-down="onDrillDown"
      />
    </template>

    <portal
      v-if="dataset && allConditions.length > 0"
      :to="`block-${block.uname}-mapped-conditions`"
    >
      <ReportWidgetConditions
        as-button
        item-type="Block"
        :viz-conditions="allConditions"
        :data-models="dataset.data_models || []"
        :adhoc-fields="appliedVizSetting.adhoc_fields"
      />
    </portal>
    <portal
      v-if="canPerformMultipleSort(block.viz.viz_setting.viz_type, true)"
      :to="`block-${block.uname}-table-sort`"
    >
      <TableSort
        :viz-setting="block.viz.viz_setting"
        :dataset="dataset"
        :viz-setting-sort="vizSettingSort"
        @sort="onTableSortFromControls"
      />
    </portal>
  </div>
</template>

<script setup lang="ts">
import {
  computed, ref, type Ref, toRef, inject,
} from 'vue';
import {
  cloneDeep,
  get, isEqual, isNil, merge,
} from 'lodash';
import { HTooltip } from '@holistics/design-system';
import { type VizBlock } from '@holistics/aml-std';
import type { VizSort } from '@holistics/types';
import VueMarkdown from '@/vue_components/vue_markdown.vue';
import ReportSort from '@/modules/QueryReports/services/ReportSort';
import ReportPagination from '@/modules/QueryReports/services/ReportPagination';
import VizResult from '@/modules/Viz/components/VizResult.vue';
import ReportWidgetConditions from '@/modules/DynamicDashboards/components/widgets/ReportWidgetConditions.vue';
import HolisticsError from '@/core/services/HolisticsError';
import { useHOtelStore } from '@/core/store/hOtelStore';
import { startSpan, type HSpan } from '@/modules/HOtel/services/tracing';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import { drillthroughManagerKey } from '@/modules/Drillthroughs/plugins/constant';
import { canPerformMultipleSort } from '@/modules/Viz/utils/buildSortOptions';
import { DATA_TABLE, PIVOT_TABLE } from '@/modules/Viz/constants/vizTypes';
import { transformVizSetting } from '@/modules/Viz/services/transformVizSetting';
import { AdhocSettings } from '@/modules/Viz/types/adhocSettings';
import { useToasts } from '@/core/composables/useToasts';
import { InteractionPresetType } from '@/modules/Viz/types/interactionPreset';
import { VizSettingTransformation } from '@/modules/Viz/types/vizSettingTransformation';
import { InteractionName } from '@/modules/Viz/constants/interaction';
import type { ExtractedValues } from '@/modules/Viz/utils/valuesExtractors/types';
import { trackInteractDrillDownHistory } from '@/modules/Viz/utils/drillDown/drillDownUsageTracking';
import { User } from '@/core/plugins/user';
import { useDashboard } from '../../composables/useDashboard';
import { useDashboardFilters } from '../../composables/useDashboardFilters';
import { useDashboardConfigs } from '../../composables/useDashboardConfigs';
import { useDashboardVizStates } from '../../composables/useDashboardVizStates';
import { useZoomable } from '../../composables/useZoomable';
import { type VizRenderOptions, type VizState } from '../../types';
import { useDashboardTheme } from '../../composables/useDashboardTheme';
import { useVizBlockStates } from '../../composables/useVizBlockStates';
import { useViewUnderlyingData } from '../../composables/useViewUnderlyingData';
import TableSort from './customControls/TableSort.vue';
import { useDrillDown } from './drillDown/useDrillDown';
import DrillDownControls from './drillDown/DrillDownControls.vue';

const props = defineProps<{
  block: VizBlock
  editMode?: boolean
  expanded?: boolean
  uiEditDisabled?: boolean
}>();
const emit = defineEmits<{(e: 'edit'): void
  (e: 'save', payload?: VizBlock): void
}>();

const currentUser = new User();

const { dashboard } = useDashboard();
const { extraDetails } = useDashboardConfigs();
const { zoomLevel } = useZoomable();
const {
  getBlockState,
  setBlockState,
  registerRefreshVizHandler,
  explorationBlockUname,
  shouldLoadWidget,
  cancelRefresh,
  refreshing,
  cancelingRefresh,
  refreshCount,
  refreshCancelable,
  expandedBlockUname,
  drillDownManager,
  cacheManager,
} = useDashboardVizStates();
const { getCurrentPageSpan } = useHOtelStore();

const vizRef = ref();
const dateDrillVizSetting = computed(() => vizRef?.value?.dateDrill?.vizSetting);

const {
  hasDrillDownData,
  drillDownHistoryOptions,
  drillReset,
  onDrillDown,
  drilledDownBlockTitle,
  drillDownMetadata,
} = useDrillDown(
  props.block,
  drillDownManager,
  ref(dashboard.value?.definition),
  dateDrillVizSetting,
);

const drillthroughsManager = inject(drillthroughManagerKey);

const {
  crossFilters, appliedCrossFilters, crossFilterInteractions,
} = useDashboardFilters();
const {
  appliedVizSetting,
  vizDependenciesReady,
  dataset,
  vizSource,
  adhocInteractions,
  undoAdhocInteractions,
} = useVizBlockStates(toRef(() => props.block), toRef(() => props.editMode));
const allConditions = computed(() => appliedVizSetting.value.filters || []);
const isDrillthroughEnabled = checkFeatureToggle('dac:drillthrough');
const blockState = computed(() => getBlockState(props.block.uname));
const vizAdhocSettings = computed(() => blockState.value.adhocSettings);
const vizOptions = computed(() => ({
  widget: !props.expanded,
  widgetId: props.block.uname,
  container: '.dac-viz-container',
  sort: new ReportSort(`${dashboard.value?.id}-${props.block.uname}`, true),
  pagination: new ReportPagination({
    paginated: {
      page: 1,
      page_size: 100,
    },
  }),
  isV4: true,
}));

// * reporting-based dashboard: from_aml is false, id != 0
// * aml-based dashboard - prod mode: from_aml is true, id != 0
// * aml-based dashboard - dev mode: from_aml is undefined, id is 0
const isAmlBasedDashboard = computed(() => {
  return dashboard.value?.from_aml || !dashboard.value?.id;
});

const hasNonRelationalDataModelsError = ref(false);

const spanContext = getCurrentPageSpan()?.spanContext;

const hspanAttributes = merge({
  'h.dashboard_block_uname': props.block.uname,
}, dashboard.value?.id ? {
  'h.dashboard_id': dashboard.value?.id,
} : {
  'h.dashboard_uname': dashboard.value?.definition?.uname,
});

const hspan = ref(isAmlBasedDashboard.value ? startSpan('VizBlock', {
  timeoutMillis: 0,
  spanOptions: {
    links: spanContext ? [{
      context: spanContext,
    }] : [],
    attributes: hspanAttributes,
  },
}) : null) as Ref<HSpan | null>;

const canPerformCrossFilter = computed(() => {
  return !props.expanded && !!crossFilterInteractions.value.find((i) => {
    return i.from === props.block.uname;
  });
});

const crossFilterSelectedValues = computed(() => {
  const isCrossFilteringSource = props.block.uname === crossFilters.value.widgetId;
  return isCrossFilteringSource ? crossFilters.value.rawValues : {};
});

function onCrossFilter (payload: any) {
  if (!isEqual(payload, crossFilters.value)) {
    crossFilters.value = {
      ...payload,
      widgetId: props.block.uname,
    };
  }
}

registerRefreshVizHandler(props.block.uname, (renderOptions: VizRenderOptions) => {
  setBlockState(props.block.uname, {
    refreshing: true,
  });
  vizRef.value?.update(renderOptions);
});
function onVizUpdatingChange (isRefreshing: boolean) {
  setBlockState(props.block.uname, {
    refreshing: isRefreshing,
  });
}
function onVizResultUpdated (payload: any) {
  const lastCached = get(payload, 'meta.lastCacheUpdated');
  const lastCachedDate = lastCached ? new Date(lastCached) : undefined;
  setBlockState(props.block.uname, {
    lastCached: lastCachedDate,
    refreshing: false,
  });
}
function onSubmitRes (payload: { job_id: number, status: string }) {
  setBlockState(props.block.uname, {
    jobId: payload.job_id,
    status: payload.status,
  });
}
function setPivotedData (payload: any) {
  setBlockState(props.block.uname, {
    pivotedData: payload,
  });
}
function setExecutedQuery (sql: string, aql?: string) {
  setBlockState(props.block.uname, {
    executedQuery: sql,
    executedAql: aql,
  });
}
function setJobStatus (payload: { id: number, status: string, pendingJobsCount?: number }) {
  const status: Partial<VizState> = {
    jobId: payload.id,
    status: payload.status,
  };
  status.pendingJobsCount = payload.pendingJobsCount;
  setBlockState(props.block.uname, status);
}
function setJobPoller (jobPoller: Pick<VizState, 'jobPoller'>) {
  setBlockState(props.block.uname, { jobPoller });
}

function onDrillthrough (drillthrough: any) {
  if (extraDetails.inDevMode) {
    drillthroughsManager?.drillInDevMode(drillthrough);
  } else {
    drillthroughsManager?.drill(drillthrough, { isCanvasDashboard: true });
  }
}

const blockTitle = computed(() => {
  return hasDrillDownData.value
    ? drilledDownBlockTitle.value
    : (props.block.label || props.block.uname);
});

function exploreData () {
  if (!explorationBlockUname.value) {
    explorationBlockUname.value = props.block.uname;
  }
}
function onJobDone () {
  setBlockState(props.block.uname, { canceling: false, jobPoller: null });
}
function setAdhocSettings (adhocSettings: AdhocSettings) {
  setBlockState(props.block.uname, { adhocSettings });
}

function hasMissingRelationships () {
  hasNonRelationalDataModelsError.value = true;
}

function onClickTitle () {
  if (props.expanded) {
    return;
  }
  expandedBlockUname.value = props.block.uname;
}

const cancelRefreshLoadingOptions = computed(() => {
  return {
    loadingHoverMessage: 'Cancel Refresh',
    loadingOnClick: cancelRefresh,
  };
});

const vizLoadingMessage = computed(() => {
  if (cancelingRefresh.value) return 'Canceling Refresh';
  if (refreshing.value && refreshCount.value > 0) return 'Refreshing';
  return appliedCrossFilters.value.conditions.length > 0 ? 'Applying Cross-filter' : null;
});

const vizResultLoadingOptions = computed(() => {
  return {
    loadingMessage: vizLoadingMessage.value,
    ...cancelRefreshLoadingOptions.value,
  };
});

function onRendered ({ error }: Record<string, HolisticsError | undefined> = {}) {
  if (hspan.value) {
    hspan.value.end({ error });
    hspan.value = null;
  }
}

const { setViewUnderlyingData } = useViewUnderlyingData();
function viewUnderlyingData (values: ExtractedValues) {
  setViewUnderlyingData({ drillValues: values, blockUname: props.block.uname, interactions: adhocInteractions.value });
}

// theme
const { resolveBlockLabelTheme, resolveVizTheme } = useDashboardTheme(toRef(() => dashboard.value.definition));
const labelTheme = computed(() => resolveBlockLabelTheme(props.block));
const vizTheme = computed(() => resolveVizTheme(props.block.viz));

const { toast } = useToasts();
const interactionConfig = computed<InteractionPresetType>(() => {
  const editInDevelopment = !isNil(extraDetails.projectId) && props.editMode;
  const editInReporting = !extraDetails.inDevMode && props.editMode;
  if (editInDevelopment || editInReporting) {
    return 'full';
  }
  return checkFeatureToggle('viz_interaction:persist_adhoc_actions') ? 'interactionSortOnly' : 'paginateSortOnly';
});

function onVizSettingTransform (transformVizSettingPayload: VizSettingTransformation) {
  if (!props.editMode) {
    adhocInteractions.value.push(transformVizSettingPayload);
    return;
  }
  if (props.uiEditDisabled) {
    toast.info('UI interactions (e.g., resize/reorder columns) on reused blocks won\'t be saved.', {
      placement: 'bottom-right',
    });
    return;
  }
  if (props.expanded) {
    // NOTE: ignore resize and freeze interactions since they are affected by the viewport
    const ignoredInteractions = [InteractionName.ResizeColumn, InteractionName.FreezeColumn];
    if (transformVizSettingPayload.type && ignoredInteractions.includes(transformVizSettingPayload.type)) return;
  }
  const newVizBlock = cloneDeep(props.block);

  const vs = transformVizSetting(newVizBlock.viz.viz_setting, transformVizSettingPayload);
  if (!vs) return;
  newVizBlock.viz.viz_setting = vs;

  emit('save', newVizBlock);
}

// handle sort
function getInitialTableSort () {
  if ([PIVOT_TABLE, DATA_TABLE].includes(props.block.viz.viz_setting.viz_type)) {
    return props.block.viz.viz_setting.settings?.sort as VizSort;
  }
  return undefined;
}
const vizSettingSort = ref(getInitialTableSort());
function onSortChanged (value: VizSort) {
  vizSettingSort.value = value;
}
function onTableSortFromControls (sortSetting: VizSort) {
  vizRef.value?.handleSort(sortSetting);
}
</script>

<style lang="postcss">
.dac-viz-block {
  .dd-select {
    visibility: hidden;
    z-index: 1;
    .h-select__container {
      opacity: 0.7;
    }
  }
  .hc-action-menu {
    visibility: hidden;
  }

  &:hover {
    .dd-select {
      visibility: visible;
    }
    .hc-action-menu {
      visibility: visible;
    }
  }
  .error-message {
    @apply overflow-y-scroll;
  }
}
</style>
