<script setup lang="ts">
import {
  computed, ref, toRef,
  watch,
} from 'vue';
import { type BlockPosition } from '@holistics/aml-std';
import { ADD_NEW_COLUMN_ID } from '@/modules/Table/constants/column';
import { CANVAS_UNAME, ITEMS_GROUP_UNAME } from './constants';
import { useCanvasPosition } from './useCanvasPosition';
import CanvasAddons from './CanvasAddons.vue';

const props = withDefaults(defineProps<{
  items: Record<string, BlockPosition>
  width: number
  height: number
  editMode?: boolean
  uiEditDisabledItems?: string[]
  dragAndDropDisabledItems?: string[]
  smartMode?: boolean
  snapToGrid?: boolean
  gridSize?: number
  container?: HTMLElement
  zoomLevel?: number
  showCanvasOutline?: boolean
}>(), {
  editMode: false,
  uiEditDisabledItems: () => ([]),
  dragAndDropDisabledItems: () => ([]),
  smartMode: false,
  snapToGrid: false,
  gridSize: 0,
  container: undefined,
  zoomLevel: 1,
});

const emit = defineEmits<{
  'update:items': [Record<string, BlockPosition>]
  'update:canvas': [{w: number, h: number}]
  'update:temporaryHeight': [number]
  'update:temporaryWidth': [number]
  'deleteItems': [string[]]
  'selectItems': [string[]]
}>();

const canvasEl = ref<HTMLElement>();
const {
  activeItems,
  overlappedItems,
  alignedItems,
  alignmentLines,
  itemsPositions,
  selectItems,
  working,
  mouseMoving,
  canvasWidth,
  canvasHeight,
  onItemVisibilityChange,
  selectRect,
  selectedItemsGroupPosition,
  resizeToFit,
  ghostItemsPosition,
} = useCanvasPosition(toRef(() => props.width), toRef(() => props.height), toRef(() => props.items), {
  editMode: toRef(() => props.editMode),
  canvasEl,
  scrollBody: toRef(() => props.container),
  zoomLevel: toRef(() => props.zoomLevel),
  smartMode: toRef(() => props.smartMode),
  snapToGrid: toRef(() => props.snapToGrid),
  gridSize: toRef(() => props.gridSize),
  dragAndDropDisabledItems: toRef(() => props.dragAndDropDisabledItems),
  ignoreSelectors: [
    '.hui-popper-floating-inner',
    '.dac-submit-filters',
    '.h-modal-backdrop',
    '.ag-header-cell-resize',
    '.h-input',
    '.feat-ignore-dnd',
    '.feat-fca',
    '.h-dynamic-filter',
    `[col-id="${ADD_NEW_COLUMN_ID}"]`,
  ],
  ignoreMovingSelectors: [
    '.ag-header-cell',
    '.ag-cell',
  ],
  onDragEnd: (params) => {
    if (params.type === 'items') {
      emit('update:items', params.position);
    } else {
      emit('update:canvas', params.size);
    }
  },
  onDelete: (items) => {
    emit('deleteItems', items);
  },
  onSelect: (items) => {
    emit('selectItems', items);
  },
});

// the idea is to render a div with 4 dots in 4 edges, and use background repeat
const gridBgImage = computed(() => {
  const s = props.gridSize;
  const f = 'rgba(133,139,157,0.3)';
  // eslint-disable-next-line max-len
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" height="${s}" width="${s}"><circle r="1" fill="${f}"/><circle cx="${s}" r="1" fill="${f}"/><circle cx="${s}" cy="${s}" r="1" fill="${f}"/><circle cy="${s}" r="1" fill="${f}"/></svg>`;
  return `url('data:image/svg+xml;base64,${btoa(svg)}')`;
});

const gridCoverStyle = computed(() => {
  if (props.editMode && props.snapToGrid && props.gridSize > 0 && activeItems.value.length > 0) {
    return {
      backgroundImage: gridBgImage.value,
    };
  }
  return {};
});

const minLayer = computed(() => Math.min(...Object.values(props.items).map(i => i.layer)));

// add a padding around select box, to prevent the cursor from interacting with canvas elements while dragging
const SELECT_BOX_PADDING = 100;
const dragSelectBox = computed(() => {
  if (!selectRect.start || !selectRect.end) {
    return {
      selecting: false,
    };
  }

  return {
    selecting: true,
    style: {
      top: `${Math.min(selectRect.start.top, selectRect.end.top) - SELECT_BOX_PADDING}px`,
      left: `${Math.min(selectRect.start.left, selectRect.end.left) - SELECT_BOX_PADDING}px`,
      width: `${Math.abs(selectRect.start.left - selectRect.end.left) + SELECT_BOX_PADDING * 2}px`,
      height: `${Math.abs(selectRect.start.top - selectRect.end.top) + SELECT_BOX_PADDING * 2}px`,
      padding: `${SELECT_BOX_PADDING}px`,
    },
  };
});

watch(() => canvasWidth.value, (width) => {
  emit('update:temporaryWidth', width);
});
watch(() => canvasHeight.value, (height) => {
  emit('update:temporaryHeight', height);
});

defineExpose({
  selectItems,
});
</script>

<template>
  <div
    ref="canvasEl"
    class="h-canvas h-canvas-item relative box-content"
    :class="{ 'select-none': working || dragSelectBox.selecting }"
    :data-uname="CANVAS_UNAME"
    :style="{
      width: `${canvasWidth}px`,
      height: `${canvasHeight}px`
    }"
  >
    <div
      class="relative size-full bg-repeat"
      :class="editMode ? 'overflow-visible' : 'overflow-hidden'"
      :style="gridCoverStyle"
    >
      <div
        v-if="selectedItemsGroupPosition"
        :data-uname="ITEMS_GROUP_UNAME"
        class="h-canvas-item absolute cursor-move"
        :style="{
          top: `${selectedItemsGroupPosition.position.y}px`,
          left: `${selectedItemsGroupPosition.position.x}px`,
          width: `${selectedItemsGroupPosition.position.w}px`,
          height: `${selectedItemsGroupPosition.position.h}px`,
          zIndex: selectedItemsGroupPosition.layer - minLayer + 1
        }"
      >
        <slot
          name="selected-items"
          :items="activeItems"
          :working="working && mouseMoving"
        />
        <div
          v-show="working && mouseMoving"
          class="absolute left-0 top-0 size-full"
        />
      </div>
      <div
        v-for="(position, uname) in ghostItemsPosition"
        v-show="working && mouseMoving"
        :key="uname"
        class="h-canvas-ghost-item absolute border bg-gray-400 opacity-50"
        :style="{
          transition: 'top 0.2s ease, left 0.2s ease, width 0.2s ease, height 0.2s ease',
          top: `${position.position.y}px`,
          left: `${position.position.x}px`,
          width: `${position.position.w}px`,
          height: `${position.position.h}px`,
          zIndex: position.layer - minLayer + 1
        }"
      />
      <div
        v-for="(position, uname) in itemsPositions"
        :key="uname"
        v-observe-visibility="(v: boolean) => onItemVisibilityChange(uname, v)"
        class="h-canvas-item absolute border border-transparent"
        :data-uname="uname"
        :class="uiEditDisabledItems.includes(uname) ? {
          'hover:border-green-500': editMode && !dragSelectBox.selecting,
          '!border-green-500': overlappedItems.includes(uname),
          'opacity-50': activeItems.includes(uname) && working,
        } : {
          'hover:border-blue-500': editMode && !dragSelectBox.selecting,
          '!border-blue-500': overlappedItems.includes(uname),
          'opacity-50': activeItems.includes(uname) && working,
        }"
        :style="{
          top: `${position.position.y}px`,
          left: `${position.position.x}px`,
          width: `${position.position.w}px`,
          height: `${position.position.h}px`,
          zIndex: position.layer - minLayer + 1
        }"
      >
        <slot
          name="item"
          :uname="uname"
        >
          {{ uname }}
        </slot>

        <div
          v-show="working && mouseMoving"
          class="absolute left-0 top-0 size-full"
        />
      </div>
    </div>
    <CanvasAddons
      v-if="editMode"
      :ui-edit-disabled-items="uiEditDisabledItems"
      :drag-and-drop-disabled-items="dragAndDropDisabledItems"
      :canvas-width="width"
      :canvas-height="height"
      :positions="itemsPositions"
      :active-items="activeItems"
      :aligned-items="alignedItems"
      :alignment-lines="alignmentLines"
      :show-canvas-outline="showCanvasOutline"
      :zoom-level="zoomLevel"
      :items-group-position="selectedItemsGroupPosition"
      :working="working && mouseMoving"
      @resize-to-fit="side => resizeToFit(side)"
    />

    <Teleport
      v-if="dragSelectBox.selecting && container"
      :to="container"
    >
      <div
        class="absolute z-[1000]"
        :style="dragSelectBox.style"
      >
        <div
          class="size-full border border-blue-500 bg-blue-300 bg-opacity-25"
        />
      </div>
    </Teleport>
  </div>
</template>
