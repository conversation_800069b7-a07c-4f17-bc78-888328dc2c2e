// TODO: move to aml-std
import {
  reporting2, processParamValue,
  type DashboardDefinition, type DashboardUpdateEvent, SourceMapEventKind,
  type BlockTemplate, type TabLayout, type LinearLayout, type CanvasLayout,
  type DashboardBlock,
} from '@holistics/aml-std';
import { generateBlockUname } from './generateBlockUname';
import { suggestPositionForBlock } from './layouts';

// JSON -> AML code
function generateParamCode (param: { type: string, name: string }, value: any) {
  if (param.type.startsWith('Heredoc<')) {
    // get type inside Heredoc<type>
    const heredocType = param.type.slice(8, -1)
      .replaceAll('\'', '')
      .replaceAll('"', '');

    // NOTE: add sanitize (available in aml-std)
    return `@${heredocType} ${value} ;;`;
  }

  if (['Dataset', 'Number', 'Int', 'Boolean'].includes(param.type)) {
    return value;
  }

  if (param.type === 'VizFieldRef') {
    const { model, field } = value;
    if (!model) {
      return reporting2.createAmlString(field);
    }
    return `ref(${reporting2.createAmlString(model)}, ${reporting2.createAmlString(field)})`;
  }

  // default: treat as AML string
  return reporting2.createAmlString(String(value));
}

export function generateTemplateBlockCode (uname: string, template: BlockTemplate, params: Record<string, any>) {
  if (!template.params) {
    // static one
    return `block ${uname}: ${template.name}`;
  }

  const paramsCode = template.params.map((param) => {
    return generateParamCode(param, params[param.name]);
  });

  if (!paramsCode.length) {
    // no params
    return `block ${uname}: ${template.name}()`;
  }

  // with params
  return `block ${uname}: ${template.name}(
    ${paramsCode.join(',\n')}
  )`;
}

const DEFAULT_BLOCK_WIDTH: Record<DashboardBlock['type'], number> = {
  VizBlock: 400,
  TextBlock: 300,
  FilterBlock: 300,
  PopBlock: 300,
  DateDrillBlock: 300,
  ErrorBlock: 300,
};
const DEFAULT_BLOCK_HEIGHT: Record<DashboardBlock['type'], number> = {
  VizBlock: 300,
  TextBlock: 200,
  FilterBlock: 100,
  PopBlock: 100,
  DateDrillBlock: 100,
  ErrorBlock: 300,
};

interface GenerateAddTemplateBlockEventOptions {
  definition: DashboardDefinition
  parentLayout?: TabLayout
  currentLayout?: CanvasLayout | LinearLayout
}
export function generateAddTemplateBlockEvent (template: BlockTemplate, params: Record<string, any>, opts: GenerateAddTemplateBlockEventOptions): DashboardUpdateEvent[] {
  const { definition, parentLayout, currentLayout } = opts;
  const events: DashboardUpdateEvent[] = [];

  const uname = generateBlockUname(
    template.type.toLowerCase().charAt(0) as any,
    definition.blocks.map((block) => block.uname),
  );
  const code = generateTemplateBlockCode(uname, template, params);

  events.push({
    event: 'UpdateValues',
    sourceMapEvents: [{
      kind: SourceMapEventKind.Update,
      path: !definition.blocks.length ? ['block'] : ['block', uname],
      node: {
        kind: 'ElementProperty',
        astEdit: {
          text: code,
        },
      },
    }],
  });

  if (currentLayout?.type === 'CanvasLayout') {
    const newWidth = template.metadata.metadata?.blockWidth ?? DEFAULT_BLOCK_WIDTH[template.type];
    const newHeight = template.metadata.metadata?.blockHeight ?? DEFAULT_BLOCK_HEIGHT[template.type];
    const newPos = suggestPositionForBlock(currentLayout as CanvasLayout, newWidth, newHeight);

    events.push({
      event: 'UpdateCanvasBlockPosition',
      parent: parentLayout?.uname,
      canvasUname: currentLayout.uname,
      blockUname: uname,
      position: newPos,
    });
  }

  return events;
}

export function generateUpdateTemplateBlockEvent (blockUname: string, template: BlockTemplate, params: Record<string, any>): DashboardUpdateEvent[] {
  const events: DashboardUpdateEvent[] = [];

  const code = generateTemplateBlockCode(blockUname, template, params);

  events.push({
    event: 'UpdateValues',
    sourceMapEvents: [{
      kind: SourceMapEventKind.Update,
      path: ['block', blockUname],
      node: {
        kind: 'ElementProperty',
        astEdit: {
          text: code,
        },
      },
    }],
  });

  return events;
}

export function calculateParamsValues (template: BlockTemplate, params?: any[]) {
  return template.params?.reduce((acc, p, idx) => {
    acc[p.name] = processParamValue(p.type, params?.[idx]);
    return acc;
  }, {} as Record<string, any>) ?? {};
}
