// eslint-disable-next-line import/no-extraneous-dependencies
import { type VizSetting } from '@holistics/types';
import {
  type DashboardDefinition, type DashboardUpdateEvent, type Dataset, unifyDatasets,
  inferCustomInteractions,
} from '@holistics/aml-std';
import { check as checkFT } from '@holistics/feature-toggle';
import generateVizBlock from './generateVizBlock';
import { generateInteractionsForNewBlocks } from './interactions';
import { generatePositionEvent, getLayoutInfo } from './layouts';

export const vizSettingToDashboardUpdateEvents = ({
  dataset, vizSetting, description, label, dashboardDefinition,
}: {
  vizSetting: VizSetting,
  dataset: Dataset,
  label?: string,
  description?: string,
  dashboardDefinition: DashboardDefinition
}) => {
  const datasets = unifyDatasets([dataset]);

  const vizBlock = generateVizBlock({
    existingUnames: dashboardDefinition.blocks.map(({ uname }) => uname),
    label,
    description,
    datasetId: dataset.id,
    vizSetting,
  });

  const newInteractions = generateInteractionsForNewBlocks([vizBlock], dashboardDefinition);
  const customInteractions = inferCustomInteractions(
    dashboardDefinition.interactions.concat(...newInteractions).filter(i => i.type !== 'ErrorInteraction'),
    dashboardDefinition.blocks.concat(vizBlock),
  );

  const events: DashboardUpdateEvent[] = [{
    event: 'UpdateBlock' as const,
    uname: vizBlock.uname,
    block: vizBlock,
    datasets,
  }, {
    event: 'UpdateInteractions',
    interactions: customInteractions,
    datasets,
  }];

  const autoPlacementForNewBlockEnabled = checkFT('dashboards_v4:auto_placement_for_new_block');
  if (autoPlacementForNewBlockEnabled) {
    const layoutInfo = getLayoutInfo(dashboardDefinition);
    if (layoutInfo?.currentLayout) {
      const positionEvent = generatePositionEvent(layoutInfo.parentLayout, layoutInfo.currentLayout, vizBlock);
      if (positionEvent) {
        events.push(positionEvent);
      }
    }
  }

  return events;
};
