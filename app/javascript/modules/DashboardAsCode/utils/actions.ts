import { h } from 'vue';

import {
  type DashboardDefinition, type DatasetWithModels, type <PERSON>rrorBlock, type NonErrorInteraction, type TextBlock, type VizBlock, type DashboardUpdateEvent,
} from '@holistics/aml-std';
import type { DropdownOption } from '@holistics/design-system';
import { check as checkFT } from '@/core/services/featureToggle';
import { User } from '@/core/plugins/user';
import { canPerformMultipleSort } from '@/modules/Viz/utils/buildSortOptions';
import isUsageMonitoringEmbeddedDashboard from '@/modules/EmbedLinks/utils/isUsageMonitoringEmbeddedDashboard';
import type { BlockAction, IcBlock } from '../types';
import RefreshCache from '../components/blocks/customControls/RefreshCache.vue';
import { BlockActionName } from '../constants/blockActions';
import { IS_EMBED_LINK, IS_PUBLIC_LINK, IS_SHAREABLE_LINK } from '../constants/views';
import AskWitchButton from '../components/dashboard/AskWitch/AskWitchButton.vue';
import { getLayoutBlocks } from './layouts';

const user = new User();

export interface ActionsContext {
  editMode: boolean
  codeModeDisabled: boolean
  uiEditDisabled: boolean
  layoutActions: DropdownOption[]
  permissions?: {
    can_explore?: boolean
    can_export?: boolean
    can_export_data: boolean
  };
  expanded?: boolean
  isTemplatedBlock?: boolean
  hasTemplateParams?: boolean
}

function makeUpdateParamAction (hasTemplateParams: boolean): BlockAction {
  return {
    type: 'button',
    key: BlockActionName.EditTemplateParams,
    icon: 'pencil-param',
    tooltip: hasTemplateParams ? 'Edit parameter values' : 'No parameters to edit',
    buttonType: 'tertiary-default',
    actionName: BlockActionName.EditTemplateParams,
    dataCi: 'edit-block-btn',
    disabled: !hasTemplateParams,
  };
}

function makeAddToLibAction (): DropdownOption {
  // eslint-disable-next-line max-len
  const tooltipContent = 'Add to <a href="https://docs.holistics.io/docs/canvas-dashboard/guides/build-library-blocks" target="_blank">Block Library</a> to make your block discoverable, reusable and customizable';
  return {
    key: BlockActionName.AddToLibrary,
    label: 'Add to Block Library',
    icons: 'component',
    tooltip: {
      content: tooltipContent,
      html: true,
      arrow: false,
      distance: 8,
    },
  };
}

function makeDeleteAction (): DropdownOption {
  return {
    key: BlockActionName.Delete,
    label: 'Delete',
    icons: 'delete',
    class: 'text-red-500 delete-block-btn',
  };
}

export function vizBlockActions (block: VizBlock, context: ActionsContext) {
  const actions: BlockAction[] = [];

  // for view mode
  // more details can be found at https://holistics.slack.com/archives/C05QF57LX7E/p1716262214720909
  // still show explore btn for explorer, but it will show permission error when user click explore
  if (!context.editMode && (context?.permissions?.can_explore || user.isExplorer)) {
    actions.push({
      type: 'button',
      buttonType: 'tertiary-default',
      icon: 'explore',
      label: 'Explore',
      key: BlockActionName.Explore,
      tooltip: 'Explore data',
      actionName: BlockActionName.Explore,
      dataCi: 'explore-block-btn',
    });
  }

  if (checkFT('ask_witch:enabled') && !context.editMode && !IS_EMBED_LINK) {
    actions.push({
      type: 'custom',
      component: AskWitchButton,
      key: 'ask-witch',
    });
  }

  if (context.editMode && !context.expanded) {
    if (context.isTemplatedBlock && !context.codeModeDisabled) {
      actions.push(makeUpdateParamAction(!!context.hasTemplateParams));
    } else if (!context.uiEditDisabled) {
      actions.push({
        type: 'button',
        key: BlockActionName.Edit,
        icon: 'edit',
        tooltip: 'Edit visualization',
        buttonType: 'primary-highlight',
        actionName: BlockActionName.Edit,
        dataCi: 'edit-block-btn',
      });
    }
  }

  // IS_EMBED_LINK check: copied the old logic, not sure why we want to hide the copy button here
  if (context.expanded && !context.editMode && !IS_EMBED_LINK) {
    actions.push({
      type: 'portal',
      key: 'copy-block-url',
      portalId: 'copy-expanded-block-url',
    });
  }

  actions.push({
    type: 'portal',
    key: 'mapped-conditions',
    portalId: `block-${block.uname}-mapped-conditions`,
  });
  if (canPerformMultipleSort(block.viz.viz_setting.viz_type, true)) {
    actions.push({
      type: 'portal',
      key: 'table-sort',
      portalId: `block-${block.uname}-table-sort`,
    });
  }

  const dropdownOptions: DropdownOption[] = [];
  if (context.editMode) {
    dropdownOptions.push(
      {
        key: BlockActionName.Preferences,
        label: 'Block preferences',
        icons: 'configuration',
        class: 'ci-block-preferences',
      },
      {
        type: 'divider',
      },
    );

    if (!context.codeModeDisabled) {
      dropdownOptions.push({
        key: BlockActionName.ShowInCode,
        label: 'View Code',
        icons: 'code',
      });
      if (!context.uiEditDisabled && checkFT('dashboard_v4:block_library')) {
        dropdownOptions.push(makeAddToLibAction());
      }
    }

    dropdownOptions.push(
      {
        key: BlockActionName.Duplicate,
        label: 'Duplicate',
        icons: 'clone',
      },
      {
        key: BlockActionName.CopyTo,
        label: 'Copy to',
        icons: 'copy',
        class: 'ci-copy-block-to',
      },
      ...context.layoutActions,
      makeDeleteAction(),
      {
        type: 'divider',
      },
      {
        key: BlockActionName.RefreshCache,
        type: 'render',
        render ({ option, select }) {
          return h(RefreshCache, {
            uname: block.uname,
            onRefresh: () => select(option),
          });
        },
      },
    );
  } else {
    if (context?.permissions?.can_export && !IS_PUBLIC_LINK && (user.isAdmin || user.isAnalyst)) {
      dropdownOptions.push({
        key: BlockActionName.DataAlerts,
        label: 'Data Alerts',
        icons: 'bell-on',
      });
    }

    if (context?.permissions?.can_export_data && !(IS_SHAREABLE_LINK && checkFT('shareable_link:disable_export'))) {
      dropdownOptions.push(
        {
          key: 'export',
          label: 'Export',
          icons: 'arrow-to-bottom',
          class: 'ci-block-export',
          children: [{
            key: BlockActionName.ExportPdf,
            label: 'PDF',
            icons: 'file/pdf',
            class: 'ci-pdf-export',
          },
          {
            key: BlockActionName.ExportExcel,
            label: 'Excel',
            icons: 'file/excel',
            class: 'ci-xlsx-export',
          },
          {
            key: BlockActionName.ExportCsv,
            label: 'CSV (Raw Data)',
            icons: 'file/csv',
            class: 'ci-csv-export',
          },
          {
            key: BlockActionName.ExportCsvFormatted,
            label: 'CSV (Formatted Data)',
            icons: 'file/csv',
            class: 'ci-csv-formatted-data-export',
          },
          ],
        },
      );
    }

    if (!context?.permissions?.can_export_data && isUsageMonitoringEmbeddedDashboard()) {
      dropdownOptions.push({
        key: 'export',
        label: 'Export',
        icons: 'arrow-to-bottom',
        disabled: true,
        tooltip: `Export raw data in Usage Monitoring is available in higher plans.
          Please upgrade your plan to enjoy the feature. For further inquiries, <NAME_EMAIL>.`,
      });
    }
    // the 'embed_link:allow_public_user_bust_cache' feature toggle applies to both embed link and public link
    if (!IS_PUBLIC_LINK || checkFT('embed_link:allow_public_user_bust_cache')) {
      if (dropdownOptions.length > 0) {
        dropdownOptions.push({
          type: 'divider',
        });
      }
      dropdownOptions.push({
        key: BlockActionName.RefreshCache,
        type: 'render',
        render ({ option, select }) {
          return h(RefreshCache, {
            uname: block.uname,
            onRefresh: () => select(option),
          });
        },
      });
    }
  }

  if (dropdownOptions.length > 0) {
    const dropdown: BlockAction = {
      type: 'dropdown',
      key: 'dropdown',
      icon: 'ellipsis-horizontal',
      tooltip: 'More',
      buttonType: 'tertiary-default',
      dataCi: 'ci-more-dropdown',
      children: dropdownOptions,
    };
    actions.push(dropdown);
  }

  if (!context.expanded) {
    actions.push({
      type: 'button',
      key: BlockActionName.Expand,
      icon: 'expand-square',
      tooltip: 'Expand',
      buttonType: 'tertiary-default',
      actionName: BlockActionName.Expand,
      dataCi: 'expand-block-btn',
    });
  } else {
    actions.push({
      type: 'button',
      key: BlockActionName.Collapse,
      icon: 'cancel',
      tooltip: 'Collapse',
      buttonType: 'tertiary-default',
      actionName: BlockActionName.Collapse,
      dataCi: 'collapse-block-btn',
    });
  }

  return actions;
}

export function icBlockActions (block: IcBlock, context: ActionsContext) {
  const actions: BlockAction[] = [];

  if (context.editMode) {
    if (context.isTemplatedBlock && !context.codeModeDisabled) {
      actions.push(makeUpdateParamAction(!!context.hasTemplateParams));
    } else if (!context.uiEditDisabled) {
      actions.push({
        type: 'button',
        key: BlockActionName.Edit,
        icon: 'edit',
        tooltip: 'Edit control',
        buttonType: 'primary-highlight',
        actionName: BlockActionName.Edit,
        dataCi: 'edit-block-btn',
      });
    }

    const children: DropdownOption[] = [
      {
        key: BlockActionName.Preferences,
        label: 'Block preferences',
        icons: 'configuration',
        class: 'ci-block-preferences',
      },
      {
        type: 'divider',
      },
    ];
    if (!context.codeModeDisabled) {
      children.push({
        key: BlockActionName.ShowInCode,
        label: 'View Code',
        icons: 'code',
      });
      if (!context.uiEditDisabled && checkFT('dashboard_v4:block_library')) {
        children.push(makeAddToLibAction());
      }
    }
    children.push(
      {
        key: BlockActionName.Duplicate,
        label: 'Duplicate',
        icons: 'copy',
      },
      ...context.layoutActions,
      makeDeleteAction(),
    );

    actions.push({
      type: 'dropdown',
      key: 'dropdown',
      icon: 'ellipsis-horizontal',
      tooltip: 'More',
      buttonType: 'tertiary-default',
      children,
    });
  }

  return actions;
}

export function textBlockActions (block: TextBlock, context: ActionsContext) {
  const actions: BlockAction[] = [];

  if (context.editMode) {
    if (context.isTemplatedBlock && !context.codeModeDisabled) {
      actions.push(makeUpdateParamAction(!!context.hasTemplateParams));
    } else if (!context.uiEditDisabled) {
      actions.push({
        type: 'button',
        key: BlockActionName.StartInlineEdit,
        icon: 'edit',
        tooltip: 'Edit text',
        buttonType: 'primary-highlight',
        actionName: BlockActionName.StartInlineEdit,
        dataCi: 'edit-block-btn',
      });
    }

    const children: DropdownOption[] = [];
    if (!context.codeModeDisabled) {
      children.push({
        key: BlockActionName.ShowInCode,
        label: 'View Code',
        icons: 'code',
      });
      if (!context.uiEditDisabled && checkFT('dashboard_v4:block_library')) {
        children.push(makeAddToLibAction());
      }
    }
    children.push(
      {
        key: BlockActionName.Duplicate,
        label: 'Duplicate',
        icons: 'copy',
      },
      ...context.layoutActions,
      makeDeleteAction(),
    );

    actions.push({
      type: 'dropdown',
      key: 'dropdown',
      icon: 'ellipsis-horizontal',
      tooltip: 'More',
      buttonType: 'tertiary-default',
      children,
    });
  }
  return actions;
}

export function errorBlockActions (block: ErrorBlock, context: ActionsContext) {
  const actions: BlockAction[] = [];

  if (context.editMode) {
    if (!context.codeModeDisabled) {
      actions.push({
        type: 'button',
        actionName: BlockActionName.ShowInCode,
        buttonType: 'primary-highlight',
        key: BlockActionName.ShowInCode,
        tooltip: 'View Code',
        icon: 'code',
      });
    }

    actions.push({
      type: 'dropdown',
      key: 'dropdown',
      icon: 'ellipsis-horizontal',
      tooltip: 'More',
      buttonType: 'tertiary-default',
      children: [
        ...context.layoutActions,
        makeDeleteAction(),
      ],
    });
  }
  return actions;
}

export function createDeleteBlocksEvents (blocks: string[], definition: DashboardDefinition, datasets: DatasetWithModels[], layoutUname?: string) {
  const blocksInLayouts: Record<string, string[]> = {};
  (definition.views || []).forEach(layout => {
    if (layout.type === 'TabLayout') {
      layout.tabs.forEach(tab => {
        const blocksInTab = getLayoutBlocks(tab).filter(block => blocks.includes(block));
        blocksInTab.forEach(block => {
          if (!blocksInLayouts[block]) {
            blocksInLayouts[block] = [];
          }
          blocksInLayouts[block].push(tab.uname);
        });
      });
    } else {
      const blocksInLayout = getLayoutBlocks(layout).filter(block => blocks.includes(block));
      blocksInLayout.forEach(block => {
        if (!blocksInLayouts[block]) {
          blocksInLayouts[block] = [];
        }
        blocksInLayouts[block].push(layout.uname);
      });
    }
  });

  const blocksToDelete = layoutUname ? blocks.filter(block => {
    // block not in any layout
    if (!blocksInLayouts[block]) {
      return true;
    }
    // block is in only the target layout
    return blocksInLayouts[block].length === 1 && blocksInLayouts[block][0] === layoutUname;
  }) : blocks;
  const events: DashboardUpdateEvent[] = blocksToDelete.map(block => ({
    event: 'UpdateBlock',
    uname: block,
    block: null, // null = delete
  }));

  if (layoutUname) {
    // only delete reference to blocks in the target layout
    (definition.views || []).forEach(layout => {
      if (layout.type === 'CanvasLayout' && layout.uname === layoutUname) {
        blocks.forEach(block => {
          if (layout.blocks[block]) {
            events.push({
              event: 'UpdateCanvasBlockPosition',
              canvasUname: layout.uname,
              blockUname: block,
              position: null,
            });
          }
        });
      } else if (layout.type === 'TabLayout') {
        layout.tabs.forEach(tab => {
          if (tab.type === 'CanvasLayout' && tab.uname === layoutUname) {
            blocks.forEach(block => {
              if (tab.blocks[block]) {
                events.push({
                  event: 'UpdateCanvasBlockPosition',
                  parent: layout.uname,
                  canvasUname: tab.uname,
                  blockUname: block,
                  position: null,
                });
              }
            });
          }
        });
      }
    });
  } else {
    // delete reference to blocks in all layouts
    (definition.views || []).forEach(layout => {
      if (layout.type === 'CanvasLayout') {
        blocks.forEach(block => {
          if (layout.blocks[block]) {
            events.push({
              event: 'UpdateCanvasBlockPosition',
              canvasUname: layout.uname,
              blockUname: block,
              position: null,
            });
          }
        });
      } else if (layout.type === 'TabLayout') {
        layout.tabs.forEach(tab => {
          if (tab.type === 'CanvasLayout') {
            blocks.forEach(block => {
              if (tab.blocks[block]) {
                events.push({
                  event: 'UpdateCanvasBlockPosition',
                  parent: layout.uname,
                  canvasUname: tab.uname,
                  blockUname: block,
                  position: null,
                });
              }
            });
          }
        });
      }
    });
  }

  const interactions = (definition.interactions ?? []).filter(i => i.type !== 'ErrorInteraction') as NonErrorInteraction[];
  const newInteractions = interactions.filter(i => !blocksToDelete.includes(i.from) && !blocksToDelete.includes(i.to));
  events.push({
    event: 'UpdateInteractions',
    interactions: newInteractions,
    datasets,
  });

  return events;
}
