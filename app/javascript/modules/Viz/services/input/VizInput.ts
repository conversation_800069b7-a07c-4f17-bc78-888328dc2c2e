/* eslint-disable camelcase */
/* eslint-disable max-classes-per-file */
import {
  map, get, capitalize, pick, includes, cloneDeep,
} from 'lodash';
import type { VizSetting as VizSettingType } from '@holistics/types';
import { ServerPivotTableRenderer } from '@/modules/Table/utils/pivot/renderers';
import * as Ajax from '@/core/services/ajax';
import VizSetting from '@/modules/Viz/models/VizSetting';
import DashboardDrillthroughPath from '@/modules/Viz/models/DashboardDrillthroughPath';
import eventBus from '@/core/services/eventBus';
import processConditionalFormats from '@/modules/Viz/submodules/ConditionalFormat/utils/processConditionalFormats';
import { ExtractedValues } from '@/modules/Viz/utils/valuesExtractors/types';
import { ValueFormatter } from '@/raw/value_formatter_raw';
import { HIGHCHARTS_WEEKDAY_MAP, Weekday } from '@/modules/Viz/constants/highchartsWeekdayMap';
import {
  buildSortOptionsFromSortSettings,
  calculateFieldLabel, getDefaultColors,
  generateNoDataMessage, parseResultValues,
  convertToRgba,
} from '@/modules/Viz/utils';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import type { ProcessActionInput, ProcessActionResult } from '@/modules/Action/types';
import { TransposeManager, ROW_VALUE_LABEL_PLACEHOLDER, VIRTUAL_FIELD } from '@/modules/Table/services/TransposeManager';
import syncVizSettingWithSortOptions from '../syncVizSettingWithSortOptions';
import FieldResolver from '../FieldResolver';
import { generate } from '../viz.ajax';
import VizInputDataCache from './DataCache';
import { GenerateResultType } from './types/MetricSheetInput.types';

const isSortBeforeLimitEnabled = checkFeatureToggle('viz:sort_before_limit');

class VizInputError extends Error {
  options: Record<string, unknown>;

  query: string;

  constructor (error) {
    if (typeof error === 'string') {
      super(error);
    } else {
      const { options, query, message } = error;
      super(message);
      this.options = options;
      this.query = query;
    }
  }
}

export const createErrorMessage = (ajaxError = null, textError = ''): Record<string, unknown> => {
  const errorMsg = ajaxError ? (ajaxError.message || Ajax.errorMessageFromAjax(ajaxError)) : textError;
  const errorDetails = get(ajaxError, 'errorDetails', {});
  return {
    type: get(errorDetails, 'level', 'error'),
    message: errorMsg,
    errorDetails,
  };
};

export default class VizInput {
  isInfiniteScroll?: boolean;

  vizSetting: VizSettingType;

  rootModel?: Record<string, unknown>[];

  filters?: Record<string, unknown>;

  drilldowns?: any[];

  vizInputDataCache?: VizInputDataCache;

  /**
   * When this is true:
   *    We will ONLY update the Viz when the frontend cache is available and usable.
   *    Otherwise if the frontend cache is not available/usable, we will raise OUTDATED_VIZ_DATA_CACHE.
   */
  forceCachedOnFrontend?: boolean;

  isInWidget?: boolean;

  pagination?: Record<string, unknown>;

  exploring?: any;

  timePeriodFilterValue?: string;

  source?: any;

  vizInputData?: Record<string, unknown>;

  shouldSubmitGenerateJobId?: boolean;

  dataSet?: Record<string, unknown>;

  projectId?: number;
  filePath?: string;
  isProduction?: boolean;
  initialSelectedValues: ExtractedValues;
  drillthroughs: any[];

  drillthroughHandler?: any;

  crossFilterHandler?: any;

  onJobPoller?: any;
  onJobStatus?: any;
  onSubmitRes?: any;
  onJobDone?: any;
  onJobCanceled?: any;

  constructor (vizSetting, rootModel, {
    forceCachedOnFrontend,
    vizInputDataCache,
    timePeriodFilterValue = '',
    filters = {},
    drilldowns = [],
    widget = false,
    pagination,
    exploring = null,
    source = null,
    vizInputData,
    dataSet = null,
    projectId = null,
    filePath = null,
    isProduction = null,
    isInfiniteScroll = false,
    drillthroughHandler = null,
    crossFilterHandler = null,
    shouldSubmitGenerateJobId = true,
    onJobPoller = null,
    onJobStatus = null,
    onSubmitRes = null,
    onJobDone = null,
    onJobCanceled = null,
    initialSelectedValues = {},
    bindingJson = {},
    colorPalettes = [],
  }) {
    this.isInfiniteScroll = isInfiniteScroll;
    this.vizSetting = new VizSetting(vizSetting);
    this.rootModel = rootModel;
    this.filters = filters;
    this.drilldowns = drilldowns;
    this.vizInputDataCache = vizInputDataCache;
    this.forceCachedOnFrontend = forceCachedOnFrontend;
    this.isInWidget = !!widget;
    this.pagination = pagination;
    this.exploring = exploring;
    this.timePeriodFilterValue = timePeriodFilterValue;
    this.source = source;
    this.vizInputData = vizInputData; // vizInputData is pre-generated
    this.shouldSubmitGenerateJobId = shouldSubmitGenerateJobId;
    this.dataSet = dataSet;
    this.projectId = projectId;
    this.filePath = filePath;
    this.isProduction = isProduction;
    this.initialSelectedValues = initialSelectedValues;
    this.bindingJson = bindingJson;
    this.colorPalettes = colorPalettes;

    this.drillthroughs = [];
    this.drillthroughHandler = drillthroughHandler; // callback when there are possible drillthrough values
    this.crossFilterHandler = crossFilterHandler;
    this.onJobPoller = onJobPoller;
    this.onJobStatus = onJobStatus; // callback for job polling (to get status of number of pending jobs)
    this.onSubmitRes = onSubmitRes;
    this.onJobDone = onJobDone;
    this.onJobCanceled = onJobCanceled;
  }

  static DEFAULT_TABLE_INFINITE_SIZE = 100;

  // eslint-disable-next-line class-methods-use-this, no-unused-vars
  async generate (generatorOptions = {}): Promise<GenerateResultType> {
    throw new Error('Not implemented');
  }

  async getVizInput (generatorOptions = {}) {
    let vizInputData;
    try {
      vizInputData = await this.generate(generatorOptions);
    } catch (err) {
      if (err.message === 'OUTDATED_VIZ_DATA_CACHE') {
        throw err;
      }
      if (window.H.env === 'development') {
        // eslint-disable-next-line no-console
        console.log(err);
      }
      vizInputData = {
        options: get(err, 'options', null),
        query: get(err, 'query', ''),
        message: createErrorMessage(err),
      };
    }
    this.forceCachedOnFrontend = false;
    return {
      data: vizInputData,
      cache: this.vizInputDataCache,
    };
  }

  async generateVizInputData ({
    page = 1, pageSize = -1, sort = '-1_asc', sortByColumnType = false, showAggregated = false,
    bustBackendCache = false, bustFrontendCache = false, forceCachedOnBackend = false,
    hOtelContext = {}, batchSubmitGenerate = false,
  } = {}) {
    const appliedVizSetting = isSortBeforeLimitEnabled ? syncVizSettingWithSortOptions(this.vizSetting, sort) : this.vizSetting;

    const vizInputDataOptions = {
      viz_setting: appliedVizSetting,
      root_model_id: get(this, 'rootModel.id'),
      data_set_id: get(this, 'dataSet.id'),
      is_model_preview: get(this, 'dataSet.forModelPreview'),
      file_path: this.filePath,
      project_id: this.projectId,
      binding_json: this.bindingJson,
      options: {
        page,
        page_size: pageSize,
        show_aggregated: showAggregated,
        sort,
        sort_by_column_type: sortByColumnType,
        exploring: this.exploring,
        bust_cache: bustBackendCache,
        force_cached: forceCachedOnBackend,
      },
      source: this.source,
    };
    const isPgcacheModel = ['PgcacheModel', 'CanalLakeModel'].includes(get(this, 'rootModel.backend_type'));
    let vizInputData;
    if (!bustFrontendCache && this.canUseCacheFor(vizInputDataOptions)) {
      vizInputData = this.vizInputDataCache?.vizInputData;
    } else {
      vizInputData = this.vizInputData || await generate(vizInputDataOptions, {
        emitJobId: this.shouldSubmitGenerateJobId,
        onJobPoller: this.onJobPoller,
        onJobStatus: this.onJobStatus,
        onSubmitRes: this.onSubmitRes,
        onJobDone: this.onJobDone,
        onJobCanceled: this.onJobCanceled,
        hOtelContext,
        batchSubmitGenerate,
        isPgcacheModel,
      });
      this.vizInputDataCache = new VizInputDataCache(vizInputDataOptions, vizInputData);
    }

    if (vizInputData.error) {
      throw new Error(vizInputData.error);
    }

    this.drillthroughs = map(vizInputData.drillthroughs || [], (drillthroughPath) => new DashboardDrillthroughPath(drillthroughPath));

    return vizInputData;
  }

  // pageSize <= 0 for no pagination
  async sortPaginate ({
    page = -1, pageSize = -1, sort = '-1_asc', showAggregated = false, mergeData = null,
    bustBackendCache = false, bustFrontendCache = false, forceCachedOnBackend = false,
    hOtelContext = {}, batchSubmitGenerate = false,
  } = {}) {
    const response = await this.generateVizInputData({
      page,
      pageSize,
      sort,
      showAggregated,
      bustBackendCache,
      bustFrontendCache,
      forceCachedOnBackend,
      hOtelContext,
      batchSubmitGenerate,
    });

    if (
      get(response, 'generated.result.values', []).length === 0
      && (!mergeData || pageSize === 1)
    ) {
      throw new VizInputError({
        options: {
          meta: {
            lastCacheUpdated: get(response, 'generated.result.meta.last_cache_updated', null),
          },
        },
        query: get(response, 'generated.extra_details.sql', ''),
        message: generateNoDataMessage(this.vizSetting.filters),
      });
    }

    response.generated.result.values = map(response.generated.result.values, (row) => parseResultValues(
      row,
      response.generated.result.column_types || [],
      { number: this.shouldParseNumberValues },
    ));

    const { timePeriodFilterValue } = this;

    if (mergeData) {
      response.generated.result.values = [...mergeData, ...response.generated.result.values];
    }
    const generated = {
      data: response.generated.result,
      chartTitle: response.generated.extra_details.chart_title,
      fields: map(response.generated.extra_details.fields, field => {
        if (field.format.type === 'date' && timePeriodFilterValue) {
          field.format.sub_type = ValueFormatter.FORMATS.DATE[capitalize(timePeriodFilterValue)] || field.format.sub_type;
        }
        return field;
      }),
      query: response.generated.extra_details.sql,
      executedAql: response?.generated?.result?.meta?.executed_aql,
      meta: get(response, 'generated.result.meta', {}),
      actions: response.generated.extra_details.actions || [],
    };
    generated.data.fields = map(generated.fields, calculateFieldLabel);

    const processActionParams: ProcessActionInput = {
      actions: generated.actions,
      detailedFields: generated.fields,
      values: generated.data.values,
      fields: generated.data.fields,
    };
    const { dataWithActions, nonActionFields } = this.processAction(processActionParams);
    generated.data.values = dataWithActions;
    generated.data.fields = nonActionFields;

    return generated;
  }

  // eslint-disable-next-line class-methods-use-this
  defaultColors () {
    return getDefaultColors();
  }

  async pivot ({
    page = 1, pageSize = -1, sortByColumnType = false, sort = null,
    bustBackendCache = false, bustFrontendCache = false, forceCachedOnBackend = false,
    hOtelContext = {}, batchSubmitGenerate = false,
  } = {}) {
    const response = await this.generateVizInputData({
      page,
      pageSize,
      sort,
      sortByColumnType,
      bustBackendCache,
      bustFrontendCache,
      forceCachedOnBackend,
      hOtelContext,
      batchSubmitGenerate,
    });

    if (get(response, 'generated.result.data.1', []).length === 0) {
      throw new VizInputError({
        options: {
          meta: {
            lastCacheUpdated: get(response, 'generated.result.meta.last_cache_updated', null),
          },
        },
        query: get(response, 'generated.extra_details.sql', ''),
        message: generateNoDataMessage(this.vizSetting.filters),
      });
    }

    return this.postPivot(response.generated);
  }

  async pivotMore ({
    page = 1, pageSize = -1, sortByColumnType = false, prePivotData = null, sort = null,
    bustBackendCache = false, bustFrontendCache = false, forceCachedOnBackend = false,
  } = {}) {
    const response = await this.generateVizInputData({
      page,
      pageSize,
      sortByColumnType,
      sort,
      bustBackendCache,
      bustFrontendCache,
      forceCachedOnBackend,
    });

    return this.postPivot(response.generated, prePivotData);
  }

  postPivot (generatedResponse, prePivotData = null) {
    const { result, extra_details: extraDetails } = generatedResponse;
    const { explore_opts, chart_title: chartTitle } = extraDetails;
    const { pivot_opts, meta } = result;

    let exploreOpts = explore_opts;
    let pivotOpts = pivot_opts;

    // display measure labels on row
    const valueLabelsPos = this?.vizSetting?.settings?.misc?.value_labels_position;
    const isValueLabelsOnRow = TransposeManager.checkValueLabelsOnRow(valueLabelsPos, pivotOpts.measures?.length);
    let transposeManager;

    if (isValueLabelsOnRow) {
      exploreOpts = cloneDeep(exploreOpts);
      pivotOpts = cloneDeep(pivotOpts);
      const isEmptyRows = pivotOpts.rows.length === 0;

      // we are adding a virtual field to rows -> need to update indexes
      pivotOpts.rows ||= [];
      pivotOpts.rows.push(pivotOpts.rows.length);
      pivotOpts.columns = pivotOpts.columns.map((c: number) => c + 1);
      pivotOpts.measures = pivotOpts.measures.map((m: number) => m + 1);

      transposeManager = new TransposeManager({
        valueLabelsPos,
        pivotOpts,
      });
      const { valueLabelsIndex } = transposeManager;

      // add the virtual field to row
      exploreOpts.rows.splice(valueLabelsIndex, 0, VIRTUAL_FIELD);

      // mutation incoming
      // this is used to get the columns data types in ServerPivotTableRenderer#_computeBodyData
      // because we added a virtual field, also need to add it here so all columns types correct
      if (!this.colMetadataContainsValueLabelPlaceholder(result.data[2].columns) && !isEmptyRows) {
        result.data[2].columns.splice(
          valueLabelsIndex,
          0,
          { original_col_name: ROW_VALUE_LABEL_PLACEHOLDER, cache_col_name: ROW_VALUE_LABEL_PLACEHOLDER, cache_col_type: 'string' },
        );
      }
    }

    const totalRowFields = get(exploreOpts, 'rows.length', 1);
    const columnFreeze = this.vizSetting.settings?.misc?.column_freeze ?? totalRowFields;
    const convertNullToZero = get(this.vizSetting, 'settings.misc.convert_null_to_zero', false);
    const conditionalFormats = get(this.vizSetting, 'settings.conditional_formatting', []);
    const measures = get(exploreOpts, 'measures', []);
    const processedConditionalFormats = processConditionalFormats(conditionalFormats, measures);
    const columnWidth = get(this.vizSetting, 'settings.misc.column_width', {});

    const fieldResolver = this.dataSet
      ? new FieldResolver(this.rootModel, this.dataSet.dataModels, this.dataSet.relatedJoins, null, undefined, this.dataSet.metrics)
      : null;

    const generated = {
      ...result,
      pivotOpts,
      exploreOpts,
      chartTitle,
    };

    // passing current data to withVizInputData -> no need to rerun computed metadata
    const data = ServerPivotTableRenderer.withVizInputData(generated, {
      conditionalFormats: processedConditionalFormats,
      prePivotData,
      columnFreeze,
      convertNullToZero,
      parseNumberValues: this.shouldParseNumberValues,
      fieldResolver,
      meta,
      columnWidth,
      transposeManager,
    });

    eventBus.$emit('vizInput:pivoted', { vizSettingId: this.vizSetting.id, data });
    return {
      ...generated,
      data,
      query: generatedResponse.extra_details.sql,
      executedAql: generated?.meta?.executed_aql ?? '',
    };
  }

  // Whether the number values in the server-side viz data should be parsed before passing into frontend processing
  // https://github.com/holistics/holistics/pull/4034#discussion_r517085702
  get shouldParseNumberValues () {
    return true;
  }

  // eslint-disable-next-line class-methods-use-this
  getColor (col, value): string | undefined {
    let color: string;
    const seriesSettings = col.series_settings;
    if (value === undefined) {
      // no series value => use col color directly
      // Note: col.color is legacy color, use series_settings.color instead
      color = get(seriesSettings, 'color');
      if (!color || color === 'auto') {
        color = col.color || 'auto';
      }
    } else {
      // has series value => use series color
      color = get(seriesSettings, ['series_hash', value, 'color'], 'auto');
    }

    if (color === 'auto') {
      return undefined; // let highcharts pick the color
    }

    // convert to rgba to prevent wrong color issue in exported png/jpg
    // ref: https://app.asana.com/0/0/1200852968316728/1201357172487971/f
    return convertToRgba(color);
  }

  // eslint-disable-next-line class-methods-use-this
  isSeriesHidden (col, value) {
    return get(col.series_settings, ['series_hash', value, 'hidden'], false);
  }

  // eslint-disable-next-line class-methods-use-this
  tenantWeekStartDay () {
    const weekStartDay = get(this.vizInputDataCache, 'vizInputData.generated.extra_details.week_start_day') || window.H.current_tenant.settings.week_start_day || Weekday.Monday;
    return HIGHCHARTS_WEEKDAY_MAP[weekStartDay];
  }

  // eslint-disable-next-line class-methods-use-this
  tooltipOptions () {
    return {
      padding: 12,
      backgroundColor: 'rgba(57, 62, 66, 1)',
      borderColor: 'transparent',
      borderRadius: 8,
      shadow: false,
      outside: true,
      style: {
        color: '#fff',
      },
    };
  }

  buildSortOptionForChart () {
    if (includes(this.vizSetting.viz_type, 'chart')) {
      return pick(this.vizSetting.settings.sort, ['isAscending', 'option']);
    }
    return {};
  }

  buildSortOptionsForTable () {
    return buildSortOptionsFromSortSettings(this.vizSetting.viz_type, get(this.vizSetting, 'settings.sort'), !!this.dataSet);
  }

  canUseCacheFor (vizInputDataOptions) {
    let canUseCache;
    if (!this.vizInputDataCache) {
      canUseCache = false;
    } else {
      canUseCache = this.vizInputDataCache.canBeUsedFor(vizInputDataOptions);
    }
    if (canUseCache) {
      return true;
    }
    if (this.forceCachedOnFrontend) {
      throw new Error('OUTDATED_VIZ_DATA_CACHE');
    }
    return false;
  }

  /**
   * this function mutate the params to avoid data duplication
   * @param options
   */
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-unused-vars
  processAction (params: ProcessActionInput): ProcessActionResult {
    // re-implement to process action for other viz type
    return {
      dataWithActions: params.values,
      nonActionFields: params.fields,
    };
  }

  // eslint-disable-next-line class-methods-use-this
  colMetadataContainsValueLabelPlaceholder (cols: any[]) {
    return cols.some((c: any) => c.original_col_name === ROW_VALUE_LABEL_PLACEHOLDER);
  }
}
