/* eslint-disable import/no-extraneous-dependencies */
import { PathHash, VizField } from '@holistics/types';
import { POP_GROWTH_RATE_PREFIX, POP_PREFIX } from '@/modules/Viz/utils/pop';
import VizSetting from '../models/VizSetting';

type DetailField = VizField & {
  original_path_hash: {
    field_name: string
    model_id: number
  }
  name: string
}

type PathHashKey = keyof PathHash;

export function backfillUUIDs (detailFields: DetailField[], vizSettingFields: VizField[]) {
  if (!detailFields || !vizSettingFields) {
    return detailFields;
  }

  // Function to create a standardized key from path_hash or original_path_hash object
  function getPathKey (hash: PathHash | null | undefined) {
    if (!hash) return undefined;
    const keys = Object.keys(hash).sort(); // Sort the keys to ignore order
    return keys.map((key: string) => `${key}:${hash[key as PathHash<PERSON><PERSON>]}`).join('|');
  }

  // Create a map for quick lookup of UUIDs in vizSettingFields
  const vizSettingMap = new Map();
  vizSettingFields.forEach(field => {
    if (!field) return;
    // gen a standardized key from path_hash
    const pathKey = getPathKey(field.path_hash);
    if (!pathKey) return;

    const popPathKey = `${POP_PREFIX}${pathKey}`;
    const popGrPathKey = `${POP_GROWTH_RATE_PREFIX}${pathKey}`;

    if (!vizSettingMap.has(pathKey)) {
      vizSettingMap.set(pathKey, []);
      vizSettingMap.set(popPathKey, []);
      vizSettingMap.set(popGrPathKey, []);
    }

    // Push the field into the appropriate arrays
    vizSettingMap.get(pathKey).push(field);
    vizSettingMap.get(popPathKey).push({ ...field, uuid: `${POP_PREFIX}${field.uuid}` });
    vizSettingMap.get(popGrPathKey).push({ ...field, uuid: `${POP_GROWTH_RATE_PREFIX}${field.uuid}` });
  });

  // Update UUIDs in detailFields
  detailFields.forEach(detailField => {
    if (!detailField) return;

    // Determine the correct path key based on the field name
    let originalPathKey = getPathKey(detailField.original_path_hash || detailField.path_hash);
    if (!originalPathKey) return;

    // Determine if the field is a pop field
    const isPopField = detailField.name.startsWith(POP_PREFIX);
    const isGrPopField = detailField.name.startsWith(POP_GROWTH_RATE_PREFIX);

    if (isPopField && !isGrPopField) {
      originalPathKey = `${POP_PREFIX}${originalPathKey}`;
    } else if (isGrPopField) {
      originalPathKey = `${POP_GROWTH_RATE_PREFIX}${originalPathKey}`;
    }

    // Get the matching vizSettingFields array
    const vsArr = vizSettingMap.get(originalPathKey);
    if (vsArr && vsArr.length > 0) {
      const vs = vsArr.shift(); // Get and remove the first element
      detailField.uuid = vs.uuid; // Update the UUID
    }
  });

  return detailFields;
}

export function backFillUuidsForPivotTableAndDataTable (options: Record<string, any>, vizSetting: VizSetting, type: string) {
  if (!vizSetting) return options;
  // Case cached data. the uuid not changed, should update in return data
  if (type === 'pivot_table') {
    if (options.exploreOpts) {
      backfillUUIDs(options.exploreOpts?.rows || [], vizSetting.fields?.pivot_data?.rows || []);
      backfillUUIDs(options.exploreOpts?.columns || [], vizSetting.fields?.pivot_data?.columns || []);
      backfillUUIDs(options.exploreOpts?.measures || [], vizSetting.fields?.pivot_data?.values || []);
    }
  } else if (type === 'data_table') {
    if (vizSetting.fields?.table_fields && vizSetting.fields?.table_fields.length > 0 && options.detailedFields && options.detailedFields.length > 0) {
      backfillUUIDs(options.detailedFields || [], vizSetting.fields?.table_fields || []);
    }
  }
  return options;
}
