<template>
  <VizResultContextMenuWrapper
    ref="vizResult"
    v-h-loading.body="{ value: showLoadingOverlay, steps: loadingSteps }"
    v-observe-visibility="observeVisibilityOpts"
    class="result-viz"
    :class="{ 'has-border': hasBorder, 'viz-result-loading-container': isOverloaded }"
    :viz-setting="renderVizSetting ?? vizSetting"
    :data-set="dataSet"
    :explore-mode="exploreMode"
    :edit-mode="editMode"
    :date-drill="dateDrill"
    :show-date-drill-context-menu="showDateDrillContextMenu"
    :can-drillthrough="canDrillthrough"
    :drillthroughs="drillthroughs"
    :drill-values="drillValues"
    :zoom-level="zoomLevel"
    :can-drill-down="canDrillDown"
    :can-view-underlying-data="canViewUnderlyingData"
    :is-production="isProduction"
    :has-pending-render="hasPendingRender || updating"
    :drill-down-metadata="drillDownMetadata"
    :is-v4="!!isV4"
    @drill-down="(data) => emit('drillDown', data)"
    @date-drill="performDateDrill"
    @drillthrough="onDrillthrough"
    @explore="onExplore"
  >
    <div
      v-if="showOverloaded"
      class="job-queue-status-container"
    >
      <component
        :is="jobQueueStatusComponent.name"
        v-bind="jobQueueStatusComponent.attrs"
        :total-pending-jobs="pendingJobsCount"
        :auto-show="false"
        @cancel-refresh="emit('cancelRefresh')"
      />
    </div>
    <template v-if="vizOptions">
      <div
        v-if="shouldShowDateDrillDropdown"
        class="absolute flex items-center"
      >
        <VizDateDrill
          mount-to-body
          :options="dateDrill.dateDrillOptions"
          :model-value="dateDrill.currentValue"
          @update:model-value="performDateDrill"
        />
      </div>
      <VizMessage
        v-if="hasErrorOrWarning && !isLoading"
        :on-boarding-viz-config="onBoardingVizConfig"
        :viz-message="vizMessage"
        :show-error-survey="showErrorSurvey"
        :data-source-id="dataSourceId"
        :is-edit-mode="editMode"
        :is-explore-mode="exploreMode"
        :dataset="dataSet"
        :is-from-dataset-preview="isFromDatasetPreview"
        :error-action="errorAction"
        :can-add-relationship="canAddRelationship"
        @add-relationship="emit('addRelationship')"
        @undo-changes="emit('undoChanges')"
      />
      <div
        v-else-if="!hasErrorOrWarning && shouldRenderWidget"
        class="ci-viz-result min-h-0 min-w-0 flex-1 overflow-hidden"
      >
        <GeoHeatmapMigratingAlert
          v-if="shouldShowGeoMigrationAlert"
          class="mb-2"
          @dismissed="showMigrationNotice = false"
          @migrate-legacy-geo-heatmap="migrateLegacyGeoHeatmap"
        />
        <VizTableWrapper
          v-if="vizComponent === 'data_table'"
          ref="table"
          disable-observe-visibility
          class="size-full"
          :updating="updating"
          :options="vizOptions"
          :widget="options.widget"
          :source="source"
          :sort="options.sort"
          :no-stretch="extraOptions.tableNoStretch"
          :infinite-scroll="isInfiniteScrollEnabled"
          :is-dataset-viz="!!dataSet"
          :data-set="dataSet!"
          :viz-setting="vizSetting"
          :explore-mode="editMode || exploreMode"
          :parent-hspan="parentHspan"
          :apply-column-width="applyColumnWidth"
          :has-pending-render="hasPendingRender || updating"
          :is-production="isProduction"
          :show-field-details="showFieldDetails"
          :interaction-config="interactionConfig"
          :viz-theme="vizTheme"
          :showing-out-of-sync-banner="showingOutOfSyncBanner"
          @sort="onSortChanged"
          @page-size="onPageSizeChanged"
          @viz-table-updated="tableUpdated"
          @drillthrough-values="setDrillValues"
          @metric-sheet-setting-changed="onMetricSheetSettingChanged"
          @rendered="handleRendered"
          @table-interaction="onVizSettingTransform"
        />
        <AnnotatedHighcharts
          v-else-if="options.showAnnotations && vizComponent === 'highcharts'"
          ref="highcharts"
          :viz-options="vizOptions"
          :parent-hspan="parentHspan"
          :edit-mode="(editMode || exploreMode) && !!vizSetting.id"
          @rendered="handleRendered"
        />
        <Highcharts
          v-else-if="!options.showAnnotations && vizComponent === 'highcharts'"
          ref="highcharts"
          class="ci-highcharts h-full"
          :options="vizOptions"
          :edit-mode="(editMode || exploreMode) && !!vizSetting.id"
          :parent-hspan="parentHspan"
          :allow-remove-sort="allowRemoveHighchartSort"
          @sort="onSortChanged"
          @rendered="handleRendered"
          @remove-sort="onRemoveSort"
          @selection="handleHcSelection"
        />
        <ConversionFunnel
          v-else-if="vizComponent === 'conversion_funnel'"
          ref="funnel"
          :options="vizOptions"
          :explore-mode="editMode || exploreMode"
          :parent-hspan="parentHspan"
          @rendered="handleRendered"
        />
        <NewConversionFunnel
          v-else-if="vizComponent === 'new_conversion_funnel'"
          ref="newFunnel"
          :options="vizOptions"
          :widget="options.widget"
          :parent-hspan="parentHspan"
          @set-drill-values="setDrillValues"
          @rendered="handleRendered"
          @cross-filter="onCrossFilter"
        />
        <RetentionHeatmap
          v-else-if="vizComponent === 'retention_heatmap'"
          ref="cohort"
          :fields="vizOptions.fields"
          :values="vizOptions.values"
          :settings="vizOptions.settings"
          :colors="vizOptions.colors"
          :auto-width="!options.container"
          :has-cohort-size="vizOptions.hasCohortSize"
          :parent-hspan="parentHspan"
          @rendered="handleRendered"
        />
        <GeoHeatmap
          v-else-if="vizComponent === 'geo_heatmap'"
          ref="geo"
          :edit-mode="editMode"
          :fields="vizOptions.fields"
          :points="vizOptions.points"
          :settings="vizOptions.settings"
          :parent-hspan="parentHspan"
          @rendered="handleRendered"
        />
        <MetricKpiWrapper
          v-else-if="vizComponent === 'metric_kpi'"
          :use-new-metric-kpi="useNewMetricKpi"
          :show-border="(editMode || exploreMode) && !options.widget"
          :viz-options="vizOptions as MetricKpiOptions"
          :parent-hspan="parentHspan"
          @rendered="handleRendered"
          @drill-values="setDrillValues"
        />
        <HMap
          v-else-if="vizComponent === 'map'"
          :options="vizOptions"
          :edit-mode="editMode || exploreMode"
          :parent-hspan="parentHspan"
        />
        <VegaChart
          v-else-if="vizComponent === 'vega'"
          ref="vega"
          :spec="vizOptions.spec"
          :metadata="vizOptions.meta"
          :selected-values="selectedValues"
          :parent-hspan="parentHspan"
          @cm-values="setDrillValues"
          @cf-values="onCrossFilter"
          @rendered="handleRendered"
          @error-while-rendering="handleErrorWhileRendering"
        />
        <span
          v-else
          class="text-gray-500"
        >
          Not implemented: <b>{{ vizComponent }}</b>{{ logInvalidVizComponent(vizComponent) }}
        </span>
      </div>
      <VizMessage
        v-if="vizMessage && vizMessage.type === 'info'"
        :viz-message="vizMessage.message"
        :is-explore-or-edit-mode="editMode || exploreMode"
      />
    </template>
    <VizOnboarding
      v-else-if="showOnboarding"
      v-bind="onBoardingVizConfig"
    />
  </VizResultContextMenuWrapper>
</template>

<script setup lang="ts">
/* eslint-disable no-use-before-define */

import {
  ref, computed, watch, nextTick, onMounted, onBeforeUnmount, provide, inject, toRef,
  useTemplateRef,
} from 'vue';
import {
  assign, cloneDeep, debounce, get, isEmpty, isEqual, merge,
  omit,
} from 'lodash';
import pDebounce from 'p-debounce';
import { elementResizeDetector as erd } from '@holistics/utils/ui';
import dateOrNull from '@holistics/utils/dateOrNull';
import { generateUuid } from '@holistics/utils';
import { DateTime } from 'luxon';
import { fetchAmlBindingFuncKey } from '@aml-studio/client/context';
import VizTableWrapper from '@/modules/Viz/components/result/VizTableWrapper.vue';
import VizOnboarding from '@/modules/Viz/components/result/Onboarding.vue';
import ConversionFunnel from '@/modules/Viz/components/result/ConversionFunnel.vue';
import NewConversionFunnel from '@/modules/Viz/components/result/NewConversionFunnel.vue';
import RetentionHeatmap from '@/modules/Viz/components/result/RetentionHeatmap.vue';
import MetricKpiWrapper from '@/modules/Viz/components/result/MetricKpiWrapper.vue';
import AnnotatedHighcharts from '@/modules/Viz/components/result/AnnotatedHighcharts.vue';
import HMap from '@/modules/Viz/components/result/Map.vue';
import Highcharts from '@/vue_components/highcharts.vue';
import GeoHeatmap from '@/vue_components/geo_heatmap.vue';
import VegaChart from '@/modules/CustomCharts/components/VegaChart.vue';
import VizDateDrill from '@/modules/Viz/components/result/DateDrill.vue';
import VizInputGenerator from '@/modules/Viz/services/VizInputGenerator';
import {
  createStoreAdhocSettingsEventName,
  isConversionFunnelTypes,
  isNoDataMessage,
  shouldShowDataTable,
  shouldUpdateDateDrill,
  shouldUpdateDateDrillIC,
  vizType2Component,
} from '@/modules/Viz/utils';
import VizMessage from '@/modules/Viz/components/result/VizMessage.vue';
import { fetchJobParsedQuery } from '@/modules/Jobs/services/jobs.ajax';
import { errorMessageFromAjax } from '@/core/services/ajax';
import {
  DATA_TABLE, GEO_HEATMAP, PIVOT_TABLE, PIE_CHART,
} from '@/modules/Viz/constants/vizTypes';
import migrateLegacyGeoHeatmapService from '@/modules/Viz/services/migrateLegacyGeoHeatmap';
import GeoHeatmapMigratingAlert from '@/modules/Viz/components/result/GeoHeatmapMigratingAlert.vue';
import JobQueueStatus from '@/modules/Jobs/components/JobQueueStatus.vue';
import WidgetJobQueueStatus from '@/modules/DynamicDashboards/components/widgets/WidgetJobQueueStatus.vue';
import isFieldError from '@/modules/Viz/utils/isFieldError';
import { trackDateDrill, TrackingInfo } from '@/modules/Viz/utils/dateDrill/trackDateDrill';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import eventBus, { createEventBus } from '@/core/services/eventBus';
import DateDrill from '@/modules/Viz/services/DateDrill';
import { HSpan, wrapFuncWithinSpan } from '@/modules/HOtel/services/tracing';
import { isActiveJob, isCanceled } from '@/modules/Jobs/helpers/checkStatus';
import getQueryRunningStepMessage from '@/modules/Jobs/helpers/getQueryRunningStepMessage';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { User } from '@/core/plugins/user';
import type { DataModel, SortSetting } from '@holistics/types';
import type DataSet from '@/modules/DataSets/models/DataSet';
import { PivotTablePropData } from '@/modules/Table/types/propData';
import { Job } from '@/modules/Jobs/types/job';
import type { DrillDownData } from '@/modules/DashboardAsCode/utils/DrillDownManager';
import type { DrillHistoryItem } from '@/modules/Viz/utils/drillDown/buildDrillDimensionOptions';
import { VizTheme } from '@holistics/aml-std';
import allowDrillDownOnVizType from '@/modules/Viz/utils/drillDown/allowDrillDownOnVizType';
import { useDevDrillthroughs } from '../composables/useDevDrillthroughs';
import VizResultContextMenuWrapper from './result/contextMenu/VizResultContextMenuWrapper.vue';
import { buildVizConditions } from '../utils/crossFiltering';
import trackVizError from '../services/trackVizError';
import { applyDateDrillConditionToVizSetting } from '../utils/dateDrill/applyDateDrillConditionToVizSetting';
import { ExtractedValues } from '../utils/valuesExtractors/types';
import { VizInputMessage } from '../services/types';
import { MetricKpiOptions } from '../types/metricKpi';
import DashboardDrillthroughPath from '../models/DashboardDrillthroughPath';
import { AdhocSettings, HighchartsSelectionPos } from '../types/adhocSettings';
import {
  Source, RenderedPayload,
  OnBoardingVizConfig,
  WidgetOptions,
  LoadingOptions,
} from '../types/vizResult';
import { useVizResultLoading } from '../composables/useVizResultLoading';
import { INTERACTION_PRESETS, InteractionConfig, InteractionPresetType } from '../types/interactionPreset';
import {
  MetricSheetSettingChangedPayload, VizSettingTransformation, VizSettingInteraction, type VizSetting,
  TransformationPatch,
} from '../types/vizSettingTransformation';
import { InteractionName } from '../constants/interaction';
import { getVizSettingPatchesByInteraction, getVizSettingPatchesUsingDiff, transformVizSettingByInteraction } from '../services/transformVizSetting';
import { checkSignificantChangeInVizSetting } from '../services/checkSignificantChangeInVizSetting';
import type { VizFeCacheManager } from '../services/cache/VizFeCacheManager';

const RESIZE_MIN_THRESHOLD = 10;

const user = new User();

// Feature toggles
const enableInfiniteScroll = checkFeatureToggle('table:infinite_scroll');
const crossFilterEnabled = checkFeatureToggle('crossfilter:enabled');
const drillthroughEnabled = checkFeatureToggle('drillthrough:enabled');
const pageTracingEnanbled = checkFeatureToggle('h_otel:page_tracing') && !window.H?.is_mobile;
const allowSaveColumnWidth = checkFeatureToggle('ag-grid:allow_save_column_width_size');
const persistAdhocSettings = checkFeatureToggle('viz_result:persist_adhoc_settings');
const isSortBeforeLimitEnabled = checkFeatureToggle('viz:sort_before_limit');
const batchSubmitGenerate = checkFeatureToggle('viz:batch_submit_generate');
const showFriendlyAqlCompileError = checkFeatureToggle('table:show_friendly_aql_compile_error');
const useNewMetricKpiEnabled = checkFeatureToggle('metric_kpi:new_renderer_for_v4');
const utilizeFeProcessing = checkFeatureToggle('aml_studio:utilize_fe_processing');
const persistAdhocInteractions = checkFeatureToggle('viz_interaction:persist_adhoc_actions');
const isVUDFeaturesEnabled = checkFeatureToggle('drill_features:view_underlying_data');
const isDrillDownFeaturesEnabled = checkFeatureToggle('drill_features:drill_down');

interface Props {
  vizSetting: VizSetting;

  onBoardingVizConfig?: OnBoardingVizConfig;
  shouldShowOverlayUpdating?: boolean;
  options?: WidgetOptions;
  updateTrigger?: 'manual' | 'auto';
  dataModel?: DataModel | null;
  dataSet?: DataSet | null;
  projectId?: number | null;
  filePath?: string | null;
  isProduction?: boolean;
  editMode?: boolean;
  exploreMode?: boolean;
  forceCachedOnFrontend?: boolean;
  source: Source;
  infiniteScroll?: boolean;
  allowDrillthrough?: boolean;
  allowCrossFilter?: boolean;
  showErrorSurvey?: boolean;
  selectedValues?: {
    rows: any[];
    columns: any[];
  };
  showDateDrillContextMenu?: boolean;
  showDateDrillDropdown?: boolean;
  loadingOptions?: LoadingOptions;
  parentHspan?: HSpan | null;
  isFromDatasetPreview?: boolean;
  refreshCancelable?: boolean;
  cancelingRefresh?: boolean;
  zoomLevel?: number;
  canAddRelationship?: boolean;
  adhocSettings?: AdhocSettings | null;
  showFieldDetails?: boolean;
  allowViewUnderlyingData?: boolean
  allowDrillDown?: boolean
  drillDownMetadata?: {
    previousDrilledDims: DrillHistoryItem[];
    relatedVizSettings: VizSetting[];
  }
  interactionConfig?: InteractionPresetType | InteractionConfig
  vizTheme?: VizTheme;
  cacheManager?: VizFeCacheManager;
  cacheKey?: string;
  showingOutOfSyncBanner?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  onBoardingVizConfig: () => ({
    showGetResultHeader: true,
    showArrow: true,
  }),
  shouldShowOverlayUpdating: true,
  options: () => ({}),
  updateTrigger: 'manual',
  dataModel: null,
  //   /**
  //    * NOTE: dataSet might be fetched asynchronously.
  //    * In that case, this dataSet object would contain `id` but might not contain all dataset details.
  //    * Even then, updateFunc should still be able to run successfully, because it only depends on dataset ID.
  //    * Other dataset details are only needed for further interactions.
  //    */
  dataSet: null,
  projectId: null,
  filePath: null,
  isProduction: true,
  // v2 report editor
  editMode: false,
  // v3 report editor
  exploreMode: false,
  forceCachedOnFrontend: false,
  infiniteScroll: false,
  allowDrillthrough: false,
  allowCrossFilter: false,
  showErrorSurvey: false,
  selectedValues: () => ({ rows: [], columns: [] }),
  showDateDrillContextMenu: true,
  showDateDrillDropdown: true,
  loadingOptions: () => ({
    // Allow parent components to customize the loading steps using a fixed message string.
    // This takes precedence over loadingPreset. Set this to null to use the preset loading messages.
    loadingMessage: null,
    // Allow parent components to customize the loading steps using preset loading messages.
    loadingPreset: 'reporting',
    // What to display when hovering over loading message
    loadingHoverMessage: '',
    // What to do when clicking the loading message
    loadingOnClick: null,
  }),
  parentHspan: null,
  isFromDatasetPreview: false,
  refreshCancelable: false,
  cancelingRefresh: false,
  zoomLevel: 1,
  canAddRelationship: false,
  // settings to be applied to vizSetting
  // currently used for maintaining states between expanding and collapsing widget
  adhocSettings: null,
  // settings to decide whether to show model name in table header
  showFieldDetails: false,
  allowDrillDown: false,
  allowViewUnderlyingData: false,
  drillDownMetadata: () => ({
    previousDrilledDims: [],
    relatedVizSettings: [],
  }),
  interactionConfig: () => (INTERACTION_PRESETS.paginateSortOnly),
  cacheManager: undefined,
  cacheKey: undefined,
  vizTheme: () => ({}),
  showingOutOfSyncBanner: false,
});

export interface Emits {
  (event: 'updating', value: boolean): void;
  (event: 'warningFieldsCaptured', fields: Record<string, any>): void;
  (event: 'vizDataCacheUpdated', options: Record<string, any>): void;
  (event: 'updated', value: any): void;
  (event: 'rendered', value: RenderedPayload): void;
  (event: 'vizDataCacheOutdated', options: Record<string, any>): void;
  (event: 'vizMessage', message: VizInputMessage): void;
  (event: 'pivoted', value: Record<string, any>): void;
  (event: 'executedQuery', sql: string, aql?: string): void;
  (event: 'onSortChanged', value: SortSetting | SortSetting[]): void;
  (event: 'onExploreData', value: { clickBehavior: any }): void;
  (event: 'vizSettingChanged', value: VizSetting): void;
  (event: 'crossFilter', value: Record<string, any>): void;
  (event: 'drillthrough', value: any): void;
  (event: 'jobStatus', value: Job): void;
  (event: 'submitRes', value: any): void;
  (event: 'cancelRefresh'): void;
  (event: 'onJobDone'): void;
  (event: 'mounted'): void;
  (event: 'numRows', value: number | null): void;
  (event: 'jobPoller', value: any): void;
  (event: 'addRelationship'): void;
  (event: 'nonRelationalDataModels'): void;
  (event: 'storeAdhocSettings', value: AdhocSettings): void;
  (event: 'undoChanges'): void;
  (event: 'drillDown', value: DrillDownData): void;
  (event: 'transform', value: VizSettingTransformation, options: { forceRender?: boolean }): void;
}
const emit = defineEmits<Emits>();

// NOTE: must define vizInputGenerator here, instead of inside runFn
const vizInputGenerator = new VizInputGenerator({
  cacheManager: props.cacheManager,
  cacheKey: props.cacheKey,
});
const fetchAmlBindingFunc = inject(fetchAmlBindingFuncKey, null);
provide('eventBus', createEventBus());

function generateDebugUuid () {
  if (!window.H?.env || window.H.env === 'production') return '';
  return generateUuid();
}

const vizComponent = ref('data_table');
const showOnboarding = ref(false);
const vizOptions = ref<Record<string, any> | null>(null);
const vizMessage = ref<VizInputMessage | null>(null);
const initialized = ref(false);
const updating = ref(false);
const updatePromise = ref<Promise<void> | null>(null);
// TODO: type this using VizInput.ts constructor options
const nextUpdateOptions = ref<Record<string, any> | null>(null);
const containerSize = ref<Record<'w' | 'h', number> | null>(null);
const pivotRenderer = ref<PivotTablePropData | null>(null);
const dateDrill = ref<DateDrill>(getDateDrill()!);
const drillthroughs = ref<DashboardDrillthroughPath[]>([]);
const drillValues = ref<ExtractedValues>({ rows: [], columns: [] });
const fetchedJobParsedQuery = ref(false);
const showMigrationNotice = ref(true);
const lastSortSetting = ref<SortSetting | SortSetting[] | null>(null);
// TODO: type from jobs.js
const jobPoller = ref<any>(null);
const jobTag = ref('');
const jobStatus = ref('');
const pendingJobsCount = ref<number>(0);
const hspans = ref<Record<'updateFunc' | 'generateVizInput', HSpan | undefined>>({
  updateFunc: undefined,
  generateVizInput: undefined,
});
const isVisible = ref(false);
const visibilityCheckCount = ref(0);
const shouldRenderWidget = ref(false);
const debugUuid = ref(generateDebugUuid());
const hasPendingRender = ref(true);
const storeAdhocSettingsEventName = ref(props.options.widgetId ? createStoreAdhocSettingsEventName(props.options.widgetId) : null);
const lastAppliedAdhocSettings = ref<AdhocSettings | null>(null);
const renderVizSetting = ref<VizSetting | null>(null);

const vizResult = useTemplateRef('vizResult');
const table = useTemplateRef<InstanceType<typeof VizTableWrapper> | null>('table');
const highcharts = useTemplateRef('highcharts');
const geo = useTemplateRef('geo');
const cohort = useTemplateRef('cohort');
const funnel = useTemplateRef('funnel');
const newFunnel = useTemplateRef('newFunnel');
const vega = useTemplateRef('vega');

const route = useRoute();
const store = useStore();

const { calculateDevDrillthroughs } = useDevDrillthroughs({
  isProduction: props.isProduction,
  dataset: toRef(() => props.dataSet),
  source: props.source,
  renderVizSetting,
});

const isV4 = computed(() => props.options.isV4);
const inWidgetMode = computed(() => !!props.options.widget);
const isAdminOrAnalyst = computed(() => user.isAnalyst || user.isAdmin);

// @tag #dashboard/virtual_scroll
const shouldRenderWidgetInViewportOnly = computed(() => !window.PHANTOM && inWidgetMode.value);
const observeVisibilityOpts = computed(() => {
  return shouldRenderWidgetInViewportOnly.value
    // on first mount, v-observe-visibility will go through all widgets to check for intersection
    // at this point, we don't want to throttle because we need widget in viewport to be visible immediately
    // after that v-observe-visiblity only check widget inside viewport while scrolling, here we apply throttle to handle fast scrolling
    ? { callback: visibilityChanged, throttle: visibilityCheckCount.value === 0 ? 0 : 200 }
    : false;
});

const shouldStoreAdhocSettings = computed(() => {
  if (!persistAdhocSettings) return false;

  return !!props.options.widgetId && !props.exploreMode && storeAdhocSettingsEventName.value;
});

const shouldApplyAdhocSettings = computed(() => {
  if (!persistAdhocSettings) return false;

  // make sure same adhoc settings are applied once
  return !!props.adhocSettings && !isEqual(props.adhocSettings, lastAppliedAdhocSettings.value);
});

const errorAction = computed(() => {
  if (!inWidgetMode.value) return null;
  if (isCanceled(jobStatus.value)) {
    return {
      text: 'Refresh' as const,
      onClick: () => update({ bustCache: true }),
    };
  }
  return null;
});

const jobQueueStatusComponent = computed(() => {
  if (inWidgetMode.value) {
    return {
      name: WidgetJobQueueStatus,
      attrs: {
        refreshCancelable: props.refreshCancelable,
        cancelingRefresh: props.cancelingRefresh,
      },
    };
  }
  return {
    name: JobQueueStatus,
    attrs: {
      jobTag: jobTag.value,
      disablePopover: true,
      class: 'text-gray-800',
    },
  };
});

const shouldShowDateDrillDropdown = computed(() => {
  return props.showDateDrillDropdown
    && (isV4.value || !props.editMode)
    && !props.exploreMode
    && dateDrill.value?.canPerformDateDrill
    && !updating.value
    && vizMessage.value?.type !== 'error'
    && props.vizSetting.viz_type !== DATA_TABLE
    && props.vizSetting.viz_type !== PIVOT_TABLE;
});

const isLegacyGeoHeatmap = computed(() => props.vizSetting.viz_type === GEO_HEATMAP);
const shouldShowGeoMigrationAlert = computed(() => {
  return (props.editMode || props.exploreMode)
    && isLegacyGeoHeatmap.value
    && showMigrationNotice.value;
});

const hasErrorOrWarning = computed(() => vizMessage.value && (vizMessage.value.type === 'error' || vizMessage.value.type === 'warning'));
const isLoading = computed(() => !initialized.value || updating.value);

const isInfiniteScrollEnabled = computed(() => props.infiniteScroll && enableInfiniteScroll);

const canCrossFilter = computed(() => props.allowCrossFilter && crossFilterEnabled && !!props.dataSet);

const extraOptions = computed(() => {
  return assign({
    showAnnotations: false,
    filters: {},
    drilldowns: [],
    tableNoStretch: false,
  }, props.options) as Record<string, any>;
});

const applyColumnWidth = computed(() => !!isV4.value && props.options.widget && allowSaveColumnWidth);

const hasBorder = computed(() => {
  return shouldShowDataTable(vizComponent.value)
    && !extraOptions.value.widget
    && !(vizComponent.value === 'metric_kpi' && useNewMetricKpi.value)
    && !(showFriendlyAqlCompileError && vizMessage.value?.errorDetails?.type === 'AQL_COMPILE_ERROR');
});

const shouldAutoUpdate = computed(() => props.updateTrigger === 'auto');

const dataSourceId = computed(() => props.dataSet?.dataSourceId);

const canDrillthrough = computed(() => {
  if (!props.allowDrillthrough || !drillthroughEnabled || window.states.shareable_link) {
    return false;
  }
  return !(props.isProduction && props.editMode);
});

const canViewUnderlyingData = computed(() => {
  return isVUDFeaturesEnabled && props.allowViewUnderlyingData && !user.isPublicUser && !props.exploreMode;
});

const canDrillDown = computed(() => {
  return isDrillDownFeaturesEnabled && props.allowDrillDown && !user.isPublicUser
    && !props.exploreMode
    && allowDrillDownOnVizType(props.vizSetting.viz_type);
});

const canPerformDrill = computed(() => canDrillthrough.value || canDrillDown.value || canViewUnderlyingData.value);

const vizResultSpanAttributes = computed(() => ({
  'h.source_id': props.source?.id,
  'h.source_type': props.source?.type,
  'h.viz_type': props.vizSetting.viz_type,
}));

const useNewMetricKpi = computed(() => !!isV4.value && !!useNewMetricKpiEnabled);

const allowRemoveHighchartSort = computed(() => props.vizSetting.viz_type === PIE_CHART);
const interactionConfig = computed(() => {
  if (typeof props.interactionConfig === 'string') {
    return INTERACTION_PRESETS[props.interactionConfig] || INTERACTION_PRESETS.paginateSortOnly;
  }
  return props.interactionConfig;
});

const {
  loadingSteps,
  loadingStartTime,
  loadingMessage,
  loadingPlaceholderTimeout,
  setLoadingPlaceholder,
  showLoadingOverlay,
  showOverloaded,
  isOverloaded,
} = useVizResultLoading({
  shouldShowOverlayUpdating: props.shouldShowOverlayUpdating,
  loadingOptions: toRef(props, 'loadingOptions'),
  vizOptions,
  inWidgetMode,
  pendingJobsCount,
  isLoading,
});

let highchartsSelectionPos: HighchartsSelectionPos | undefined;
// Do not store date drill interaction unless it has already been performed
// Prevent cases where viz already has date drill stored in its viz setting, which is not an adhoc interaction
const hasPerformedDateDrill = ref(false);
function storeAdhocSettings () {
  const adhocSettings: AdhocSettings = {
    sort: lastSortSetting.value,
    highchartsSelectionPos,
  };
  if (hasPerformedDateDrill.value) {
    adhocSettings.dateDrillValue = dateDrill.value?.currentValue;
  }
  emit('storeAdhocSettings', adhocSettings);
}
function handleHcSelection (pos: HighchartsSelectionPos) {
  highchartsSelectionPos = pos;
}

function handleSort (vizSettingSort: SortSetting) {
  if (table.value) {
    table.value.handleSort(vizSettingSort);
  }
}

async function migrateLegacyGeoHeatmap () {
  if (!isLegacyGeoHeatmap.value) {
    return;
  }

  const newVizSetting = migrateLegacyGeoHeatmapService(props.vizSetting);
  eventBus.$emit('vizSettingForm:onVizSettingChanged', { vizSetting: newVizSetting, shouldEmitChange: true });
  await nextTick();
  update({ bustFrontendCache: true }, newVizSetting);
}

function tableUpdated () {
  emitVizDataCacheUpdated();
}

function setRenderVizSetting (newVizSetting: VizSetting | null) {
  /**
   * update renderVizSetting so that:
   * + queued updates will use this new viz setting
   * + subsequent new updates that do not pass down vizSetting (e.g. refresh) will use this new viz setting
   * + local changes made within VizResult itself (e.g. local date drill) will be applied to on top of this new viz setting
   */
  renderVizSetting.value = newVizSetting;
}

async function update (options: Record<string, any> = {}, vizSetting: VizSetting | null = null) {
  nextUpdateOptions.value = options || {};
  setRenderVizSetting(vizSetting || renderVizSetting.value);

  if (!updatePromise.value) {
    // start new queue/chain of updates
    updatePromise.value = runQueuedUpdates().finally(() => {
      updatePromise.value = null;

      // The following typically clears query for failed updates. (failed updates -> fetchedJobParsedQuery = false)
      // Doing this here to skip intermediary states (e.g. cancelled jobs)
      // Only set query back to empty string after the initial component finished rendering to avoid removing cached excuted query
      if (!fetchedJobParsedQuery.value && initialized.value) setExecutedQuery('', '');
    });
  } else {
    // cancel current job. `runQueuedUpdates` will eventually pick up the nextUpdateOptions
    // NOTE: If there is no job yet, e.g. we are in the middle of submit_generate, then this cancelCurrentJob() will do nothing.
    //       Instead, when we get the job, onJobStatus would then cancel it.
    cancelCurrentJob();
  }

  return updatePromise.value || Promise.resolve();
}

async function runQueuedUpdatesFunc () {
  const options = nextUpdateOptions.value;
  if (options) {
    nextUpdateOptions.value = null;
    try {
      await updateFunc(options, renderVizSetting.value!);
    } finally {
      /**
       * During the above updateFunc call, `nextUpdateOptions` might be updated.
       * -> Call runQueuedUpdates again to process that newly updated `nextUpdateOptions`
       */
      await runQueuedUpdatesFunc();
    }
  }
}
// Create debounced version of runQueuedUpdatesFunc
const runQueuedUpdates = pDebounce(runQueuedUpdatesFunc, 100);

function cancelCurrentJob () {
  const currentJobPoller = jobPoller.value;
  if (!currentJobPoller) return;

  currentJobPoller.cancelJobOptimistic();
  setJobPoller(null);
}

function vizInputOptions (renderOptions: Record<string, any> = {}) {
  let bustFrontendCache = false;
  let bustBackendCache = false;
  let forceCachedOnBackend = false;
  const forceCachedOnFrontend = get(renderOptions, 'forceCachedOnFrontend', props.forceCachedOnFrontend);
  if (renderOptions) {
    if (typeof renderOptions === 'object') {
      bustFrontendCache = !!(renderOptions.bustCache || renderOptions.bustFrontendCache);
      bustBackendCache = !!(renderOptions.bustCache || renderOptions.bustBackendCache);
      forceCachedOnBackend = !!renderOptions.forceCachedOnBackend;
    } else {
      // in legacy API, `renderOptions` is a boolean, which represents bustAllCache
      bustFrontendCache = true;
      bustBackendCache = true;
    }
  }
  return {
    bustBackendCache,
    bustFrontendCache,
    forceCachedOnBackend,
    forceCachedOnFrontend: bustFrontendCache ? false : forceCachedOnFrontend,
    timePeriodFilterValue: extraOptions.value.filters.time_period || props.options.timePeriodFilterValue, // load time_period from dashboard while expanding
    filters: extraOptions.value.filters,
    drilldowns: extraOptions.value.drilldowns,
    widget: props.options.widget,
    pagination: props.options.pagination,
    exploring: props.options.exploring,
    source: props.source,
    dataSet: props.dataSet,
    projectId: props.projectId,
    filePath: props.filePath,
    isProduction: props.isProduction,
    isInfiniteScroll: isInfiniteScrollEnabled.value,
    drillthroughHandler: canPerformDrill.value ? setDrillValues : null,
    crossFilterHandler: canCrossFilter.value ? onCrossFilter : null,
    onJobPoller: setJobPoller,
    onJobStatus,
    onSubmitRes,
    onJobDone,
    initialSelectedValues: props.selectedValues,
    bindingJson: null,
    colorPalettes: null,
  };
}

function applyAdhocSettings (vizSetting: VizSetting) {
  if (!props.adhocSettings || !vizSetting) return vizSetting;

  let newVizSetting = cloneDeep(vizSetting);
  const { dateDrillValue, sort } = props.adhocSettings;

  if (dateDrillValue && dateDrill.value) {
    newVizSetting = dateDrill.value.buildTransformedVizSetting(dateDrillValue, newVizSetting) || newVizSetting;
  }
  if (sort) {
    newVizSetting.settings.sort = sort;
    // update multiple sort popover
    onSortChanged(sort);
  }
  return newVizSetting;
}

// Convert arrow functions to normal functions
async function generateVizInputFn (renderOptions: Record<string, any> = {}, vizSetting: VizSetting | null = null) {
  let renderVs = cloneDeep(vizSetting || props.vizSetting) as VizSetting;

  try {
    if (isSortBeforeLimitEnabled && lastSortSetting.value) {
      renderVs.settings.sort = lastSortSetting.value;
    }
    if (shouldApplyAdhocSettings.value) {
      renderVs = applyAdhocSettings(renderVs);
    }

    const vizInputOpts = vizInputOptions(renderOptions);
    // if FT aml_studio:utilize_fe_processing, it will skip as the promise is null
    if (utilizeFeProcessing) {
      vizInputOpts.bindingJson = await fetchAmlBinding();
    }

    // preemptively emit cache-outdated so that even in case of error or cancellation, the viz data status is correctly set as "outdated"
    emitVizDataCacheOutdated();

    const colorPalettes = await store.dispatch(
      'colors/fetchColorPalettes',
      { hOtelContext: { activeSpan: hspans.value.generateVizInput } },
    );
    vizInputOpts.colorPalettes = colorPalettes;

    const generatedVizInput = await vizInputGenerator.generate(
      renderVs,
      props.dataModel,
      vizInputOpts,
      {
        hOtelContext: { activeSpan: hspans.value.generateVizInput },
        batchSubmitGenerate: inWidgetMode.value && !isEmpty(vizInputOpts.bindingJson) && batchSubmitGenerate,
      },
    );

    const lastCacheUpdated = dateOrNull(get(generatedVizInput, 'options.meta.lastCacheUpdated'));
    const noData = isNoDataMessage(get(generatedVizInput, 'options.meta.lastCacheUpdated'));
    const warningFields = isFieldError((generatedVizInput.message || {}) as any as VizInputMessage);
    const meta = get(generatedVizInput, 'options', {});
    emit('warningFieldsCaptured', warningFields);

    if (!isEmpty(generatedVizInput.message)) {
      setJobPoller(null);
    }
    if (lastCacheUpdated) emitVizDataCacheUpdated({ noData, lastCacheUpdated });

    emit('updated', { meta, updatedAt: new Date() });
    return generatedVizInput;
  } catch (err: any) {
    if (err.message === 'OUTDATED_VIZ_DATA_CACHE') {
      emitVizDataCacheOutdated();
      return null;
    }
    throw err;
  }
}

async function updateFn (renderOptions: Record<string, any>, vizSetting: VizSetting) {
  if (updating.value) {
    return;
  }
  updating.value = true;
  loadingStartTime.value = DateTime.now();
  loadingMessage.value = props.loadingOptions.loadingMessage;
  if (fetchedJobParsedQuery.value) setExecutedQuery('-- Waiting for new Query');
  fetchedJobParsedQuery.value = false;
  const vizInput = await generateVizInput(renderOptions, vizSetting);

  showOnboarding.value = !!props.exploreMode; // only showOnboarding for v3 AFTER the first load

  if (!vizInput) {
    updating.value = false;
    return;
  }
  const {
    options, message, pivotRenderer: pvRenderer, chartTitle, query, drillthroughs: dthroughts, executedAql,
  } = vizInput as any;

  pivotRenderer.value = pvRenderer;

  vizComponent.value = vizType2Component(vizSetting.viz_type) || '';
  vizOptions.value = merge(options, buildAdditionalOptions());
  vizMessage.value = message;
  emit('vizMessage', message);

  // passing vizSetting so chart data (VizPivotTable.vue) can react correctly to adhoc date drill
  emit('pivoted', pvRenderer && { pivotRenderer: pvRenderer, appliedVizSetting: vizSetting, source: props.source });

  const numRows = pivotRenderer.value?.numRows || vizOptions.value?.meta?.numRows || null;
  emit('numRows', numRows);

  if (query) {
    fetchedJobParsedQuery.value = true;
    setExecutedQuery(query, executedAql);
  }

  eventBus.$emit('vizInput:title', chartTitle);

  if (extraOptions.value.container) {
    wrapFuncWithinSpan(
      'VizResult#updateFunc#resize',
      { parentSpan: () => hspans.value.updateFunc as HSpan },
      resize,
    )(true);
  }

  if (props.isProduction) {
    drillthroughs.value = dthroughts;
  } else {
    drillthroughs.value = await calculateDevDrillthroughs();
  }

  if (shouldApplyAdhocSettings.value) {
    // maintain highcharts zoom position when collapsing and expanding widget
    updateHighchartsZoom();
    lastAppliedAdhocSettings.value = props.adhocSettings;
  }

  updating.value = false;
  emit('updated', vizOptions.value);
  if (hasErrorOrWarning.value) {
    handleRendered({ error: JSON.stringify(vizMessage.value) });
  } else if (shouldShowGeoMigrationAlert.value) {
    handleRendered({ error: 'Geo heatmap reqires migration' });
  }

  // trace VizResult DOM
  if (pageTracingEnanbled) {
    hspans.value.updateFunc?.traceDom(
      vizResult.value?.$el,
      'VizResult',
      {
        attributes: { ...vizResultSpanAttributes.value, 'h.num_rows': numRows },
      },
    );
  }
}

function updateHighchartsZoom () {
  const { highchartsSelectionPos: highchartsPos } = props.adhocSettings || {};
  if (highchartsPos) {
    nextTick(() => {
      if (highcharts.value) {
        highcharts.value.zoom(highchartsPos);
      }
    });
  }
}

function logInvalidVizComponent (component: string) {
  handleRendered({ error: `Invalid viz component: ${component}` });
}

function getDateDrill (vizSetting = props.vizSetting) {
  if (!vizSetting) {
    return null;
  }

  return new DateDrill(vizSetting, {
    dataModel: props.dataModel,
    dataSet: props.dataSet,
  });
}

function resize (force = false) {
  if (!shouldRenderWidget.value) return;
  nextTick(() => {
    const container = vizResult.value?.$el?.closest(extraOptions.value.container);
    if (!container) {
      return;
    }
    if (!force && !shouldHandleResize(container)) {
      return;
    }
    const geoContainer = vizResult.value?.$el.querySelector('div') as HTMLDivElement | null;
    if (geo.value) {
      let heightChanged = false;
      let newHeight = null;
      const fixedHeightWrapper = container.querySelector('.result-viz');
      if (fixedHeightWrapper) {
        const computedStyle = window.getComputedStyle(fixedHeightWrapper);
        newHeight = fixedHeightWrapper.clientHeight - parseFloat(computedStyle.paddingTop) - parseFloat(computedStyle.paddingBottom);
        newHeight = `${newHeight}px`;
        heightChanged = newHeight !== geoContainer?.style?.height;
      }
      if (force || heightChanged) {
        if (heightChanged && geoContainer && newHeight) {
          geoContainer.style.height = newHeight;
        }
        geo.value.onVizRenderEvent(force);
      }
      return;
    }
    if (geoContainer) {
      geoContainer.style.height = '';
    }
    if (table.value) {
      table.value.fitToContainer(container);
      return;
    }
    if (cohort.value) {
      cohort.value.fitToContainer(container);
      return;
    }
    if (funnel.value) {
      funnel.value.fitToContainer(container);
      return;
    }
    if (newFunnel.value) {
      newFunnel.value.fitToContainer(container);
    }
    if (vega.value) {
      vega.value.fitToContainer();
    }
  });
}

function shouldHandleResize (container: HTMLElement) {
  if (!shouldRenderWidget.value) return false;

  // if size change under a threshold -> should not resize
  const shouldResize = vizOptions.value && (!containerSize.value
    || Math.abs(containerSize.value.w - container.clientWidth) >= RESIZE_MIN_THRESHOLD
    || Math.abs(containerSize.value.h - container.clientHeight) >= RESIZE_MIN_THRESHOLD);
  if (shouldResize) {
    containerSize.value = {
      w: container.clientWidth,
      h: container.clientHeight,
    };
  }
  return shouldResize;
}

function addResizeListener () {
  nextTick(() => {
    const container = vizResult.value?.$el?.closest(extraOptions.value.container);
    const resizeFn = resize;
    if (container) {
      erd.listenTo(container, debounce(() => resizeFn(false), 200));
    }
  });
}

function removeResizeListener () {
  const container = vizResult.value?.$el?.closest(extraOptions.value.container);
  if (container) {
    erd.removeAllListeners(container);
  }
}

function buildAdditionalOptions () {
  const additionalOptions = {} as Record<string, any>;

  if (vizComponent.value === 'highcharts' || isConversionFunnelTypes(vizComponent.value)) {
    if (extraOptions.value.widget) {
      additionalOptions.exporting = {
        enabled: false,
      };
    }
  }
  return additionalOptions;
}

function onSortChanged (val: SortSetting | SortSetting[], shouldEmitChange = false) {
  lastSortSetting.value = val;
  if (props.editMode || props.exploreMode) {
    eventBus.$emit('vizSettingForm:sortChanged', val, renderVizSetting.value, shouldEmitChange);
  }

  emit('onSortChanged', val);
}

function onRemoveSort () {
  lastSortSetting.value = null;

  // for highcharts sort, it mutates the viz options directly => cannot reset sort normally (since data is mutated and we dont know the original state)
  // => call update again to recalculate viz options (hopefully it still uses frontend cache)
  update();

  if (props.editMode || props.exploreMode) {
    const newVizSetting = {
      ...props.vizSetting,
      settings: {
        ...props.vizSetting.settings,
        sort: {},
      },
    };
    eventBus.$emit('vizSettingForm:onVizSettingChanged', { vizSetting: newVizSetting, shouldEmitChange: true });
  }
}

function onPageSizeChanged (pageSize: number) {
  eventBus.$emit('vizSettingForm:pageSizeChanged', pageSize);
}

function onExplore ({ clickBehavior }: { clickBehavior: any }) {
  emit('onExploreData', { clickBehavior });
}

// TODO: remove this after the feature is released
// https://www.notion.so/holistics/RFC-2696-Revamp-table-interactions-1aef89dc7e4980159dd4d639d66824a9?pvs=4#1caf89dc7e4980a1be50db20aab5925b
function doNotPersistDateDrillAndMetricSheetInWidget (type: InteractionName) {
  // TODO: unify all use of widget options :pray:
  return (!!props.options.widgetId || !!props.options.widget)
    && !persistAdhocInteractions
    && [InteractionName.ChangeMetricSheetSettings, InteractionName.DateDrill].includes(type);
}

function emitTransform ({ patches, type }: VizSettingTransformation, vizSetting: VizSetting) {
  if (!type) return;
  // check dataSet to quickly handle 2.0 https://holistics.slack.com/archives/C2W4QL77F/p1746178317968529s
  if (!props.dataSet || doNotPersistDateDrillAndMetricSheetInWidget(type)) {
    updateFn({ bustFrontendCache: true }, vizSetting);
    emit('vizSettingChanged', vizSetting);
  } else {
    emit('transform', { patches, type }, {
      forceRender: type
        && (
          type === InteractionName.SortColumn
          || patches.some(({ path }) => checkSignificantChangeInVizSetting(vizSetting.viz_type, path))
        ),
    });
  }
}

function onVizSettingTransform (interaction: VizSettingInteraction, vizSetting = renderVizSetting.value) {
  if (!vizSetting || updating.value || hasPendingRender.value || !interaction.type) return;

  const patches = getVizSettingPatchesByInteraction(vizSetting, interaction) as TransformationPatch[];
  emitTransform({ patches, type: interaction.type }, vizSetting);
}

async function performDateDrill (transformationValue: string | null) {
  hasPerformedDateDrill.value = false;
  if (!dateDrill.value) {
    return;
  }
  // null => reset to original transformation
  const newTransformation = transformationValue === null ? dateDrill.value.originalValue : transformationValue;

  const transformedVizSetting = dateDrill.value.buildTransformedVizSetting(newTransformation as string, renderVizSetting.value);
  if (!transformedVizSetting) {
    return;
  }
  hasPerformedDateDrill.value = true;

  // Make expanded and collapsed widget/block consistent
  // https://holistics.slack.com/archives/C055V9WSVU0/p1733889184252689
  if (!isSortBeforeLimitEnabled) {
    lastSortSetting.value = null;
  }

  const patches = getVizSettingPatchesUsingDiff(renderVizSetting.value!, transformedVizSetting);
  emitTransform({ patches, type: InteractionName.DateDrill }, transformedVizSetting);

  const originalTransformation = dateDrill.value.dateDrillOptions.find(({ value }) => value === dateDrill.value?.originalValue);
  const transformation = dateDrill.value.dateDrillOptions.find(({ value }) => value === newTransformation);
  const trackingInfo = {
    widgetId: props.source?.id,
    vizType: props.vizSetting.viz_type,
    originalDrillType: get(originalTransformation, 'value') || 'none',
    drillType: get(transformation, 'value') || 'none',
  } as TrackingInfo;
  trackDateDrill(trackingInfo);
}

function setDrillValues (values: ExtractedValues) {
  drillValues.value = values;
}

// Use the existing debounce function from the original component
const onCrossFilter = debounce((values) => {
  if (updating.value || !canCrossFilter.value) {
    return;
  }
  if (isEqual(values, props.selectedValues)) {
    return; // prevent duplicated events
  }

  const dataSetId = props.dataSet?.id;
  const conditions = buildVizConditions(values, dataSetId!, props.vizSetting);

  emit('crossFilter', {
    dataSetId,
    conditions,
    rawValues: values,
  });
}, 100);

function onDrillthrough (drillthrough: any) {
  emit('drillthrough', drillthrough);
}

function setJobPoller (newJobPoller: any) {
  emit('jobPoller', newJobPoller);
  jobPoller.value = newJobPoller;
}

// pendingJobCount is from jobs_batch_polling.js
// TODO: convert to .ts and type properly
function onJobStatus (job: Job & { pendingJobsCount: number }) {
  emit('jobStatus', job);

  const { pendingJobsCount: pendingCount, tag, status } = job;
  jobStatus.value = status;
  jobTag.value = tag;
  pendingJobsCount.value = pendingCount;

  if (job?.running_info) {
    loadingMessage.value = getQueryRunningStepMessage(isAdminOrAnalyst.value, job.running_info);
  }
  if (get(job, 'running_info.has_generated_sql')) {
    getJobParsedQuery(job.id);
  }

  if (isActiveJob(status) && nextUpdateOptions.value) {
    // there is a queued nextUpdateOptions -> cancel current job/update eagerly so that next update can be triggered immediately
    cancelCurrentJob();
  }
}

async function getJobParsedQuery (jobId: number) {
  if (!user.canViewGeneratedSql || fetchedJobParsedQuery.value) {
    return;
  }

  fetchedJobParsedQuery.value = true;
  try {
    const res = await fetchJobParsedQuery(jobId, { hOtelContext: { activeSpan: hspans.value.updateFunc as HSpan } });
    if (res.parsed_query) {
      setExecutedQuery(res.parsed_query, res.executed_aql);
    }
  } catch (err: any) {
    const errMsg = errorMessageFromAjax(err);
    setExecutedQuery(errMsg, errMsg);
  }
}

function setExecutedQuery (sql: string, aql = '') {
  if (!user.canViewGeneratedSql) return;

  emit('executedQuery', sql, aql);
}

function onSubmitRes (payload: any) {
  emit('submitRes', payload);
}

function onJobDone () {
  updating.value = false;
  setJobPoller(null);
  emit('onJobDone');
}

function handleRendered ({ error }: RenderedPayload = {}) {
  emit('rendered', { error });
}

function handleErrorWhileRendering (errorWhileRendering: any) {
  if (errorWhileRendering) {
    vizMessage.value = { type: 'error', message: errorWhileRendering.message };
  }
}

function recheckShouldRenderWidget () {
  if (shouldRenderWidgetInViewportOnly.value) {
    shouldRenderWidget.value = isVisible.value;
  } else {
    shouldRenderWidget.value = true;
  }
}

function visibilityChanged (isVis: boolean) {
  visibilityCheckCount.value += 1;

  // reset containerSize because keepAlive will retain its value e.g. moving from edit widget -> dashboard
  // which will cause shouldHandleResize to return false and incorrectly reisze widgets
  // https://holistics.slack.com/archives/CLUGX6RUY/p1707121509829789
  if (!isVis) {
    containerSize.value = null;
  }

  isVisible.value = isVis;

  if (shouldRenderWidgetInViewportOnly.value && isVis) {
    // render widget if it becomes visible
    // (don't un-render if it goes from visible to non-visible)
    shouldRenderWidget.value = true;
  }
}

function fetchAmlBinding (): Promise<any> {
  if (typeof fetchAmlBindingFunc !== 'function') return Promise.resolve();

  // if dataModel is available, then it should be model preview => get model binding
  const objectFqn = get(props, 'dataModel.name') || get(props, 'dataSet.name') || get(props, 'dataSet.id');

  const spanOptions = {
    attributes: {
      'h.param.objectFqn': objectFqn,
    },
  };
  return HSpan.withinSpan(
    'aml.fetchAmlBinding',
    {
      spanOptions,
      parentSpan: () => hspans.value.generateVizInput as HSpan,
    },
    () => fetchAmlBindingFunc(objectFqn),
  );
}

function logDebug (...message: any[]) {
  if (!window.H?.env || window.H.env === 'production') return;

  // eslint-disable-next-line no-console
  console.debug(debugUuid.value, ...message);
}

function emitVizDataCacheUpdated (options = {}) {
  hasPendingRender.value = false;
  emit('vizDataCacheUpdated', options);
}

function emitVizDataCacheOutdated (options = {}) {
  hasPendingRender.value = true;
  emit('vizDataCacheOutdated', options);
}

function onMetricSheetSettingChanged (payload: MetricSheetSettingChangedPayload) {
  const type = InteractionName.ChangeMetricSheetSettings;
  if (doNotPersistDateDrillAndMetricSheetInWidget(type)) {
    const vs = transformVizSettingByInteraction(renderVizSetting.value, { type, payload });
    if (!vs) return;

    updateFn({ bustFrontendCache: true }, vs);
    if (props.editMode || props.exploreMode) {
      eventBus.$emit('vizSettingForm:onVizSettingChanged', { vizSetting: vs });
    }
  } else {
    onVizSettingTransform({ type, payload }, renderVizSetting.value);
  }
}

// watches
watch(() => shouldRenderWidgetInViewportOnly.value, () => {
  recheckShouldRenderWidget();
}, { immediate: true });
watch(() => shouldRenderWidget.value, (newVal, oldVal) => {
  if (newVal && !oldVal) resize();
});
watch(() => updating.value, (isUpdating) => {
  setLoadingPlaceholder(isUpdating);
  emit('updating', isUpdating);
});

watch(() => props.vizSetting, (newVizSetting, oldVizSetting) => {
  const toCompare = renderVizSetting.value || oldVizSetting;
  if (isEqual(newVizSetting, toCompare)) return;

  setRenderVizSetting(newVizSetting);

  // when changing viz, re-check shouldRenderWidget to un-render non-visible viz
  recheckShouldRenderWidget();
  lastSortSetting.value = null;
  if (shouldUpdateDateDrill(newVizSetting, toCompare)) {
    dateDrill.value = getDateDrill()!;
  } else if (shouldUpdateDateDrillIC(newVizSetting, toCompare)) {
    dateDrill.value = getDateDrill(applyDateDrillConditionToVizSetting(cloneDeep(newVizSetting)))!;
  }
  if (shouldAutoUpdate.value) {
    logDebug('update by vizSetting change', toCompare, newVizSetting);
    update({}, newVizSetting);
  } else if (props.forceCachedOnFrontend) {
    logDebug('generateVizInput by vizSetting change');
    // Use side effect of #generateVizInput to check if viz input cache is outdated
    generateVizInput();
  }
});

watch(() => props.vizSetting.settings?.sort, (newVal, oldVal) => {
  if (shouldStoreAdhocSettings.value && !isEqual(newVal, oldVal)) {
    lastSortSetting.value = newVal;
  }
}, {
  deep: true,
});

watch(() => extraOptions.value, (newValue, oldValue) => {
  if (!isEqual(newValue, oldValue)) {
    logDebug('update by extraOptions change', oldValue, newValue);
    update();
  }
});

watch(() => props.dataModel, (newValue, oldValue) => {
  if (shouldAutoUpdate.value && !isEqual(newValue, oldValue)) {
    logDebug('update by dataModel change', oldValue, newValue);
    update();
  }
});

watch(() => props.dataSet, (newValue, oldValue) => {
  if (shouldAutoUpdate.value && !isEqual(newValue, oldValue)) {
    logDebug('update by dataSet change', oldValue, newValue);
    update();
  }
});

watch(() => props.adhocSettings?.highchartsSelectionPos, (newVal, oldVal) => {
  if (persistAdhocSettings && !isEqual(newVal, oldVal)) {
    updateHighchartsZoom();
  }
});

watch(() => props.adhocSettings, (newVal, oldVal) => {
  if (persistAdhocSettings && newVal && !isEqual(omit(newVal, 'highchartsSelectionPos'), omit(oldVal, 'highchartsSelectionPos'))) {
    logDebug('update by adhocSettings change', newVal, oldVal);
    update();
  }
});

watch(() => vizMessage.value, (newVizMessage) => {
  if (newVizMessage && newVizMessage.type === 'error') {
    trackVizError(newVizMessage, {
      dataSetId: props.dataSet?.id,
      widgetId: props.options.widget?.id,
      route,
      vizType: props.vizSetting.viz_type,
    });
  }
  if (newVizMessage && newVizMessage.message === 'Can\'t combine selected fields due to missing relationships between them.') {
    emit('nonRelationalDataModels');
  }
});

function updateTableCrossFilterIndicator () {
  if (!canCrossFilter.value || !table.value) {
    return;
  }
  if (vizOptions.value?.updateCrossFilterIndicatorFn) {
    vizOptions.value.updateCrossFilterIndicatorFn(props.selectedValues);
  } else if (vizOptions.value?.crossFilter) {
    vizOptions.value.crossFilter.updateCFIndicator(props.selectedValues);
  }
}

function updateChartCrossFilterIndicator () {
  if (!canCrossFilter.value || !highcharts.value?.chart || !vizOptions.value?.updateCrossFilterIndicatorFn) {
    return;
  }
  vizOptions.value.updateCrossFilterIndicatorFn(highcharts.value.chart, props.selectedValues);
}

function updateNewFunnelFilterIndicator () {
  if (!canCrossFilter.value || !newFunnel.value?.highchart || !vizOptions.value?.updateCrossFilterIndicatorFn) {
    return;
  }

  vizOptions.value.updateCrossFilterIndicatorFn(newFunnel.value?.highchart, props.selectedValues);
  newFunnel.value?.updateCrossFilterIndicatorFn(props.selectedValues);
}

watch(() => highcharts.value, () => updateChartCrossFilterIndicator());
watch(() => newFunnel.value?.highchart, () => updateNewFunnelFilterIndicator());
watch(() => table.value, () => updateTableCrossFilterIndicator());

watch(() => props.selectedValues, (val, oldVal) => {
  if (!isEqual(val, oldVal)) {
    updateTableCrossFilterIndicator();
    updateChartCrossFilterIndicator();
    updateNewFunnelFilterIndicator();
  }
}, { deep: true });

watch(() => props.vizTheme, () => {
  nextTick(() => {
    if (table.value && table.value.updateVisualState) {
      table.value.updateVisualState();
    }
  });
}, { deep: true });

// Setup lifecycle hooks
onMounted(() => {
  renderVizSetting.value = props.vizSetting;

  if (shouldAutoUpdate.value) {
    logDebug('update on mounted');
    update().finally(() => {
      initialized.value = true;
    });
  } else {
    initialized.value = true;
  }
  if (extraOptions.value.container) {
    addResizeListener();
  }
  emit('mounted');
});

onBeforeUnmount(() => {
  if (extraOptions.value.container) {
    removeResizeListener();
  }
  if (window.H?.current_tenant?.auto_cancel_unused_report_jobs && jobPoller.value) {
    jobPoller.value.cancel();
  }
  if (loadingPlaceholderTimeout.value) {
    clearTimeout(loadingPlaceholderTimeout.value);
  }
});

// Register event bus listeners
if (shouldStoreAdhocSettings.value) {
  eventBus.$on(storeAdhocSettingsEventName.value!, storeAdhocSettings);

  onBeforeUnmount(() => {
    eventBus.$off(storeAdhocSettingsEventName.value!, storeAdhocSettings);
  });
}

// Wrap functions with tracing
const updateFunc = wrapFuncWithinSpan(
  'VizResult#updateFunc',
  {
    spanOptions: {
      attributes: vizResultSpanAttributes.value,
    },
    parentSpan: (options) => get(options, 'hOtelContext.activeSpan') || props.parentHspan as HSpan,
    timeoutMillis: 0,
  },
  updateFn,
  (span) => {
    hspans.value.updateFunc = span as HSpan;
  },
);
const generateVizInput = wrapFuncWithinSpan(
  'VizResult#generateVizInput',
  { parentSpan: () => hspans.value.updateFunc as HSpan },
  generateVizInputFn,
  (span) => {
    hspans.value.generateVizInput = span as HSpan;
  },
);

defineExpose({
  update,
  handleSort,
});
</script>

<style lang="postcss">
.result-viz {
  flex: 1;
  position: relative;
  min-height: 0;
  min-width: 0;
  display: flex;
  flex-direction: column;

  &.has-border {
    padding: 6px;
    border-radius: 4px;
    /* Use border/default token for default border color */
    @apply border border-gray-300;
  }

  .viz-context-menu {
    /* Use background/default token for white background */
    @apply bg-white;
    /* Use border/default token for border */
    @apply border border-gray-300;
    border-radius: 4px;
    /* Using default shadow from Tailwind config */
    @apply shadow-lg;
    min-width: 150px;
  }
}

.job-queue-status-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.viz-result-loading-container {
  > *:not(.job-queue-status-container) {
    opacity: 0.2;
  }
}
</style>
