import {
  computed, Ref, ref, watch,
} from 'vue';
import type { LoadingOptions } from '@/modules/Viz/types/vizResult';
import { DateTime } from 'luxon';

export const useVizResultLoading = ({
  loadingOptions,
  vizOptions,
  inWidgetMode,
  pendingJobsCount,
  isLoading,
  shouldShowOverlayUpdating,
}: {
  shouldShowOverlayUpdating: boolean;
  loadingOptions: Ref<LoadingOptions>;
  vizOptions: Ref<Record<string, any> | null>;
  inWidgetMode: Ref<boolean>;
  pendingJobsCount: Ref<number>;
  isLoading: Ref<boolean>;
}) => {
  const loadingStartTime = ref<DateTime | null>(null);
  const loadingMessage = ref<string | null>(null);
  const showLoadingPlaceholder = ref(true);
  const loadingPlaceholderTimeout = ref<ReturnType<typeof setTimeout> | null>(null);

  function setLoadingPlaceholder (isUpdating: boolean) {
    if (isUpdating) {
      /**
       * NOTE: why don't we use `duration` of v-h-loading?
       * Because we want to make sure the placeholder is display only once in the whole loading cycle,
       * which is impractically hard to achieve with v-h-loading `duration`, espescially when:
       *   * we have a custom isOverloaded display
       *   * we continuously update localLoadingMessage
       */
      showLoadingPlaceholder.value = true;
      loadingPlaceholderTimeout.value = setTimeout(() => {
        showLoadingPlaceholder.value = false;
      }, 2500);
    } else if (loadingPlaceholderTimeout.value) {
      clearTimeout(loadingPlaceholderTimeout.value);
    }
  }

  const loadingSteps = computed(() => {
    const {
      loadingPreset, loadingHoverMessage, loadingOnClick, type: loadingType,
    } = loadingOptions.value;

    let placeholderStep; // an initial step with minimal layout shifts and details
    if (showLoadingPlaceholder.value) {
      if (vizOptions.value) {
        // try to keep existing result visible
        placeholderStep = { type: 'border' };
      } else if (loadingType) {
        placeholderStep = { type: loadingType };
      } else if (inWidgetMode.value) {
        placeholderStep = { type: 'skeleton' };
      } else {
        placeholderStep = { type: 'text', preset: loadingPreset || 'reporting' };
      }
      return [placeholderStep];
    }

    const progressStep = {
      type: 'progress',
      text: loadingMessage.value,
      hint: { compact: inWidgetMode.value },
      startTime: loadingStartTime.value,
      ...(inWidgetMode.value ? { hoverText: loadingHoverMessage, onClick: loadingOnClick } : {}),
    };
    return [progressStep];
  });

  const isOverloaded = computed(() => pendingJobsCount.value > 0);
  const showOverloaded = computed(() => {
    if (!shouldShowOverlayUpdating) return false;
    if (showLoadingPlaceholder.value) return false;
    return isOverloaded.value;
  });
  const showLoadingOverlay = computed(() => {
    if (!shouldShowOverlayUpdating) return false;
    if (!isLoading.value) return false;

    /**
   * NOTE: Full loading sequence:
   *   1. placeholder
   *   2. overloaded (if true)
   *   3. progress
   */

    if (showLoadingPlaceholder.value) return true;

    // we will use a custom component to display overloaded status
    if (showOverloaded.value) return false;

    return true;
  });

  watch(() => loadingOptions.value.loadingMessage, (message: string | null) => {
    loadingMessage.value = message;
  });

  watch(() => isOverloaded.value, (newVal, oldVal) => {
    if (oldVal && !newVal) {
    // NOTE: we want to exclude pending time from the actual running time
      loadingStartTime.value = DateTime.now();
    }
  });

  return {
    loadingSteps,
    loadingStartTime,
    loadingMessage,
    loadingPlaceholderTimeout,
    setLoadingPlaceholder,
    showLoadingOverlay,
    showOverloaded,
    isOverloaded,
  };
};
