<template>
  <div
    v-h-loading.body="isFetchingEmbedPortal"
    class="size-full"
  >
    <div
      v-if="embedPortal"
      class="relative flex size-full bg-white"
      data-ci="embed-portal"
    >
      <EmbedPortalExplorer
        v-show="isExpanding"
        ref="explorerRef"
        class="min-w-[252px] border-r border-blue-gray-300 p-2"
      />
      <div class="flex flex-1 flex-col">
        <RouterView v-slot="{ Component }">
          <PreviewEmbedWarning object-label="portal" />
          <component
            :is="Component"
            @toggle-expand="toggleExpand"
            @go-to-dashboard="goToDashboard"
          />
        </RouterView>
      </div>
    </div>
    <div
      v-else-if="fetchEmbedPortalError"
      class="relative size-full bg-white p-2"
    >
      <HBanner
        type="danger"
        title="Embed Portal Error"
      >
        {{ fetchEmbedPortalError }}
      </HBanner>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed, onMounted, ref, useTemplateRef,
} from 'vue';
import { useTitle } from '@vueuse/core';
import { RouterView } from 'vue-router';
import { storeToRefs } from 'pinia';
import { HBanner } from '@holistics/design-system';
import PreviewEmbedWarning from '@/core/components/ui/banner/PreviewEmbedWarning.vue';
import { useEmbedPortalStore } from '../store/embedPortalStore';
import EmbedPortalExplorer from '../components/EmbedPortalExplorer.vue';

const embedPortalStore = useEmbedPortalStore();
const { isFetchingEmbedPortal, embedPortal, fetchEmbedPortalError } = storeToRefs(embedPortalStore);
const isExpanding = ref(true);

const toggleExpand = () => {
  isExpanding.value = !isExpanding.value;
};

onMounted(() => {
  embedPortalStore.fetchEmbedPortal();
});

const pageTitle = computed(() => embedPortal.value?.uname || 'Loading Embed Portal');
useTitle(pageTitle, {
  titleTemplate: '%s - Holistics',
});

const explorerRef = useTemplateRef('explorerRef');

function goToDashboard (id: number) {
  explorerRef.value?.goToNode({ id, type: 'Dashboard' });
}

</script>
