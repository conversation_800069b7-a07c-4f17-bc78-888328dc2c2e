<!--<docs>
</docs>-->
<template>
  <validation-form
    ref="form"
    as="div"
  >
    <h-modal
      class="dynamic-data-schedule"
      @dismiss="cancel"
    >
      <template #title>
        Data Delivery
      </template>
      <template #description>
        Automatically push data to your preferred channels.
        <a
          target="_blank"
          href="https://docs.holistics.io/docs/delivery/email-schedules"
        >
          Learn more
        </a>
      </template>
      <div class="data-schedule">
        <div class="flex flex-row pb-2">
          <div class="flex-col-3">
            <label class="pt-2 font-medium">Destination</label>
          </div>
          <div class="flex-col-9">
            <!-- if a dest is neither enabled nor coming soon, it will be hidden -->
            <!-- if a dest is not enabled but coming soon, it will be displayed as disabled -->
            <HSegmentedControl
              :model-value="destType"
              size="md"
              :items="destTypeConfigList.filter(config => checkDestEnabled(config) || checkDestComingSoon(config)).map((config) => {
                return {
                  label: config.displayName,
                  value: config.type,
                  icon: config.icon,
                  disabled: !canChangeToDest(config),
                  tooltip: tooltipForChangingDest(config) ?? undefined,
                  class: `ci-${config.kebabType}`
                }
              })"
              @update:model-value="changeDestType"
            />
          </div>
        </div>
        <div class="divider" />
        <div
          v-if="loading"
          class="pt-5"
        >
          <h-icon
            name="circle-notch"
            spin
          /> Loading...
        </div>
        <div v-else>
          <component
            :is="destTypeConfig?.destForm"
            ref="dest-form"
            v-model="mutatedDataSchedule.dest"
            :data-schedule="mutatedDataSchedule"
            :metadata="metadata"
            @dismiss="handleDestTypeChangeFailure"
          >
            <template
              v-if="!isSendOnce && testExecutionEnabled"
              #send-test-trigger
            >
              <DestFormSendTest
                class="mt-2"
                :dest="mutatedDataSchedule.dest"
                :can-perform-send-test="presetsReady && !isTestJobActive"
                :testing-status="testingStatus"
                @send-test="sendTest"
                @show-test-job-log="showTestJobLog"
              />
            </template>
            <template #schedule>
              <hr class="-mx-1 my-1 border-gray-300">
              <div class="flex flex-row py-5">
                <div class="flex-col-3">
                  <label class="pt-2 font-medium">Frequency</label>
                </div>
                <div class="flex-col-9">
                  <schedule-selector
                    :schedule="mutatedDataSchedule.schedule"
                    :timezone="timezone"
                    :support-send-once="!isEditing"
                    @handle="setSchedule"
                    @inited="e => initStatus = { ...initStatus, isScheduleInited: true }"
                  />
                </div>
              </div>
            </template>
            <template #filters>
              <filter-presets-form
                ref="filter-presets-form"
                v-model="mutatedDataSchedule.dynamic_filter_presets"
                presettable-type="EmailSchedule"
                :presettable-id="mutatedDataSchedule.id || null"
                :filter-holdable-type="sourceInfo.type"
                :filter-holdable-id="sourceInfo.id"
                :dashboard-filter-conditions="dashboardFilterConditions"
                @ready="e => presetsReady = true"
                @inited="e => initStatus = { ...initStatus, isFilterInited: true }"
              />
            </template>
          </component>
        </div>
      </div>
      <template
        v-if="mutatedDataSchedule"
        #footer
      >
        <div class="flex justify-between">
          <div
            class="flex items-center"
          >
            <div
              v-if="isSendOnce && testingStatus"
              class="test-status ci-test-status cursor-pointer"
              @click="showTestJobLog"
            >
              <JobStatus :status="testingStatus" />
            </div>
          </div>
          <div>
            <HButton
              type="secondary-default"
              class="ci-close-modal"
              @click="cancel()"
            >
              Cancel
            </HButton>
            <HButton
              type="primary-highlight"
              class="ci-submit-btn ml-1"
              :disabled="!presetsReady || submitting || (isSendOnce && isTestJobActive)"
              @click.prevent="onSubmit"
            >
              <h-icon
                v-if="submitting || (isSendOnce && isTestJobActive)"
                name="circle-notch"
                class="align-top"
                spin
              />
              {{ isSendOnce ? 'Send Now' : 'Save' }}
            </HButton>
          </div>
        </div>
      </template>
    </h-modal>
  </validation-form>
</template>
<script>
import { HTooltip, HButton, HSegmentedControl } from '@holistics/design-system';
import {
  find, cloneDeep, get, assign, merge, noop, isEqual,
} from 'lodash';
import { Form as ValidationForm } from 'vee-validate';
import Schedule from '@/es6/schedule';
import ScheduleSelector from '@/modules/DataSchedule/components/ScheduleSelector.vue';
import * as FeatureToggle from '@/core/services/featureToggle';
import EmailSchedules from '@/es6/email_schedules';
import { DEST_LIST } from '@/modules/DataSchedule/constants/dests';
import * as Notifier from '@/core/services/notifier';
import * as Ajax from '@/core/services/ajax';
import Jobs from '@/jobs/jobs';
import JobStatus from '@/vue_components/job_status.vue';
import JobLogModal from '@/ui/modals/job_log';
import FilterPresetsForm from '@/modules/DynamicFilterPresettable/components/ui/FilterPresetsForm.vue';
import SlackDestForm from '@/modules/DataSchedule/components/forms/SlackDestForm.vue';
import EmailDestForm from '@/modules/DataSchedule/components/forms/EmailDestForm.vue';
import DocsPopover from '@/core/components/ui/DocsPopover.vue';
import GsheetDestForm from '@/modules/DataSchedule/components/forms/GoogleSheetDestForm.vue';
import SftpDestForm from '@/modules/DataSchedule/components/forms/SftpDestForm.vue';
import { isActiveJob } from '@/modules/Jobs/helpers/checkStatus';
import DestFormSendTest from '@/modules/DataDelivery/components/DestFormSendTest.vue';
import { buildDashboardVizConditionsDisplayText } from '@/modules/DataAlerts/services/buildDataAlertPreviewMessage';
import { buildDateVariables } from '@/modules/DataSchedule/services/dynamicVariables/buildDateVariables';

const CLIENT_TIMEZONE = Schedule.getBrowserTimeZone();
export default {
  components: {
    HTooltip,
    HButton,
    ValidationForm,
    EmailDestForm,
    SlackDestForm,
    JobStatus,
    ScheduleSelector,
    DocsPopover,
    FilterPresetsForm,
    GsheetDestForm,
    SftpDestForm,
    DestFormSendTest,
    HSegmentedControl,
  },
  props: {
    isOnce: {
      type: Boolean,
      default: false,
    },
    dataSchedule: {
      type: Object,
      required: true,
    },
    source: {
      type: Object,
      required: true,
    },
    dashboardFilterConditions: {
      type: Object,
      default: null,
    },
  },
  emits: ['dismiss', 'resolve'],
  data () {
    const destTypeConfigList = DEST_LIST;
    return {
      sourceInfo: {},
      loading: false,
      mutatedDataSchedule: null,
      initialDataSchedule: null,
      initStatus: {
        isScheduleInited: false,
        isFilterInited: false,
      },
      presetsReady: false,
      submitting: false,
      testingStatus: '',
      destTypeConfigList,
      destTypeConfig: destTypeConfigList[0],
      dateVariables: buildDateVariables(),
      previousDestType: null,
    };
  },
  computed: {
    inited () {
      return this.initStatus.isScheduleInited && this.initStatus.isFilterInited;
    },
    isEditing () {
      return this.dataSchedule && !!this.dataSchedule.id;
    },
    destType: {
      get () {
        return get(this.mutatedDataSchedule, 'dest_type');
      },
      set (newVal) {
        this.mutatedDataSchedule.dest_type = newVal;
      },
    },
    isSendOnce () {
      return this.mutatedDataSchedule.schedule.isSendOnce;
    },
    timezone () {
      return CLIENT_TIMEZONE;
    },
    testExecutionEnabled () {
      return FeatureToggle.check('data_schedules:test_execution');
    },
    isTestJobActive () {
      return isActiveJob(this.testingStatus);
    },
    dashboardControlText () {
      return buildDashboardVizConditionsDisplayText(this.mutatedDataSchedule?.dynamic_filter_presets || [], false, this.destType);
    },
    metadata () {
      if (!this.mutatedDataSchedule) return {};

      const dashboardPath = new URL(this.source?.path, window.location.origin).toString();
      return {
        dashboard_title: this.source?.title || '',
        dashboard_url: dashboardPath,
        dashboard_controls: this.dashboardControlText,
        ...this.dateVariables,
      };
    },
  },
  // There are many children components that mutate the mutatedDataSchedule in their init operations
  // This watcher is used to check that all the initial processes are finish to clone the initial state for checking the changes.
  // https://app.asana.com/0/921567352315699/1198900796680618
  watch: {
    inited: {
      handler () {
        if (this.inited && !this.initialDataSchedule) {
          this.initialDataSchedule = cloneDeep(this.mutatedDataSchedule);
        }
      },
    },
  },
  created () {
    this.init();
  },
  methods: {
    async init () {
      this.loading = true;
      this.initDestTypeConfig();
      this.mutatedDataSchedule = await this.prepareDataSchedule();
      this.mutatedDataSchedule.schedule.isSendOnce = this.isOnce;
      this.loading = false;
    },
    initDestTypeConfig () {
      if (this.dataSchedule && this.dataSchedule.dest_type) {
        this.destTypeConfig = find(this.destTypeConfigList, config => config.type === this.dataSchedule.dest_type);
      }
    },
    checkDestEnabled (destConfig) {
      return destConfig.checkEnabled({ sourceType: this.source.source_type, version: '3' });
    },
    checkDestComingSoon (destConfig) {
      return destConfig.checkComingSoon({ sourceType: this.source.source_type, version: '3' });
    },
    canChangeToDest (destConfig) {
      return !this.isEditing && this.checkDestEnabled(destConfig);
    },
    tooltipForChangingDest (destConfig) {
      if (destConfig.type === this.destType) return null;
      if (this.isEditing) return 'You can\'t change the destination type, please create a new schedule';
      if (!this.checkDestEnabled(destConfig)) return 'Not supported yet';

      return null;
    },
    async changeDestType (newDestType) {
      try {
        if (newDestType === 'SlackDest') {
          this.loading = true;
          const authorized = await this.$slackIntegration.authorizeSlack();
          if (!authorized) return;
        }
        this.previousDestType = this.destType;
        this.updateDestType(newDestType);
      } catch (error) {
        Ajax.handleAjaxError(error);
      } finally {
        this.loading = false;
      }
    },
    async prepareDataSchedule () {
      let dataSchedule = {};
      if (this.isEditing) {
        try {
          dataSchedule = await EmailSchedules.get(this.dataSchedule.id);
          dataSchedule = merge(dataSchedule, { source_data: this.source.source_data }); // getting extra data
          this.sourceInfo = { id: this.dataSchedule.source_id, type: this.dataSchedule.source_type };
        } catch (err) {
          Ajax.handleAjaxError(err, 'Error getting Email Schedule from server');
        }
      } else {
        try {
          dataSchedule = this.dataSchedule || {};
          dataSchedule.source = await EmailSchedules.getSource(this.source.source_id, this.source.source_type);
          assign(dataSchedule, this.source);
          dataSchedule.dest_type = this.destTypeConfig.type;
          dataSchedule.dest = {
            type: this.destTypeConfig.type,
          };
          this.sourceInfo = { id: this.source.source_id, type: this.source.source_type };
        } catch (err) {
          Ajax.handleAjaxError(err, 'Error getting source object');
        }
      }
      // fill the data schedule missing fields with default values
      return EmailSchedules.new(dataSchedule);
    },
    async validateDestForm () {
      const destForm = this.$refs['dest-form'];
      if (destForm && destForm.validate) {
        return destForm.validate();
      }
      return { valid: true };
    },
    getSubmittingDs () {
      const ds = cloneDeep(this.mutatedDataSchedule);
      ds.dest_type = this.destTypeConfig.type;
      ds.schedule = cloneDeep(this.mutatedDataSchedule.schedule);
      return ds;
    },
    async sendOnce () {
      if (isActiveJob(this.testingStatus)) return;
      this.testingStatus = 'submitting';
      let jobInfo = null;
      try {
        jobInfo = await this.submitSendOnce();
      } catch (err) {
        this.testingStatus = Ajax.errorMessageFromAjax(err);
      }
      if (jobInfo) {
        this.testJobId = jobInfo.job_id;
        this.pollTestJob(jobInfo);
      }
    },
    async sendTest () {
      if (!this.presetsReady) return;
      const validated = await this.validateForm();
      if (!validated) return;
      const confirmed = await this.$modal.confirm('Send test now?', 'Send test for this schedule immediately?');
      if (!confirmed) return;
      await this.sendOnce();
    },
    pollTestJob (jobInfo) {
      Jobs.pollResults(jobInfo, {
        getResultsFunc: jobId => {
          const url = `/jobs/${jobId}/get_results.json`;
          return Ajax.standardGet(url);
        },
        successFunc: () => {
          const typeName = this.destTypeConfig.displayName;
          const successMsg = `Successfully send dashboard to ${typeName}`;
          Notifier.success(successMsg);
        },
        errorFunc: (err) => { this.testingStatus = Ajax.errorMessageFromAjax(err); },
        cacheFunc: noop,
        statusFunc: ({ status }) => { this.testingStatus = status; },
      });
    },
    showTestJobLog () {
      JobLogModal.open(this.testJobId);
    },
    getOnceDs () {
      const ds = this.getSubmittingDs();
      ds.id = null;
      ds.dest.id = null;
      ds.schedule.id = null;
      return ds;
    },
    submitSendOnce () {
      const ds = this.getOnceDs();
      const originalDsId = get(this.dataSchedule, 'id', null);
      return EmailSchedules.testExecute(ds, originalDsId);
    },
    close () {
      this.$emit('dismiss');
    },
    updateDestType (destType) {
      const index = this.destTypeConfigList.findIndex(config => config.type === destType);
      this.destTypeConfig = this.destTypeConfigList[index];
      this.destType = destType;
    },
    handleDestTypeChangeFailure () {
      if (!this.previousDestType) this.close();
      this.updateDestType(this.previousDestType);
    },
    async validateForm () {
      const { valid: isValidDest } = await this.validateDestForm();
      if (!isValidDest) return false;
      const { valid: isValidForm } = await this.$refs.form.validate();
      if (!isValidForm) return false;
      return true;
    },
    async onSubmit () {
      this.submitting = true;
      const validated = await this.validateForm();
      if (!validated) {
        this.submitting = false;
        return;
      }
      if (this.mutatedDataSchedule.schedule.isSendOnce) {
        await this.sendOnce();
        this.submitting = false;
      } else {
        const ds = this.getSubmittingDs();
        const actioned = this.isEditing ? 'updated' : 'added';
        const typeName = this.destTypeConfig.displayName;
        const successMsg = `${typeName} Schedule successfully ${actioned}`;
        try {
          if (!this.isEditing) {
            await EmailSchedules.create(ds);
          } else {
            await EmailSchedules.update(ds);
          }
          Notifier.success(successMsg);
          this.$emit('resolve', null);
        } catch (err) {
          Ajax.handleAjaxError(err);
        }
        this.submitting = false;
      }
    },
    async cancel () {
      const isDirty = !isEqual(this.mutatedDataSchedule, this.initialDataSchedule);

      if (isDirty) {
        const confirmed = await this.$modal.confirm('Discard changes?', 'Changes you made will not be submitted.');
        if (!confirmed) {
          return;
        }
      }
      this.close();
    },
    setSchedule (schedule) {
      this.mutatedDataSchedule.schedule = schedule;
    },
  },
};
</script>
<style lang="postcss" scoped>
.dynamic-data-schedule {
  .test-status {
    text-decoration: none;
  }
}
</style>
