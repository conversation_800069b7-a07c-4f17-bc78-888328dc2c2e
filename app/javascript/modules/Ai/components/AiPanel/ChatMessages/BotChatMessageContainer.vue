<script setup lang="ts">
import { DateTime } from 'luxon';
import { HButton, HTooltip, HIcon } from '@holistics/design-system';
import { Feedback } from '@/modules/Ai/types';
import { useTemplateRef } from 'vue';

withDefaults(defineProps<{
  startTimestamp?: DateTime | null,
  showButtons?: boolean
  retriable?: boolean
  feedback?: Feedback | undefined
  isShowingSuggestions?: boolean,
  enabledSuggestions?: boolean
  suggestionButtonTooltip?: string
  restoredFromId?: string | undefined,
  restoring?: boolean | undefined,
}>(), {
  startTimestamp: null,
  showButtons: false,
  retriable: false,
  feedback: undefined,
  isShowingSuggestions: false,
  enabledSuggestions: false,
  suggestionButtonTooltip: '',
  restoredFromId: undefined,
  restoring: undefined,
});

/* eslint-disable-next-line no-spaced-func, func-call-spacing */
const emit = defineEmits<{
  (e: 'retry'): void,
  (e: 'feedback', type: Feedback): void,
  (e: 'followUp', triggerElement: HTMLElement): void,
  (e: 'highlight', id: string): void,
}>();

function like () {
  emit('feedback', 'like');
}

function dislike () {
  emit('feedback', 'dislike');
}

const followUpButton = useTemplateRef('followUpButton');

function followUp () {
  if (followUpButton.value) {
    emit('followUp', followUpButton.value.$el);
  }
}

</script>

<template>
  <div
    class="ai-bot-chat-message-container group flex"
    :class="{ 'mt-5': restoredFromId }"
  >
    <div
      class="z-10 mr-2 size-6 flex-shrink-0 rounded-full bg-[#05264C] p-[2px]"
    >
      <img src="https://cdn.holistics.io/images/holistics-logo-symbol.png">
    </div>
    <div
      class="min-w-0 flex-1"
    >
      <template v-if="restoredFromId">
        <div class="mb-2 mt-1 flex gap-1 text-xs font-medium text-gray-600">
          <HIcon name="history" />
          <span>{{ restoring ? 'Restoring' : 'Restored' }}</span>
          <span
            class="cursor-pointer underline hover:text-gray-800"
            @click="emit('highlight', restoredFromId)"
          >
            past result
          </span>
        </div>
      </template>

      <slot />

      <!--------START BUTTONS GROUP-------------->
      <div class="relative flex items-center">
        <div
          v-if="showButtons"
          class="flex flex-1 gap-1 font-medium"
        >
          <HTooltip
            :content="suggestionButtonTooltip ?? ''"
            :disabled="enabledSuggestions"
          >
            <HButton
              v-if="retriable"
              ref="followUpButton"
              type="clear-default"
              :class="{
                'hover:!text-gray-800': enabledSuggestions,
                '!text-gray-800': isShowingSuggestions,
                '!text-gray-500': !isShowingSuggestions
              }"
              class="ml-[-3px]"
              label="Follow up"
              :icon="isShowingSuggestions ? 'lightbulb-on' : 'lightbulb'"
              :disabled="!enabledSuggestions"
              @click.stop="followUp"
            />
          </HTooltip>
          <div class="flex-1" />
          <HTooltip
            content="Retry"
            disable-hoverable-content
          >
            <HButton
              v-if="retriable"
              type="clear-default"
              icon="refresh"
              class="!text-gray-500 hover:!text-gray-700"
              unified
              @click.stop="emit('retry')"
            />
          </HTooltip>
          <HTooltip
            content="Helpful"
            disable-hoverable-content
          >
            <HButton
              type="clear-default"
              :icon="feedback === 'like' ? 'like-solid' : 'like'"
              unified
              class="!text-gray-500 transition duration-100 ease-linear hover:rotate-[-25deg] hover:!text-gray-700"
              :class="retriable ? '' : 'opacity-0 group-hover:opacity-100'"
              @click="like"
            />
          </HTooltip>
          <HTooltip
            content="Not helpful"
            disable-hoverable-content
          >
            <HButton
              type="clear-default"
              :icon="feedback === 'dislike' ? 'like-solid' : 'like'"
              unified
              class="rotate-180 !text-gray-500 transition duration-300 ease-in-out hover:rotate-[165deg] hover:!text-gray-700"
              :class="retriable ? '' : 'opacity-0 group-hover:opacity-100'"
              @click="dislike"
            />
          </HTooltip>
        </div>
      </div>
      <!--------END BUTTONS GROUP-------------->
    </div>
  </div>
</template>

<style lang="postcss">
.ai-thought, .ai-answer {
  @apply text-xs font-normal text-gray-800;

  button {
    @apply p-0 pl-3;
  }
  .chat-markdown, &.chat-markdown {
    ol, ul {
      @apply pl-6;
    }
    h1, h2, h3, h4, h5, h6 {
      @apply text-xs font-medium !mb-2 !mt-3;
    }
    p {
      @apply !mb-0 text-xs/5;
    }
    strong {
      @apply !font-medium;
    }
    li {
      @apply !mt-1 text-xs/5
    }
    ul {
      @apply !mt-1;
    }
    #references + ul {
      @apply !mt-0;
      li {
        @apply !mt-0;
      }
    }
    code {
      @apply px-2 py-px bg-blue-50 text-blue-800 rounded-xl font-medium border-blue-300 border font-sans mx-px;
    }
    pre {
      @apply bg-white !mt-2 border-gray-300;
      code {
        @apply font-normal text-xs/4 font-mono border-none bg-white;
      }
    }
  }
}
.ai-bot-chat-message-container {
  [data-hui-section="trigger"] {
    > button {
      @apply px-0
    }
    > button > div > div {
      @apply flex-none;
    }
    > button > div > div:nth-child(2) {
      @apply ml-1;
    }
   .h-icon {
      @apply text-gray-600 size-3;
    }
  }
  [data-hui-comp="accordion"] {
    &[data-hui-expanded="true"], &:hover {
      [data-hui-section="label"] {
        @apply text-gray-700;
      }

      [data-hui-section="trigger"] {
        .h-icon {
          @apply text-gray-700;
        }
      }
    }
  }
  [data-hui-section="label"] {
    @apply text-gray-600;
  }
  [data-hui-section="body"] {
    >div{
      @apply p-0;
    }
    @apply border-t-0 border-l-2 bg-gray-100 mt-2 px-3 pb-2 font-sans rounded-none;
  }
}
</style>
