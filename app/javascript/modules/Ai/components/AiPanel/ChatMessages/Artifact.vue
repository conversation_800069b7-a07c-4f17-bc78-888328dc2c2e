<script setup lang="ts">
import { Artifact } from '@/modules/Ai/agents/types';
import { RestorableStatus } from '@/modules/Ai/types';
import VIZ_ICONS from '@/modules/Viz/constants/vizIcons';
import VueMarkdown from '@/vue_components/vue_markdown.vue';
import {
  HButton, HIcon, HTooltip, IconName, ICONS,
} from '@holistics/design-system';
import { computed, ComputedRef } from 'vue';

const props = defineProps<{
  artifact: Artifact
  isViewing: boolean
  restorableStatus: RestorableStatus,
  highlighted?: boolean,
}>();

const emit = defineEmits<{
  'restore': [],
}>();

const chartIcon = computed(() => {
  const iconName = VIZ_ICONS[props.artifact.vizSetting.viz_type];
  if (!Object.keys(ICONS).includes(iconName)) {
    return 'chart/combination';
  }
  return iconName as IconName;
});

interface RestoreState {
  tooltip: string
  tooltipDisabled?: boolean
  button: string
  buttonDisabled?: boolean
  icon: IconName
}
const restoreState: ComputedRef<RestoreState | null> = computed(() => {
  const status = props.restorableStatus;
  switch (status) {
    case 'restorable':
      return {
        tooltip: 'Restore this result',
        button: 'Restore',
        icon: 'redo',
      };
    case 'restorable-pending':
      return {
        tooltip: 'Pending (another action is in progress)',
        button: 'Restore',
        buttonDisabled: true,
        icon: 'redo',
      };
    case 'current':
      return {
        tooltip: 'Latest result',
        button: 'Current',
        buttonDisabled: true,
        icon: 'check',
      };
    case 'restored':
      return {
        tooltip: 'Restored',
        button: 'Restored',
        buttonDisabled: true,
        icon: 'history',
      };
    case 'non-restorable':
      return null;
    default:
      throw new Error(`Unhandled status: ${status satisfies never}`);
  }
});

</script>

<template>
  <div
    v-if="artifact.type === 'chartArtifact'"
    class="ai-artifact relative flex items-center gap-3 rounded border p-3 font-sans shadow transition-[border-color] duration-500"
    :class="{ 'border-blue-500': highlighted }"
  >
    <div class="flex items-center justify-center">
      <HIcon
        :name="chartIcon"
        size="lg"
        class="rounded border border-gray-300 bg-white p-2 shadow"
      />
    </div>
    <div class="flex items-center justify-center">
      <VueMarkdown
        :source="artifact.name"
      />
    </div>
    <div
      v-if="restoreState"
      class="restore-buttons"
    >
      <HTooltip
        :content="restoreState.tooltip"
        disable-hoverable-content
        placement="top"
        :disabled="restoreState.tooltipDisabled"
      >
        <HButton
          :type="restoreState.buttonDisabled ? 'clear-default' : 'secondary-default'"
          class="size-8"
          :disabled="restoreState.buttonDisabled"
          @click="emit('restore')"
        >
          <HIcon
            :name="restoreState.icon"
            size="lg"
          />
        </HButton>
      </HTooltip>

      <!-- <HTooltip
        content="Coming soon"
        disable-hoverable-content
      >
        <HButton
          type="secondary-default"
          class="rounded-r-full !border border-gray-300 !px-2 !py-[2px] !text-2xs !text-gray-800"
        >
          <HIcon
            name="eye-open"
            class="text-gray-500"
          />
          View
        </HButton>
      </HTooltip> -->
    </div>
  </div>
</template>

<style lang="postcss">
.ai-artifact {
  strong {
    @apply !font-medium;
  }
  .restore-buttons {
    .hui-btn.hui-btn-secondary-default[disabled] {
      @apply !bg-white !cursor-default;
    }
  }
}

</style>
