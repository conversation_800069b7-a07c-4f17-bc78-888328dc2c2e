# typed: true

module Api::V2
  class DataScheduleSerializer
    include Alba::Resource
    include Serializers::PermissionHelpers

    attributes :id, :source_id, :source_type

    one :schedule, resource: Api::V2::ScheduleSerializer
    one :last_run, key: :last_run_job, resource: LastRunSerializer

    attribute :dynamic_filter_presets do |object|
      T.bind(self, DataScheduleSerializer)

      ::DynamicFilterPresetSerializer.new(
        object.dynamic_filter_presets, # dynamic_filter, dynamic_filter_defintion should be preloaded in the controller
        params: {
          current_ability: params[:current_ability],
          dynamic_filter_holdable: object.source,
        },
      ).as_json
    end

    attribute :dest do |object|
      T.bind(self, DataScheduleSerializer)

      ScheduleDests::DestSerializerHelper.dest_serializer_for(object: object, current_user: params[:current_user]).as_json
    end

    attribute :permissions do |object|
      T.bind(self, DataScheduleSerializer)

      {
        can_crud: current_ability.can?(:crud, object),
        can_execute: current_ability.can?(:execute, object),
      }
    end
  end
end
