# typed: true
# frozen_string_literal: true

class ExternalUserItem < ApplicationRecord
  include TenantScope

  belongs_to :item, polymorphic: true
  belongs_to :owner, class_name: 'User', foreign_key: 'owner_id'

  validates_presence_of :tenant_id, :item, :owner
  validates_inclusion_of :is_personal, in: [true, false]

  sig { params(user: User).returns(T::<PERSON><PERSON><PERSON>) }
  def in_org_workspace_of?(user)
    tenant_id == user.tenant_id &&
      !is_personal &&
      owner&.external_user&.external_org_id == user.external_user&.external_org_id
  end

  sig { params(user: User).returns(T::<PERSON><PERSON><PERSON>) }
  def in_personal_workspace_of?(user)
    !!(tenant_id == user.tenant_id &&
      is_personal &&
      owner&.external_user&.external_org_id == user.external_user&.external_org_id &&
      owner_id == user.id)
  end
end
