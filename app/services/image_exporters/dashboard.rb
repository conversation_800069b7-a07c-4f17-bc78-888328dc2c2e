# typed: false

module ImageExporters
  class Dashboard
    GRID_CELL_SIZE = 65
    GRID_MARGIN = 4
    RENDERER_PACK = 'pptr_dashboard'.freeze

    def initialize(
      dashboard,
      job:,
      permission_rules:,
      query_processing_timezone:,
      widgets: nil,
      widget_viz_conditions_map: {},
      check_widget_permission: true,
      tab_unames: []
    )
      @dashboard = dashboard
      @widget_viz_conditions_map = widget_viz_conditions_map || {}
      @query_processing_timezone = query_processing_timezone
      @job = job
      @permission_rules = permission_rules
      @check_widget_permission = check_widget_permission
      # <PERSON><PERSON> selected widgets only or all widgets if not specified
      @widgets = widgets
      @tab_unames = tab_unames

      # If export tabs, we will handle this action in ImageExporters::DashboardsV4::Tab
      # TODO: Split export dashboard by version instead of mix them together
      return if is_export_tabs?

      @widgets ||=
        if dashboard.is_v4?
          ::DashboardsV4::Services::MaterializedBlockOperations.get_all_blocks(@dashboard)
        else
          @dashboard.dashboard_widgets
        end
      if check_widget_permission
        @widget_ability = DashboardWidgetsAbility.new(job.user)
        Abilities::Services::AllowPublicDrillthroughService.new.call(@widget_ability)
      end
    end

    def is_export_tabs?
      first_view = @dashboard.definition_struct&.views&.first
      @dashboard.is_v4? && first_view.is_a?(::DashboardsV4::Values::Definition::TabLayout) && !@tab_unames.empty? && FeatureToggle.active?('exporting:export_tabs_on_canvas_dashboard', @dashboard.tenant_id)
    end

    # @param widget_cache_map: can be either
    #   * [Hash] map between widget ids and their cache key. Used to generate data for each widget from cache if `results_data` is not provided
    #   * [Dashboards::Values::ReportWidgetCacheMap]
    # @param format [String]: output format
    # @param results_data [Array]: widgets' results data that will be passed into the renderer packs. Use this to export same data multiple times without having to generate the data again
    def export(widget_cache_map, format: 'png', results_data: nil)
      if is_export_tabs?
        return ImageExporters::DashboardsV4::Tab.new(
          @dashboard,
          job: @job,
          permission_rules: @permission_rules,
          query_processing_timezone: @query_processing_timezone,
          widget_viz_conditions_map: @widget_viz_conditions_map,
          widgets: @widgets,
          tab_unames: @tab_unames,
        ).export(widget_cache_map, format: format, results_data: results_data)
      end

      results = results_data || generate_results_data(widget_cache_map)
      renderer_input, page_width, page_height = @dashboard.is_v4? ? generate_renderer_input_v4(results) : generate_renderer_input(results)

      PuppeteerRunner.new(renderer_pack: RENDERER_PACK, job: @job).execute(
        format: format,
        width: page_width,
        height: page_height,
        renderer_input: renderer_input,
        timeout: [@widgets.size * 3, 40].max,
      ) do |outfile, log|
        if format == 'pdf'
          @job.logger.info 'Post processing pdf'
          page_crop_map = ImageExporters::Helper.get_page_crop_map_from_log(log, page_width: page_width,
                                                                                 page_height: page_height,)
          Pdf::Crop.new(outfile.path, job: @job).execute(page_crop_map: page_crop_map)
        else
          File.read(outfile.path)
        end
      end
    end

    def generate_results_data(widget_cache_map)
      ImageExporters::Services::WidgetResultGenerator.new(
        widgets: @widgets,
        job: @job,
        permission_rules: @permission_rules,
        query_processing_timezone: @query_processing_timezone,
        widget_viz_conditions_map: @widget_viz_conditions_map,
        check_widget_permission: @check_widget_permission,
        should_build_table_options: !@dashboard.is_v4? && !@dashboard.is_v3?,
      ).generate(widget_cache_map)
    end

    private

    def generate_renderer_input(results)
      grid = calculate_grid

      # Add some paddings and space for header
      page_width = (grid[:cols] * (grid[:cell_size] + grid[:margin])) + grid[:margin] + 20
      # Add great space for height because the header can span many rows. The output will be cropped later
      page_height = (grid[:rows] * (grid[:cell_size] + grid[:margin])) + grid[:margin] + 500

      renderer_input = {
        dashboard: {
          id: @dashboard.id,
          title: @dashboard.title,
          grid: grid,
          settings: {
            timezone: @query_processing_timezone,
          },
        },
        results: results,
      }
      [renderer_input, page_width, page_height]
    end

    def generate_renderer_input_v4(results)
      dashboard_ability = DashboardsAbility.new(@job.user)
      renderer_input = {
        dashboard: {
          id: @dashboard.id || @dashboard.uname,
          title: @dashboard.title,
          version: @dashboard.version,
          definition: dashboard_definition_with_filter,
          definition_aml: '',
          permissions: {
            can_crud: dashboard_ability.can?(:crud, @dashboard),
            can_read: dashboard_ability.can?(:read, @dashboard),
          },
          settings: {
            timezone: @query_processing_timezone,
          },
        },
        results: results,
      }
      # Get first page size for dashboard
      first_view = @dashboard.definition_struct&.views&.first

      # tab layout: get the first tab
      first_view = first_view['tabs']&.first if first_view.is_a?(::DashboardsV4::Values::Definition::TabLayout)

      page_width, page_height = ImageExporters::DashboardsV4::Helper.extract_page_width_and_height(first_view)

      [renderer_input, page_width, page_height]
    end

    def calculate_grid
      cols = 8
      rows = 1
      @widgets.each do |dw|
        grid_opts = { col: 0, row: 0, sizeX: 0, sizeY: 0 }.merge(dw.grid || {})
        widget_right = grid_opts[:col] + grid_opts[:sizeX]
        widget_bottom = grid_opts[:row] + grid_opts[:sizeY]
        cols = [cols, widget_right].max
        rows = [rows, widget_bottom].max
      end

      {
        cols: cols,
        rows: rows,
        cell_size: GRID_CELL_SIZE,
        margin: GRID_MARGIN,
      }
    end

    def dashboard_definition_with_filter
      ImageExporters::DashboardsV4::Helper.dashboard_definition_with_filter(@dashboard, @widget_viz_conditions_map)
    end
  end
end
