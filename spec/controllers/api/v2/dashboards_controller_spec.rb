# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Api::V2::DashboardsController, :api, type: :controller do
  include_context 'query_model_dataset_based_report'

  let(:user) { get_test_admin }
  let(:explorer) do
    FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
    e = get_test_explorer
    e.allow_authentication_token = true

    e
  end
  let(:tenant) { get_test_tenant }
  let(:qr) { query_model_dataset_based_report }
  let(:dashboard) do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global(Dashboard::FT_V1_CREATION, false)
    create(:dashboard, category_id: 1, version: 3, title: 'My Dashboard', owner_id: user.id,
                       settings: { timezone: 'Pacific/Midway' },)
  end
  let!(:report_widget) do
    create(:dashboard_widget, source: qr, dashboard: dashboard, grid: { col: 12, row: 8, sizeX: 12, sizeY: 8 })
  end
  let!(:dynamic_filter) do
    create(:dynamic_filter, dynamic_filter_holdable: dashboard, order: 0, drillthrough_enabled: false)
  end
  let(:name_field_path) do
    DataModeling::Values::FieldPath.new(field_name: 'name', model_id: query_data_model.id)
  end
  let!(:mapping) do
    create(:dynamic_filter_mapping, dynamic_filter: dynamic_filter, viz_conditionable: report_widget,
                                    field_path: name_field_path,)
  end
  let!(:report_widget2) do
    create(:dashboard_widget, source: qr, dashboard: dashboard, title: 'ey yo',
                              grid: { col: 0, row: 0, sizeX: 12, sizeY: 8 },)
  end
  let!(:dynamic_filter2) do
    create(:dynamic_filter, dynamic_filter_holdable: dashboard, order: 1, drillthrough_enabled: true)
  end
  let!(:data_modeling_condition_1) do
    {
      dynamic_filter_id: dynamic_filter2.id,
      condition: { operator: 'is', values: ['ahihi'] },
    }
  end
  let!(:data_modeling_condition_2) do
    {
      dynamic_filter_id: dynamic_filter2.id,
      condition: { operator: 'is', values: ['no hope'] },
    }
  end
  let(:dashboard_filter_conditions) do
    [data_modeling_condition_1, data_modeling_condition_2]
  end
  let!(:single_filter_states) do
    dashboard_filter_conditions.map do |c|
      DynamicFilters::Values::SingleFilterState.new(
        dynamic_filter_id: c[:dynamic_filter_id],
        selected_condition: DataModeling::Values::Condition.new(
          operator: c[:condition][:operator],
          values: c[:condition][:values],
        ),
      )
    end
  end

  def get_dashboard_permission(dashboard, for_detail = false)
    permissions = {
      can_crud: user.can?(:crud, dashboard),
      can_read: user.can?(:read, dashboard),
      can_update: user.can?(:update, dashboard),
      can_share: user.can?(:share, dashboard),
      can_export: user.can?(:export, dashboard),
      can_export_data: user.can?(:export_data, dashboard),
      can_pin: user.can?(:pin, dashboard),
    }

    permissions.merge!(can_live_update: user.can?(:live_update, dashboard)) if for_detail

    permissions
  end

  context 'with valid api token' do
    before do
      set_token_header(user)
    end

    def response_result
      JSON.parse(response.body)
    end

    describe 'GET #show' do
      let(:expected_response) do
        {
          dashboard: {
            id: dashboard.id,
            owner_id: user.id,
            title: 'My Dashboard',
            category_id: dashboard.category_id,
            version: dashboard.version,
            path: dashboard.to_url,
            project_id: nil,
            uname: nil,
            permissions: get_dashboard_permission(dashboard, true),
            render_options: nil,
            widgets: [
              {
                id: report_widget.id,
                permissions: {
                  can_explore: true,
                  can_export: true,
                  can_export_data: true,
                },
                source_id: qr.id,
                source_type: 'QueryReport',
                source_title: report_widget.display_title,
              },
              {
                id: report_widget2.id,
                permissions: {
                  can_explore: true,
                  can_export: true,
                  can_export_data: true,
                },
                source_id: qr.id,
                source_type: 'QueryReport',
                source_title: report_widget2.display_title,
              },
            ],
            dynamic_filters: [
              {
                id: dynamic_filter.id,
                order: 0,
                uname: 'text_filter',
                drillthrough_enabled: false,
                mappings: [
                  {
                    aggregation: nil,
                    field_path: {
                      data_set_id: nil,
                      field_name: 'name',
                      is_metric: false,
                      joins_path: nil,
                      model_id: query_data_model.id,
                    },
                    id: mapping.id,
                    permissions: {
                      crud: true,
                    },
                    viz_conditionable_id: report_widget.id,
                    viz_conditionable_type: 'DashboardWidget',
                  },
                ],
                permissions: {
                  crud: true,
                  use: true,
                  read: true,
                },
                definition: {
                  id: dynamic_filter.definition.id,
                  label: dynamic_filter.definition.label,
                  constraints: {},
                  default_condition: {
                    modifier: nil,
                    operator: 'is',
                    values: ['alice'],
                    options: nil,
                  },
                  input_type: nil,
                  filter_type: 'string',
                  filter_source: {
                    source_type: 'ManualFilterSource',
                    manual_options: [
                      {
                        value: 'alice',
                      },
                      {
                        value: 'bob',
                      },
                    ],
                  },
                  is_shareable: nil,
                },
              },
              {
                id: dynamic_filter2.id,
                order: 1,
                uname: 'text_filter_2',
                drillthrough_enabled: true,
                mappings: [],
                permissions: {
                  crud: true,
                  use: true,
                  read: true,
                },
                definition: {
                  id: dynamic_filter2.definition.id,
                  label: dynamic_filter2.definition.label,
                  constraints: {},
                  default_condition: {
                    modifier: nil,
                    operator: 'is',
                    values: ['alice'],
                    options: nil,
                  },
                  input_type: nil,
                  filter_type: 'string',
                  filter_source: {
                    source_type: 'ManualFilterSource',
                    manual_options: [
                      {
                        value: 'alice',
                      },
                      {
                        value: 'bob',
                      },
                    ],
                  },
                  is_shareable: nil,
                },
              },
            ],
            definition: nil,
            definition_aml: nil,
            from_aml: false,
            repo_version: nil,
            settings: {
              allow_to_change_timezone: nil,
              autoload_disabled: false,
              date_drill_enabled: nil,
              timezone: 'Pacific/Midway',
            },
          },
        }
      end

      it 'renders dashboard with filters and widgets' do
        params = { id: dashboard.id }
        get :show, params: params

        assert_success_response!
        test_full_hash!(response_result, expected_response)
      end

      it 'renders dashboard without embed_links with nil embed_info' do
        params = { id: dashboard.id, include_embed_info: true }
        get :show, params: params

        assert_success_response!
        expected_response_with_embed_info = expected_response.deep_merge(
          dashboard: {
            embed_info: nil,
          },
        )
        test_full_hash!(response_result['dashboard'], expected_response_with_embed_info[:dashboard])
      end

      context 'with embed link' do
        let!(:link) { create(:embed_link, source: dashboard) }

        it 'renders dashboard with embed_info' do
          params = { id: dashboard.id, include_embed_info: true }
          get :show, params: params

          assert_success_response!
          expected_response_with_embed_info = expected_response.deep_merge(
            dashboard: {
              embed_info: {
                hash_code: link.hash_code,
                secret_key: link.secret_key,
              },
            },
          )
          test_full_hash!(response_result['dashboard'], expected_response_with_embed_info[:dashboard])
        end

        it 'returns unauthorized error if user is not allow to list embed info' do
          biz_user = get_test_user
          biz_user.update(allow_authentication_token: true)
          user.share(biz_user, :read, dashboard)
          set_token_header(biz_user)

          params = { id: dashboard.id, include_embed_info: true }
          get :show, params: params

          assert_response_status!(403)
          expect(response_result['type']).to eq 'PermissionDeniedError'
          expect(response_result['message']).to include('You are not allowed to list embed info. Please disable `include_embed_info`.')
        end
      end
    end

    context 'dashboard v4' do |_ex|
      include_context 'dashboards_v4'
      let(:expected_response) do
        {
          dashboard: {
            id: dashboard_v4.id,
            owner_id: user.id,
            title: dashboard_v4.title,
            category_id: dashboard_v4.category_id,
            version: dashboard_v4.version,
            path: dashboard_v4.to_url,
            definition: dashboard_v4.definition,
            definition_aml: dashboard_v4.definition_aml,
            from_aml: false,
            render_options: nil,
            permissions: get_dashboard_permission(dashboard, true),
            widgets: [
              {
                id: '2:w1',
                permissions: {
                  can_explore: true,
                  can_export: true,
                  can_export_data: true,
                },
                source_id: '2:w1',
                source_title: '',
                source_type: 'VizBlock',
              },
            ],
            dynamic_filters: [
              {
                'id' => "#{dashboard_v4.id}:filter_1",
                'order' => -1,
                'drillthrough_enabled' => true,
                'definition' =>
                  { 'id' => nil,
                    'label' => 'filter_1',
                    'default_condition' => { 'operator' => 'is', 'values' => [], 'modifier' => nil, 'options' => nil },
                    'filter_type' => 'string',
                    'constraints' => {},
                    'is_shareable' => false,
                    'input_type' => nil,
                    'filter_source' =>
                      { 'source_type' => 'DmFieldFilterSource',
                        'data_set_id' => 14,
                        'field_path' => { 'model_id' => 2, 'field_name' => 'name', 'data_set_id' => data_set.id }, }, },
                'mappings' => [],
                'uname' => 'filter_1',
                'permissions' => { 'read' => true, 'crud' => true, 'use' => true },
              },
              {
                definition: {
                  constraints: {},
                  default_condition: {
                    modifier: 'year',
                    operator: 'transform_pop_relative',
                    options: {
                      show_growth_rate: false,
                    },
                    values: [
                      1,
                    ],
                  },
                  filter_source: {
                    manual_options: [],
                    source_type: 'ManualFilterSource',
                  },
                  filter_type: 'pop',
                  id: nil,
                  input_type: nil,
                  is_shareable: false,
                  label: 'Pop',
                },
                drillthrough_enabled: false,
                id: "#{dashboard_v4.id}:p1",
                mappings: [],
                order: -1,
                permissions: {
                  crud: true,
                  read: true,
                  use: true,
                },
                uname: 'p1',
              },
              {
                definition: {
                  constraints: {},
                  default_condition: {
                    modifier: nil,
                    operator: 'transform_date_drill',
                    options: nil,
                    values: [
                      'default',
                    ],
                  },
                  filter_source: {
                    manual_options: [],
                    source_type: 'ManualFilterSource',
                  },
                  filter_type: 'date_drill',
                  id: nil,
                  input_type: nil,
                  is_shareable: false,
                  label: 'Date Drill',
                },
                drillthrough_enabled: false,
                id: "#{dashboard_v4.id}:d1",
                mappings: [],
                order: -1,
                permissions: {
                  crud: true,
                  read: true,
                  use: true,
                },
                uname: 'd1',
              },
            ],
            project_id: nil,
            uname: nil,
            repo_version: nil,
            settings: {
              allow_to_change_timezone: nil,
              autoload_disabled: false,
              date_drill_enabled: nil,
              timezone: nil,
            },
          },
        }
      end

      it 'returns dashboard with definition and definition_aml' do
        get :show, params: { id: dashboard_v4.id }
        assert_success_response!
        test_full_hash!(response_result['dashboard'], expected_response[:dashboard])
      end

      context 'with embed link' do
        let!(:link) { create(:embed_link, source: dashboard_v4) }

        it 'renders dashboard with embed_info' do
          params = { id: dashboard_v4.id, include_embed_info: true }
          get :show, params: params

          assert_success_response!
          expected_response_with_embed_info = expected_response.deep_merge(
            dashboard: {
              embed_info: {
                hash_code: link.hash_code,
                secret_key: link.secret_key,
              },
            },
          )
          test_full_hash!(response_result['dashboard'], expected_response_with_embed_info[:dashboard])
        end
      end
    end
  end

  context 'destroy' do
    context 'with embed link' do
      let!(:d) { FactoryBot.create(:dashboard) }
      let!(:link) { FactoryBot.create(:embed_link, source: d) }

      it 'cannot destroy while embed link is in use' do
        set_token_header(user)
        delete :destroy, params: { id: d.id }
        assert_response_status!(422,
                                body: /Cannot delete with existing embed links. Please remove the embed links first/,)
      end

      it 'can destroy if embed link is destroyed' do
        set_token_header(user)
        link.destroy!
        delete :destroy, params: { id: d.id }
        assert_response_status!(200)
      end
    end

    it 'able to destroy' do
      set_token_header(user)

      delete :destroy, params: { id: dashboard.id }
      assert_response_status!(200)
    end

    it 'no permission' do
      analyst = get_test_analyst
      set_token_header(analyst)

      delete :destroy, params: { id: dashboard.id }

      assert_response_status!(403)
    end

    it 'not found dashboard' do
      set_token_header(user)

      delete :destroy, params: { id: 999 }

      assert_response_status!(404)
    end
  end

  context 'index' do
    let!(:dashboard2) do
      FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
      FeatureToggle.toggle_global(Dashboard::FT_V1_CREATION, false)
      create(:dashboard, category_id: 1, version: 3, title: 'Test dashboard 2', owner_id: user.id)
    end
    let!(:dashboard_with_embed_link) do
      FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
      FeatureToggle.toggle_global(Dashboard::FT_V1_CREATION, false)
      create(:dashboard, category_id: 1, version: 3, title: 'Test dashboard 3', owner_id: user.id)
    end

    let!(:embed_link) { create(:embed_link, source: dashboard_with_embed_link) }

    before do
      set_token_header(user)
    end

    it 'no extra query param' do
      get :index, params: {}
      response_json = JSON.parse(response.body)
      response_ids = response_json['dashboards'].map { |db| db['id'].to_i }
      expected_ids = Dashboard.where(tenant_id: user.tenant_id).pluck(:id)
      assert_response_status!(200)
      expect(response_ids).to match_array(expected_ids)
    end

    it 'filter out dashboard not visble with user' do
      biz_user = get_test_user
      biz_user.update(allow_authentication_token: true)
      user.share(biz_user, :read, dashboard)
      set_token_header(biz_user)

      get :index, params: {}
      response_json = JSON.parse(response.body)
      assert_response_status!(200)
      response_ids = response_json['dashboards'].map { |db| db['id'].to_i }
      expect(response_ids).to contain_exactly(1)
    end

    it 'there is limit' do
      get :index, params: { limit: 2 }
      response_json = JSON.parse(response.body)
      response_ids = response_json['dashboards'].map { |db| db['id'].to_i }
      expect(response_ids).to contain_exactly(1, 2)
    end

    it 'order desc and limit' do
      get :index, params: { limit: 2, sort: 'id_desc' }
      response_json = JSON.parse(response.body)
      response_ids = response_json['dashboards'].map { |db| db['id'].to_i }
      expect(response_ids).to contain_exactly(2, 3)
    end

    it 'does not returns info only if include_embed_info is not true' do
      get :index, params: { include_embed_info: false }
      response_json = JSON.parse(response.body)
      is_embed_info_key_exist = response_json['dashboards'].any? { |db| db.key?(:embed_info) }
      expect(is_embed_info_key_exist).to be_falsey
    end

    it 'returns embed link info only with include_embed_info=true' do
      get :index, params: { include_embed_info: true }
      response_json = JSON.parse(response.body)
      response_infos = response_json['dashboards'].map { |db| db['embed_info'] }
      assert_response_status!(200)

      expected_infos = Dashboard.where(tenant_id: user.tenant_id).map do |db|
        db.embed_links&.first&.slice(:hash_code, :secret_key)
      end

      expect(response_infos).to match_array(expected_infos)
    end

    it 'returns unauthorized error if user is not allow to list embed info' do
      biz_user = get_test_user
      biz_user.update(allow_authentication_token: true)
      user.share(biz_user, :read, dashboard)
      set_token_header(biz_user)

      get :index, params: { include_embed_info: true }
      response_json = JSON.parse(response.body)

      assert_response_status!(403)
      expect(response_json['type']).to eq 'PermissionDeniedError'
      expect(response_json['message']).to include('You are not allowed to list embed info. Please disable `include_embed_info`.')
    end

    context 'canvas dashboard' do
      include_context 'canvas_dashboard'

      let(:canvas_dashboard_additional_blocks) do
        200.times.map do |i|
          {
            'type' => 'VizBlock',
            'label' => '',
            'uname' => "v#{100 + i}",
            'description' => nil,
            'settings' => {
              'hide_controls' => false,
              'hide_labels' => false,
            },
            'viz' => {
              'dataset_id' => data_set.id,
              'viz_setting' => {
                'fields' => {
                  'table_fields' => [
                    { 'path_hash' => { 'model_id' => products_model.id, 'field_name' => 'name' }, 'type' => 'string',
                      'format' => { 'type' => 'string' }, },
                  ],
                },
                'filters' => [],
                'viz_type' => 'data_table',
              },
              'settings' => {},
            },
          }
        end
      end

      it 'is fast', :skip_on_circleci do
        GC.disable
        t = Benchmark.realtime do
          get :index
        end
        GC.enable
        puts "t: #{t}s".yellow
      end
    end

    context 'N+1' do
      def generate_multiple_dashboards(dashboard_num: 3, query_report_per_dashboard_num: 3)
        dashboard_num.times do |i|
          dashboard = create(:dashboard, category_id: 1, version: 3, title: "Test dashboard #{i}", owner_id: user.id)

          query_report_per_dashboard_num.times do |_j|
            qr = create(:query_report, data_set_id: query_model_data_set.id, viz_setting: viz_setting)
            dw = create(:dashboard_widget, source: qr, dashboard: dashboard,
                                           grid: { col: 12, row: 8, sizeX: 12, sizeY: 8 },)
          end
        end
      end

      before do
        Dashboard.destroy_all
        FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
        generate_multiple_dashboards
      end

      it 'number of personal item should equal to number of dashboards' do
        sql_queries = extract_queries('SELECT "personal_items"') do
          get :index, params: {}
        end
        expect(sql_queries.length).to eq Dashboard.where(owner_id: user.id).count
      end

      it 'number of tenant domain query should be 1' do
        sql_queries = extract_queries('SELECT "tenant_domains"') do
          get :index, params: {}
        end
        expect(sql_queries.length).to eq 1
      end
    end
  end

  context 'build_url' do
    before do
      set_token_header(user)
      request.headers['Content-Type'] = 'application/json'

      # test that 2 DynamicFilterStates with the same SingleFilterState can co-exist in the database
      DynamicFilterState.new(tenant_id: user.tenant_id, data: single_filter_states).save!
    end

    it 'works with empty filter values' do
      params = { id: dashboard.id, filter_states: [] }
      post :build_url, params: params
      assert_response_status!(200)

      fstate_hash = JSON.parse(response.body)['fstate_hash']
      expect(fstate_hash).to be_nil
    end

    it 'works with adhoc filter values' do
      params = { id: dashboard.id, filter_states: dashboard_filter_conditions }
      post :build_url, params: params
      assert_response_status!(200)
      url = JSON.parse(response.body)['dashboard_url']
      fstate_hash = JSON.parse(response.body)['fstate_hash']
      expect(url.include?(fstate_hash))

      expect(DynamicFilterState.count).to eq(2)
      dynamic_filter_state = DynamicFilterState.last
      expect(dynamic_filter_state.hashid).to eq(fstate_hash)

      filter_states = dynamic_filter_state.data
      filter_states.each_with_index do |state, index|
        expect(state.selected_condition.operator).to eq(dashboard_filter_conditions[index][:condition][:operator])
        expect(state.selected_condition.values).to eq(dashboard_filter_conditions[index][:condition][:values])
      end
    end

    it 'invalid dashboard id' do
      params = { id: 999, filter_states: dashboard_filter_conditions }
      post :build_url, params: params
      assert_response_status!(404)
    end

    it 'filter does not belong to dashboard' do
      dashboard_with_cache = FactoryBot.create(:dashboard_with_cache)
      params = { id: dashboard_with_cache.id, filter_states: dashboard_filter_conditions }
      post :build_url, params: params
      assert_response_status!(422, body: /filter #{dynamic_filter2.id} does not belong/)
    end

    it 'no permission' do
      analyst = get_test_analyst
      set_token_header(analyst)

      params = { id: dashboard.id, filter_states: dashboard_filter_conditions }
      post :build_url, params: params
      assert_response_status!(403)
    end
  end

  context 'grid_preview' do
    before do
      set_token_header(user)
    end

    def test_widget_grid(widget, expected_widget)
      widget = widget.rsk
      expect(widget[:grid]).to eq(expected_widget.grid)
      expect(widget[:viz_preview_type]).to eq(expected_widget.source.viz_setting.viz_type)
    end

    it 'able to get grid preview' do
      get :grid_preview, params: { id: dashboard.id }
      assert_response_status!(200)
      widgets = JSON.parse(response.body)['dashboard_grid_preview']['widgets']
      expect(widgets.count).to be(dashboard.dashboard_widgets.count)
      test_widget_grid(widgets.first, report_widget)
      test_widget_grid(widgets.last, report_widget2)
    end
  end

  context 'dashboard_stats' do
    before do
      tenant.set_setting(:dashboard_consumption_enabled, true)
    end

    let(:sl) do
      sl = FactoryBot.create(:shareable_link, resource: dashboard, tenant_id: dashboard.tenant_id)
      sl.set_public_user
      sl.share_resource
      sl
    end
    let(:expected_response) do
      {
        dashboard_stats: {
          id: 1,
          created_at: dashboard.created_at.to_time.utc,
          description: '',
          created_user: {
            id: user.id,
            email: user.email,
            name: user.name,
            initials: user.initials,
            role: user.role,
            allow_authentication_token: true,
            enable_export_data: true,
            current_sign_in_at: nil,
            last_sign_in_at: nil,
            created_at: user.created_at.to_time.utc,
            is_deleted: false,
            is_activated: true,
            has_authentication_token: true,
            title: nil,
            job_title: nil,
            is_two_factor_auth_enabled: false,
          },
          last_edited_user: nil,
          last_edited_at: nil,
          favourites_count: 0,
          views_count: 0,
          frequent_viewers: [],
        },
      }
    end

    def response_result
      JSON.parse(response.body)
    end

    it 'fetch dashboard stats' do
      set_token_header(user)
      sign_in user

      get :dashboard_stats, params: { id: dashboard.id }
      assert_response_status!(200)
      test_full_hash!(response_result, expected_response)
    end

    it 'admin disabled dashboard stats' do
      tenant.set_setting(:dashboard_consumption_enabled, false)
      set_token_header(user)
      sign_in user

      get :dashboard_stats, params: { id: dashboard.id }
      assert_response_status!(403)
      expect(response_result).to match(
        'type' => 'PermissionDeniedError',
        'message' => 'You do not have permission to perform this action (Read Stats) on this Dashboard',
      )
    end

    it 'not found dashboard' do
      set_token_header(user)
      sign_in user

      get :dashboard_stats, params: { id: 90_000 }
      assert_response_status!(404)
    end

    it 'is public user' do
      set_token_header(sl.public_user)
      sign_in sl.public_user
      get :dashboard_stats, params: { id: dashboard.id }
      assert_response_status!(403)
      expect(response.body).to match('{\"type\":\"PermissionDeniedError\",\"message\":\"User do not have permission to authenticate via token\"}')
    end
  end

  describe '#submit_preload' do
    before do
      set_token_header(user)
      request.headers['Content-Type'] = 'application/json'
      dynamic_filter2.definition.update!(label: 'Other filter')
    end

    let!(:mapping2) do
      create(:dynamic_filter_mapping, dynamic_filter: dynamic_filter2, viz_conditionable: report_widget2,
                                      field_path: name_field_path,)
    end

    it 'able to request post submit preload' do
      post :submit_preload, params: { id: dashboard.id }
      job = assert_success_async_response!(['job', 'id'])
      assert_success_job!(job)
    end

    it 'able to request post submit preload with bust_cache params' do
      post :submit_preload, params: { id: dashboard.id, bust_cache: true, aaa: 'asdf' }
      job = assert_success_async_response!(['job', 'id'])
      assert_success_job!(job)
    end

    it 'no permission' do
      analyst = get_test_analyst
      set_token_header(analyst)

      post :submit_preload, params: { id: dashboard.id }
      assert_response_status!(403)
    end

    it 'not found dashboard' do
      post :submit_preload, params: { id: 9999 }
      assert_response_status!(404)
    end

    it 'applies default filters correctly' do
      params = { id: dashboard.id }
      post :submit_preload, params: params
      job = assert_success_async_response!(['job', 'id'])
      assert_job_log!(job, /"name" = 'alice'\s*\)/)
      assert_job_log!(job, /^Applying filters:\n- Text Filter: is "alice"\n- Other filter: is "alice"$/)
    end

    it 'applies submitted filters correctly' do
      dashboard_filter_conditions =
        [
          {
            dynamic_filter_id: dynamic_filter2.id,
            condition: { operator: 'is', values: ['ahihi'] },
          },
        ]
      params = { id: dashboard.id, dashboard_filter_conditions: dashboard_filter_conditions }
      post :submit_preload, params: params
      job = assert_success_async_response!(['job', 'id'])
      assert_job_log!(job, /"name" = 'ahihi'\s*\)/)
      assert_job_log!(job, /^Applying filters:\n- Text Filter: is "alice"\n- Other filter: is "ahihi"$/)
    end

    it 'supports skipping filters using None operator' do
      dashboard_filter_conditions =

        [
          {
            dynamic_filter_id: dynamic_filter2.id,
            condition: { operator: 'none', values: [] },
          },
        ]
      params = { id: dashboard.id, dashboard_filter_conditions: dashboard_filter_conditions }

      post :submit_preload, params: params
      job = assert_success_async_response!(['job', 'id'])
      assert_job_log!(job, /"name" = 'alice'\s*\)/)
      assert_job_log!(job, /^Applying filters:\n- Text Filter: is "alice"\n- Other filter: none$/)
    end

    describe 'belongs to correct job queue' do
      it 'belongs to prefetch queue' do
        params = { id: dashboard.id }
        post :submit_preload, params: params
        job = assert_success_async_response!(['job', 'id'])
        expect(job.tag).to eq('prefetch')
      end
    end
  end

  describe '#show' do
    context 'dashboard consumption' do
      let(:params) do
        { id: dashboard.id, view_dashboard: true }
      end

      shared_examples 'should not track user view dashboard' do
        it 'run correctly' do
          old_views_count = dashboard.views_count
          get :show, params: params
          dashboard.reload
          expect(dashboard.views_count).to eq(old_views_count)
        end
      end

      context 'session auth', :skip_schema_conform do
        let(:admin) { get_test_admin }

        before do
          FeatureToggle.toggle_global(Dashboard::FT_DASHBOARD_CONSUMPTION, true)
          request.headers[ControllerConstants::Headers::USE_SESSION_AUTH] = '1'
          sign_in(admin)
        end

        it 'tracks user view dashboard' do
          old_views_count = dashboard.views_count
          get :show, params: params
          dashboard.reload
          expect(dashboard.views_count).to eq(old_views_count + 1)
        end

        context 'when not exist params view_dashboard' do
          let(:params) { { id: dashboard.id } }

          it_behaves_like 'should not track user view dashboard'
        end

        context 'when public user' do
          let(:embed_link) do
            embed_link = FactoryBot.create(:embed_link, source: dashboard, version: 4)
            embed_link.set_public_user
            embed_link.share_source
            embed_link.update_filters
            embed_link
          end
          let(:embed_user) do
            embed_link.public_user
          end

          let(:enable_export_data) { true }
          let(:embed_payload) do
            {}
          end

          let(:embed_token) do
            sk = embed_link.secret_key
            jwt_encode(sk, embed_payload, Time.now.to_i + 1000)
          end

          before do
            request.headers[PublicLinks::AuthenticationHelper::EMBED_ID_HEADER] = embed_link.hash_code
            request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token
          end

          it_behaves_like 'should not track user view dashboard'
        end
      end

      context 'api auth', :allow_forgery_protection do
        before do
          FeatureToggle.toggle_global(Dashboard::FT_DASHBOARD_CONSUMPTION, true)
          set_token_header(user)
        end

        it_behaves_like 'should not track user view dashboard'
      end
    end

    context 'with include_pin_status param' do
      before do
        FeatureToggle.toggle_global(Dashboard::FT_PIN_DASHBOARD, true)
        set_token_header(user)
      end

      it 'includes pin status true in response' do
        create(:pinned_item, source_id: dashboard.id)

        get :show, params: { id: dashboard.id, include_pin_status: true }
        assert_success_response!
        parsed = JSON.parse(response.body)
        expect(parsed['dashboard']['is_pinned']).to be(true)
      end

      it 'includes pin status false in response' do
        get :show, params: { id: dashboard.id, include_pin_status: true }
        assert_success_response!
        parsed = JSON.parse(response.body)
        expect(parsed['dashboard']['is_pinned']).to be(false)
      end
    end
  end

  describe '#ai_generate' do
    let(:user) do
      user = get_test_admin
      user.allow_authentication_token = true
      user.save!
      user
    end
    let(:headers) do
      { 'content_type' => 'application/json',
        HolisticsSetting::API_KEY_HEADER.to_s => user.generate_authentication_token, }
    end
    let(:params) do
      {
        'payload' => { 'dataset' => { 'id' => 1, 'name' => 'test' }, 'dimensions' => ['name', 'age'],
                       'measures' => ['count(id)'], },
      }
    end

    context 'enable ft v4_ai_codegen', type: :request do
      before do
        FeatureToggle.toggle_global(Dashboard::FT_V4_AI_CODEGEN, true)
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
        ENV['OPENAI_ACCESS_TOKEN'] = 'mocked'
        ENV['OPENAI_CANVAS_DASHBOARD_GENERATOR_ASSISTANT_ID'] = 'mocked'
        set_token_header(user)
      end

      context 'can generate dashboard' do
        it 'run correctly' do
          VCR.use_cassette('dashboard/ai_generate', match_requests_on: [:uri, :body, :method]) do
            post '/api/v2/dashboards/ai_generate', params: params, as: :json, headers: headers
            assert_success_response!

            job = assert_success_async_response!(['job', 'id'])
            result = job.fetch_cache_data
            # some test on the structure of the result
            expect(result[:title].class).to be(String)
            expect(result[:blocks].class).to be(Array)
          end
        end
      end

      context 'user do not have permission create dashboard' do
        let(:user) do
          user = get_test_user
          user.allow_authentication_token = true
          user.save!
          user
        end

        it 'raise error' do
          post '/api/v2/dashboards/ai_generate', params: params, as: :json, headers: headers
          assert_response_status!(403)
          expect(response.body).to match('{"type":"PermissionDeniedError","message":"You do not have permission to perform this action on this Dashboard"}')
        end
      end

      context 'user do not have permission on dataset' do
        let(:user) do
          user = get_test_analyst
          user.allow_authentication_token = true
          user.save!
          user
        end

        it 'raise error' do
          post '/api/v2/dashboards/ai_generate', params: params, as: :json, headers: headers
          assert_response_status!(403)
          expect(response.body).to match('{"type":"PermissionDeniedError","message":"You do not have permission to perform this action (Read) on this Dataset"}')
        end
      end

      it 'invalid params', :skip_schema_conform do
        post '/api/v2/dashboards/ai_generate', params: {}, as: :json, headers: headers
        assert_response_status!(400)
      end
    end

    context 'disable ft v4_ai_codegen', type: :request do
      before do
        FeatureToggle.toggle_global(Dashboard::FT_V4_AI_CODEGEN, false)
        set_token_header(user)
      end

      it 'invalid when disabled ft' do
        post '/api/v2/dashboards/ai_generate', params: params, as: :json, headers: headers
        assert_response_status!(422)
      end
    end
  end

  describe '#update_canvas_dashboard' do
    include_context 'edit_production_dashboard'

    let(:params) do
      {
        id: empty_dashboard.id,
        dashboard_update_events: dashboard_update_events,
        repo_version: proj_repo.version,
        datasets: datasets_event,
      }
    end

    before do
      set_token_header(user)
      request.headers['Content-Type'] = 'application/json'
    end

    it 'raise permission denied for implicit git' do
      FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXPLICIT_GIT, tenant.id, false)

      post :submit_update_canvas_dashboard, params: params
      assert_response_status!(403)
    end

    context 'when feature toggle is turned on' do
      before do
        FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EXPLICIT_GIT, tenant.id, true)
        FeatureToggle.toggle_tenant(AmlStudio::Project::FT_EDIT_IN_PRODUCTION, tenant.id, true)
      end

      it 'raise error if edit in production setting is not enabled' do
        project.update(settings: project.settings.merge(edit_in_production_enabled: false))

        post :submit_update_canvas_dashboard, params: params
        assert_response_status!(403)
      end

      it 'successfully update dashboard' do
        project.update(settings: project.settings.merge(edit_in_production_enabled: true))
        post :submit_update_canvas_dashboard, params: params
        assert_success_async_response!(['job', 'id'])
      end
    end
  end

  describe '#create_canvas_dashboard' do
    include_context 'edit_production_dashboard'

    let(:params) do
      {
        project_id: project.id,
        definition: live_dashboard.definition,
        category_id: 0,
        datasets: datasets_event,
      }
    end

    before do
      set_token_header(user)
      request.headers['Content-Type'] = 'application/json'
    end

    it 'successfully create dashboard' do
      project.update(settings: project.settings.merge(edit_in_production_enabled: true))
      post :submit_create_canvas_dashboard, params: params
      assert_success_async_response!(['job', 'id'])

      expect(Dashboard.where(project_id: project.id, uname: 'live_dashboard_1').exists?).to be(true)
    end

    it 'raises permission deinied if project is not enable in production' do
      project.update(settings: project.settings.merge(edit_in_production_enabled: false))

      post :submit_create_canvas_dashboard, params: params
      assert_response_status!(403)
    end

    context 'explorer' do
      before do
        FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
        set_token_header(explorer)
        request.headers['Content-Type'] = 'application/json'
      end

      it 'raise permission denied if user dont have permission on category' do
        post :submit_create_canvas_dashboard, params: params
        assert_response_status!(403)
      end
    end
  end

  describe '#list_metadata' do
    let(:dashboard_v4) do
      d = create(:dashboard, version: 3, uname: 'v4', project_id: 1, tenant_id: user.tenant_id, title: 'dashboard v4')
      d.update!(version: 4)
      d
    end
    let(:dashboard_v3) do
      create(:dashboard, version: 3, tenant_id: user.tenant_id, title: 'dashboard v3')
    end

    before do
      request.headers['Content-Type'] = 'application/json'

      dashboard
      dashboard_v3
      dashboard_v4
    end

    context 'admin' do
      before do
        set_token_header(user)
      end

      it 'can list all dashboards' do
        get :list_metadata

        assert_success_response!
        res = JSON.parse(response.body)
        expect(res['dashboards_metadata']).to contain_exactly(
          dashboard.slice(:id, :title, :uname, :version, :project_id),
          dashboard_v3.slice(:id, :title, :uname, :version, :project_id),
          dashboard_v4.slice([:id, :title, :uname, :version, :project_id]),
        )
      end

      it 'can filter dashboard v4 only' do
        get :list_metadata, params: { version: 4 }

        assert_success_response!
        res = JSON.parse(response.body)
        expect(res['dashboards_metadata']).to contain_exactly(
          dashboard_v4.slice([:id, :title, :uname, :version, :project_id]),
        )
      end
    end

    context 'explorer' do
      before do
        FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
        FeatureToggle.toggle_global(Permission::FT_COMPOUND_ACTIONS, true)
        FeatureToggle.toggle_global(Permission::FT_EXPLORER_EDIT_DASHBOARD, true)
        set_token_header(explorer)

        user.share(explorer, :crud, dashboard_v4)
        user.share(explorer, :read, dashboard_v3)
      end

      it 'only show shared dashboard' do
        get :list_metadata

        assert_success_response!
        res = JSON.parse(response.body)
        expect(res['dashboards_metadata']).to contain_exactly(
          dashboard_v3.slice(:id, :title, :uname, :version, :project_id),
          dashboard_v4.slice([:id, :title, :uname, :version, :project_id]),
        )
      end

      it 'can filter by permission' do
        get :list_metadata, params: { permission_action: :update }

        assert_success_response!
        res = JSON.parse(response.body)
        expect(res['dashboards_metadata']).to contain_exactly(
          dashboard_v4.slice([:id, :title, :uname, :version, :project_id]),
        )
      end
    end
  end

  describe '#create_dashboard_for_external_user' do
    include_context 'embed_portal_with_ssbi'
    let(:params) do
      {
        definition: {
          uname: 'new_dashboard',
          title: 'empty dashboard',
          blocks: [],
          interactions: [],
          views: [],
        },
        datasets: [],
        is_personal: true,
      }
    end

    it 'works' do
      external_user
      login_as_embed_user

      post :create_dashboard_for_external_user, params: params, as: :json
      assert_success_response!

      dashboard = Dashboard.find_by(title: params[:definition][:title])
      expect(dashboard.external_user_item.is_personal).to be(true)

      folder_path = AmlStudio::Project.storage_path_for_ugo_dashboard(external_user, true)
      dashboard_uname = dashboard.uname.split('.')[-1]
      expect(dashboard.uname.start_with?('embedding.')).to be(true)

      expect(proj_work_flow.read_file("#{folder_path}/#{dashboard_uname}.page.aml",
                                      branch_name: project.production_branch,).present?).to eq(true)
    end

    it 'raise error for internal user' do
      set_token_header(get_test_admin)

      post :create_dashboard_for_external_user, params: params, as: :json
      assert_response_status!(422)

      expect(response.body).to match(/Unsupported embed link. Please contact Holistics Support/)
    end

    context 'permission denied' do
      let(:enable_personal_workspace) { false }

      it 'raise permission denined' do
        external_user
        login_as_embed_user

        post :create_dashboard_for_external_user, params: params, as: :json
        assert_response_status!(403)
      end
    end
  end

  describe '#submit_delete_canvas_dashboard' do
    include_context 'embed_portal_with_ssbi'

    it 'works' do
      external_user
      login_as_embed_user

      dashboard_id = org_dashboard.id

      post :submit_delete_canvas_dashboards, params: { ids: [dashboard_id] }, as: :json
      assert_success_response!

      job_id = JSON.parse(response.body)['job']['id']
      assert_success_job!(Job.find(job_id))

      expect(Dashboard.where(id: dashboard_id).exists?).to be(false)
    end

    it 'raise permission denied' do
      set_token_header(get_test_analyst)

      post :submit_delete_canvas_dashboards, params: { ids: [org_dashboard.id] }, as: :json
      assert_response_status!(403)
    end
  end
end
