shared_context 'aml_studio_dashboard' do
  include_context 'aml_studio_dataset' do
    let(:isolated_repo) { true }

    let(:project_fixture_folder_path) do
      'spec/services/engines/aml_studio/git_flows/deploy/fixtures/aml/'
    end
  end

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
  end

  let(:dashboard_extension) { AmlStudio::Values::DASHBOARD_EXTENSION }
  let(:user) { get_test_admin }
  let(:work_flow) do
    project.working_repo!(user).work_flow(user)
  end
end

# need to define :dashboard_url, :dataset_tree_selector, :dataset_id, dataset_select_value, :dual_mode_selector
shared_examples 'edit_dashboard_as_code' do
  include_context 'aml_studio_dashboard'
  let(:editor_selector) { '[data-ci="aml-monaco-editor"]' }

  def login_and_select_dual_mode
    user.nux[:seen_features] = ['dac-controls-panel']
    user.save!

    safe_login user, dashboard_url
    wait_and_click(dual_mode_selector)
    btn_close_panel = page.find_all('.ci-close-bottom-panel-button')
    # Close panel if exist
    if btn_close_panel.length > 0
      btn_close_panel.each(&:click)
    end
    wait_for_element_load('.dac-body')
  end

  def select_viz_field(model_name, field_name)
    select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', value: "#{model_name}$!#{field_name}")
  end

  def test_update_dashboard_aml_content(rspec_example, snapshot_name, &update_action_blk)
    old_content = page.find(editor_selector).text
    update_action_blk.call

    new_content = ''
    wait_for do
      new_content = page.find(editor_selector).text
      # Wait for the content to be completely updated
      sleep 0.5
      old_content != new_content
    end

    SnapshotTest.test!(new_content, rspec_example: rspec_example, snapshot_name: snapshot_name)
  end

  def hotel_tester
    HOtel::IntegrationTester.new(database: hotel_frontend_db)
  end

  def test_hotel_span(span_name, span_count, span_attributes)
    hotel_tester.expect_span({ name: span_name }, count: span_count, attributes: span_attributes)
  end

  def find_spans_by_name(span_name)
    hotel_tester.extract_frontend_db(name: span_name)
  end

  it 'able to add and edit text widget' do |ex|
    login_and_select_dual_mode

    test_update_dashboard_aml_content(ex, 'add_text_block.snap') do
      wait_for_element_load('[data-ci="add-text-block"]')
      wait_and_click('[data-ci="add-text-block"] ')

      widget_text = 'Add random test. Ahihi'
      wait_and_set_ace_editor_text('text-widget-editor', widget_text)
      wait_and_click('.ci-modal-submit:not(.disabled)')
      wait_expect(widget_text) { page.find('[data-ci="text-block"]').text }
    end

    test_update_dashboard_aml_content(ex, 'edit_text_block.snap') do
      wait_for_element_load('#block-t1')

      safe_click('#block-t1')
      safe_click('[data-ci="edit-block-btn"]')

      widget_text = 'Edit some text'

      wait_for_element_load('textarea[data-ci="text-block-editor"]')
      page.find('textarea[data-ci="text-block-editor"]').set(widget_text).send_keys(:tab)

      wait_expect(widget_text) { page.find('[data-ci="text-block"]').text }
    end
  end

  it 'able to add edit viz', :otel do |ex|
    login_and_select_dual_mode

    test_update_dashboard_aml_content(ex, 'add_viz_block.snap') do
      wait_for_element_load('[data-ci="add-viz-block"]')
      wait_and_click('[data-ci="add-viz-block"]')

      wait_for_element_load(dataset_tree_selector)
      select_h_select_option(dataset_tree_selector, value: dataset_select_value)
      select_viz_field('users', 'id')
      safe_click('.ci-explorer-control-get-results')

      wait_for_loading_finish
      safe_click('.ci-save-ds-based-report')

      # set title
      wait_for_element_load('[data-hui-section="resolve-button"]')
      page.find('.h-input').set('Report title')
      safe_click('[data-hui-section="resolve-button"]')

      # viz error because the table/field name does not exist on test data_source
      wait_expect(true) { page.find('#block-v1 [data-ci="viz-block"]').text.include?('ERROR') }

      # span VizBlock only has 1 event, because it only start when mounted component
      test_hotel_span(
        'VizBlock', 1,
        { 'h.dashboard_uname': 'empty_dashboard', 'h.dashboard_block_uname': 'v1' },
      )

      test_hotel_span(
        'DashboardUiEditor#updateDashboard', 1,
        {
          'h.dashboard_uname': 'empty_dashboard',
          'h.events_name': '["UpdateBlock","UpdateInteractions","UpdateCanvasBlockPosition"]',
          'h.members_uname': '["v1",null,null]',
        },
      )
    end

    test_update_dashboard_aml_content(ex, 'edit_viz_block.snap') do
      wait_for_element_load('#block-v1')
      page.find_by_id('block-v1').hover

      safe_click('[data-ci="block-v1-controls"] [data-ci="edit-block-btn"]')
      select_viz_field('users', 'full_name')

      safe_click('.ci-explorer-control-get-results')
      wait_for_loading_finish

      safe_click('.ci-save-ds-based-report')
      wait_expect(true) { page.find('#block-v1 [data-ci="viz-block"]').text.include?('ERROR') }

      # span VizBlock only has 1 event, because it only start when mounted component
      test_hotel_span(
        'VizBlock', 1,
        { 'h.dashboard_uname': 'empty_dashboard', 'h.dashboard_block_uname': 'v1' },
      )

      wait_expect(2, 30) do
        spans = find_spans_by_name('DashboardUiEditor#updateDashboard')
        spans.size
      end

      # Span of create new VizBlock event above
      hotel_tester.include_attributes?(
        { name: 'DashboardUiEditor#updateDashboard' },
        {
          'h.dashboard_uname': 'empty_dashboard',
          'h.events_name': '["UpdateBlock","UpdateInteractions","UpdateCanvasBlockPosition"]',
          'h.members_uname': '["v1",null,"view_1"]',
        },
      )

      # Span of edit VizBlock event
      hotel_tester.include_attributes?(
        { name: 'DashboardUiEditor#updateDashboard' },
        {
          'h.dashboard_uname': 'empty_dashboard',
          'h.events_name': '["UpdateBlock"]',
          'h.members_uname': '["v1"]',
        },
      )
    end
  end

  it 'able to add, edit ic' do |ex|
    login_and_select_dual_mode

    test_update_dashboard_aml_content(ex, 'add_filter_block.snap') do
      wait_for_element_load('[data-ci="add-ic-block"]')
      wait_and_click('[data-ci="add-ic-block"]')

      filter_label = 'Test filter'
      fill_text('.ci-filter-label-input', filter_label)

      safe_click('[data-ci="ci-data-set-model-field-select"]')
      select_h_select_option('.ci-data-set-options', value: dataset_id)
      select_h_select_option('.ci-data-model-field-options', value: 'users$!id')

      safe_click('.ci-data-set-model-field-select-button')
      page.find('.ci-submit').click

      wait_expect(true) { page.find('#block-f1 [data-ci="ic-block"]').text.include?(filter_label) }
    end

    test_update_dashboard_aml_content(ex, 'edit_filter_block.snap') do
      filter_label = 'Edited filter'

      wait_for_element_load('#block-f1')
      page.find_by_id('block-f1').hover

      safe_click('[data-ci="block-f1-controls"] [data-ci="edit-block-btn"]')
      fill_text('.ci-filter-label-input', filter_label)
      select_h_select_option('.ci-filter-type-select', value: 'string')

      safe_click('.ci-submit')
      wait_expect(true) { page.find('#block-f1 [data-ci="ic-block"]').text.include?(filter_label) }
    end
  end
end
