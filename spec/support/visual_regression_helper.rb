# typed: false
# frozen_string_literal: true

# Remove ChunkyPNG dependency
require 'fileutils'
require 'rmagick'

module VisualRegressionHelper
  REFERENCE_SCREENSHOT_PATH = Rails.root.join('spec/screenshots/reference')
  TEST_SCREENSHOT_PATH = Rails.root.join('tmp/capybara/screenshots/test')
  DIFF_SCREENSHOT_PATH = Rails.root.join('tmp/capybara/screenshots/diff')

  # Environment variables to control behavior
  GENERATE_DIFFS = ENV.fetch('VISUAL_REGRESSION_GENERATE_DIFFS', '0').match?(/^(true|1|t|yes|y)$/i)
  UPDATE_REFERENCES = ENV.fetch('VISUAL_REGRESSION_UPDATE_REFERENCES', '0').match?(/^(true|1|t|yes|y)$/i)

  # Ensure directories exist when the module is loaded
  FileUtils.mkdir_p(REFERENCE_SCREENSHOT_PATH)
  FileUtils.mkdir_p(TEST_SCREENSHOT_PATH)
  FileUtils.mkdir_p(DIFF_SCREENSHOT_PATH)

  # Compares the captured screenshot of a Capybara element/page against a reference image.
  #
  # @param name [String, nil] A unique, descriptive name for the screenshot (used for filename).
  # @param selector [String] CSS selector for the element to capture. Defaults to 'body'.
  # @param threshold [Float] Allowed difference percentage (0.0 to 100.0). Defaults to 0.01.
  # @param wait [Float] Seconds to wait before taking the screenshot (allows for rendering/animations). Defaults to 0.5.
  # @raise [RuntimeError] if visual mismatch exceeds the threshold or dimensions differ.
  def assert_visual_match(name = nil, selector: 'body', threshold: 0.01, wait: 0.5)
    # Auto-generate name from RSpec example if not provided
    name = generate_screenshot_name_from_example if name.nil?

    # Allow time for rendering/animations after actions
    sleep(wait) if wait > 0

    identifier = name.parameterize.underscore
    ref_path = REFERENCE_SCREENSHOT_PATH.join("#{identifier}.png")
    test_path = TEST_SCREENSHOT_PATH.join("#{identifier}.png")
    diff_path = DIFF_SCREENSHOT_PATH.join("#{identifier}.png")

    capture_screenshot(name, selector, test_path, ref_path)

    if UPDATE_REFERENCES
      FileUtils.cp(test_path, ref_path)
      puts "✨ New visual reference created: #{ref_path}. Verify and commit."
      pending("✨ New visual reference created: #{ref_path}. Verify and commit.")
    end

    unless File.exist?(ref_path)
      FileUtils.rm_f(test_path)
      raise("❌ Reference image not found: #{ref_path}\n  " \
            "- Run with VISUAL_REGRESSION_UPDATE_REFERENCES=1 to create it\n    " \
            '- Then verify the generated reference image before committing')
    end

    diff_percentage = compare_images(name, ref_path, test_path, diff_path)

    # --- Result Handling ---
    if diff_percentage <= threshold
      # Match
      FileUtils.rm_f(test_path)
      FileUtils.rm_f(diff_path)
    else
      # Mismatch: Generate diff image if enabled
      error_message = "Visual Difference Exceeded Threshold ('#{name}'): Images differ by #{diff_percentage.round(2)}% (threshold is #{threshold}%).\n  " \
                      "- Reference: #{ref_path}\n  " \
                      "- Test:      #{test_path}"

      # Add diff image info only if generated
      error_message += if GENERATE_DIFFS
                         "\n  - Diff:      #{diff_path}\n    " \
                           '- Please review the diff image to see the changes.'
                       else
                         # Provide guidance on how to see diffs
                         "\n  - To generate a diff image: VISUAL_REGRESSION_GENERATE_DIFFS=1\n    " \
                           '- To update reference: VISUAL_REGRESSION_UPDATE_REFERENCES=1'
                       end

      expect(diff_percentage).to be <= threshold, -> { error_message }
    end
  end

  # Generates a screenshot name from the current RSpec example
  # @return [String] A name derived from the current example's full description
  def generate_screenshot_name_from_example
    example = RSpec.current_example

    if example.nil?
      raise 'Cannot auto-generate screenshot name outside of an RSpec example. ' \
            'Please provide an explicit name to assert_visual_match.'
    end

    description = example.metadata[:description]
    example_group = example.metadata[:example_group]

    # Build the full path from nested example groups
    parts = []
    while example_group
      parts.unshift example_group[:description] unless example_group[:description].empty?
      example_group = example_group[:parent_example_group]
    end

    parts << description
    parts.join(' - ')
  end

  # Captures a screenshot of the specified element
  #
  # @param name [String] The name of the screenshot (for error messages)
  # @param selector [String] CSS selector for the element to capture
  # @param test_path [String] Path where the screenshot will be saved
  # @param ref_path [String] Reference path (for error messages)
  # @return [void]
  # @raise [RuntimeError] if capture fails
  def capture_screenshot(name, selector, test_path, ref_path)
    # Capture the screenshot of the specified element or body
    begin
      element = find(selector) # Ensure element is present first
    rescue Capybara::ElementNotFound => e
      raise "Visual Test Error ('#{name}'): Element not found using selector '#{selector}'.\n  - Check if the selector is correct or if the element exists on the page.\n  - Capybara Error: #{e.message}\n"
    end

    # Ensure element is displayed and get its bounds
    unless element.visible?
      raise "Visual Test Error ('#{name}'): Element found with selector '#{selector}' but it is not visible.\n  - Ensure the element is displayed on the page and not hidden by CSS or other elements.\n"
    end

    begin
      # Scroll the element into view
      page.execute_script(<<~JS, element.native)
        function getScrollParent(node) {
          if (!node || node === document.body) return null;
          const overflowY = window.getComputedStyle(node).overflowY;
          const isScrollable = overflowY !== 'visible' && overflowY !== 'hidden';
          return isScrollable && node.scrollHeight > node.clientHeight ? node : getScrollParent(node.parentNode);
        }

        const el = arguments[0];
        const scrollParent = getScrollParent(el);

        if (scrollParent) {
          scrollParent.scrollTop = scrollParent.scrollTop + el.getBoundingClientRect().top - 300;
        } else {
          el.scrollIntoView({ behavior: 'instant', block: 'center' });
        }
      JS

      sleep(0.5) # Wait for scroll to complete

      # Always use full page screenshot and crop strategy
      capture_success = false
      begin
        # Get positioning using JavaScript's getBoundingClientRect and page scroll
        position_data = page.evaluate_script(<<~JS, element.native)
          (function(element) {
            var rect = element.getBoundingClientRect();
            var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          #{'  '}
            // Get absolute position by adding scroll offset to viewport position
            return {
              x: rect.left + scrollLeft,
              y: rect.top + scrollTop,
              width: rect.width,
              height: rect.height,
              pixelRatio: window.devicePixelRatio || 1
            };
          })(arguments[0]);
        JS

        # Use the JavaScript results if available, otherwise fall back to Selenium coords
        if position_data
          rect = {
            'x' => position_data['x'],
            'y' => position_data['y'],
            'width' => position_data['width'],
            'height' => position_data['height'],
            'pixelRatio' => position_data['pixelRatio'],
          }
        end

        # Take screenshot and process with RMagick
        capture_success = capture_and_crop_element(test_path, rect, ref_path, name)
      rescue Magick::ImageMagickError => e
        handle_screenshot_error(name, ref_path, test_path, 'RMagick processing error', e)
      rescue StandardError => e
        handle_screenshot_error(name, ref_path, test_path, 'screenshot processing', e)
      end

      unless capture_success
        raise "Visual Test Error ('#{name}'): Could not capture screenshot.\n  - Both direct element capture (if applicable) and full-page crop methods failed.\n  - Review previous test.log for potential causes (e.g., element issues, driver problems).\n"
      end
    rescue StandardError => e
      raise "Visual Test Error ('#{name}'): An unexpected error occurred during screenshot capture or processing.\n  - Please review the error details and backtrace.\n"
    end
  end

  # Helper method to capture a screenshot and crop to an element
  # @param test_path [String] Path where the screenshot will be saved
  # @param element_rect [Hash] Coordinates and dimensions of the element
  # @param ref_path [String] Reference path (for error messages)
  # @param name [String] Name of the screenshot for error messages
  # @return [Boolean] Whether the capture was successful
  def capture_and_crop_element(test_path, element_rect, ref_path, name)
    page.save_screenshot(test_path) # rubocop:disable Lint/Debugger

    begin
      image = Magick::Image.read(test_path).first

      # Calculate crop dimensions with device pixel ratio adjustment
      crop_dimensions = calculate_crop_dimensions(image, element_rect)

      cropped = image.crop(
        crop_dimensions[:x],
        crop_dimensions[:y],
        crop_dimensions[:width],
        crop_dimensions[:height],
      )

      cropped.write(test_path)
      true
    rescue StandardError => e
      log_error('Failed to crop screenshot', e)
      false
    end
  end

  # Calculate crop dimensions with pixel ratio adjustment
  # @param image [Magick::Image] The source image
  # @param rect [Hash] Element coordinates (x, y, width, height, pixelRatio)
  # @return [Hash] Adjusted crop coordinates
  def calculate_crop_dimensions(image, rect)
    pixel_ratio = rect['pixelRatio'] || 1.0
    padding = 5 # Padding around the element in pixels

    # Apply pixel ratio to convert from CSS pixels to actual image pixels
    crop_x = [((rect['x'] * pixel_ratio) - (padding * pixel_ratio)).round, 0].max
    crop_y = [((rect['y'] * pixel_ratio) - (padding * pixel_ratio)).round, 0].max

    # Ensure crop dimensions don't exceed image boundaries
    max_crop_width = image.columns - crop_x # RMagick uses columns/rows instead of width/height
    max_crop_height = image.rows - crop_y
    crop_width = [((rect['width'] * pixel_ratio) + (padding * 2 * pixel_ratio)).round, max_crop_width].min
    crop_height = [((rect['height'] * pixel_ratio) + (padding * 2 * pixel_ratio)).round, max_crop_height].min

    {
      x: crop_x,
      y: crop_y,
      width: crop_width,
      height: crop_height,
    }
  end

  # Centralized error handler for screenshot errors
  # @param name [String] Screenshot name
  # @param ref_path [String] Reference image path
  # @param test_path [String] Test image path
  # @param context [String] Description of what was happening when error occurred
  # @param error [Exception] The caught exception
  def handle_screenshot_error(name, ref_path, test_path, context, error)
    raise "Visual Test Error ('#{name}'): Failed during #{context}.\n  " \
          "- Error details: #{error.message}\n  " \
          "- Reference Image: #{ref_path}\n  " \
          "- Test Image Path: #{test_path}"
  end

  # Log errors consistently
  # @param message [String] Error message
  # @param error [Exception] The caught exception (optional)
  def log_error(message, error = nil)
    puts "[Visual Regression] ERROR: #{message}"
    puts "[Visual Regression] Exception: #{error.message}" if error
    puts "[Visual Regression] Backtrace: #{error.backtrace.first(3).join("\n  ")}" if error
  end

  # Compares two images and returns the difference percentage
  #
  # @param name [String] The name of the screenshot (for error messages)
  # @param ref_path [String] Path to reference image
  # @param test_path [String] Path to test image
  # @param diff_path [String] Path where diff image will be saved if needed
  # @return [Float] Difference percentage between images
  # @raise [RuntimeError] if images can't be read or dimensions don't match
  def compare_images(name, ref_path, test_path, diff_path)
    begin
      images = [
        Magick::Image.read(ref_path).first,
        Magick::Image.read(test_path).first,
      ]
    rescue Magick::ImageMagickError => e
      raise "Visual Test Error ('#{name}'): Could not read image file using RMagick.\n  - Ensure the image files exist and have correct permissions.\n  - Check for potential file corruption.\n  - RMagick Error: #{e.message}\n  - Reference Image: #{ref_path}\n  - Test Image: #{test_path}"
    end

    # Check dimensions (RMagick uses columns/rows instead of width/height)
    ref_width = images[0].columns
    ref_height = images[0].rows
    test_width = images[1].columns
    test_height = images[1].rows
    if ref_width != test_width || ref_height != test_height
      raise "Visual Test Error ('#{name}'): Image dimensions do not match the reference.\n  - Reference (#{ref_width}x#{ref_height}): #{ref_path}\n  - Test (#{test_width}x#{test_height}):      #{test_path}\n  - Please inspect both images to understand the difference."
    end

    diff_image = images[0].compare_channel(images[1], Magick::MeanSquaredErrorMetric)
    # diff_image[0] is the difference image
    # diff_image[1] is the normalized mean error

    # Convert the error metric to a percentage (0-100)
    # RMagick returns a value from 0.0 to 1.0
    diff_percentage = diff_image[1] * 100.0

    # Generate visual diff if there are differences AND diff generation is enabled
    # Using a small threshold to account for floating point precision
    if GENERATE_DIFFS && diff_percentage > 0.001
      generate_diff_visualization(images[0], images[1], diff_path)
    end

    diff_percentage
  end

  # Generates an diff visualization between two images
  # @param reference_img [Magick::Image] The reference (expected) image
  # @param test_img [Magick::Image] The test (actual) image
  # @param output_path [String] Where to save the diff image
  # @return [void]
  def generate_diff_visualization(reference_img, test_img, output_path)
    width = reference_img.columns
    height = reference_img.rows
    legend_height = 60 # Extra space for labels and legend
    total_height = height + legend_height

    # Create a new image with space for the legend
    diff_img = Magick::Image.new(width * 3, total_height)
    diff_img.background_color = 'white'

    # Create a diff pixel image and identify change regions
    diff_pixels, change_regions, max_changes = generate_difference_points(reference_img, test_img)

    # Compose the three images side by side
    diff_img = diff_img.composite(reference_img, 0, 0, Magick::OverCompositeOp)
    diff_img = diff_img.composite(test_img, width, 0, Magick::OverCompositeOp)
    diff_img = diff_img.composite(diff_pixels, width * 2, 0, Magick::OverCompositeOp)

    # Draw visual indicators for change regions
    draw_difference_shapes(diff_img, change_regions, max_changes, width)

    # Add section labels and color legend
    add_legend_and_labels(diff_img, width, height, legend_height)

    # Save the final image
    diff_img.write(output_path)
  end

  # Processes pixel-by-pixel differences between images
  # @param reference_img [Magick::Image] The reference image
  # @param test_img [Magick::Image] The test image
  # @return [Array] Contains [diff_pixels_image, change_regions_hash, max_changes_count]
  def generate_difference_points(reference_img, test_img)
    width = reference_img.columns
    height = reference_img.rows

    # Create an image for difference visualization
    diff_pixels = Magick::Image.new(width, height)
    diff_pixels.background_color = 'white'

    # Track areas with changes for highlighting
    change_regions = {}
    region_size = 20 # Size of grid for change detection
    max_changes_in_region = 0

    # Process differences pixel by pixel
    height.times do |y|
      width.times do |x|
        # Get pixel values from both images
        ref_pixel = reference_img.pixel_color(x, y)
        test_pixel = test_img.pixel_color(x, y)

        # Compare pixels
        if ref_pixel.to_color == test_pixel.to_color
          # Unchanged pixels - make slightly transparent
          diff_pixels.pixel_color(x, y,
                                  Magick::Pixel.new(
                                    test_pixel.red, test_pixel.green, test_pixel.blue, (Magick::QuantumRange * 0.7),
                                  ),)
        else
          # Extract RGB values normalized to 0-255 scale for calculations
          r1 = ref_pixel.red * 255 / Magick::QuantumRange
          g1 = ref_pixel.green * 255 / Magick::QuantumRange
          b1 = ref_pixel.blue * 255 / Magick::QuantumRange
          r2 = test_pixel.red * 255 / Magick::QuantumRange
          g2 = test_pixel.green * 255 / Magick::QuantumRange
          b2 = test_pixel.blue * 255 / Magick::QuantumRange

          # Calculate perceptual difference using luminance-weighted RGB differences
          # Weights based on human perception sensitivity to different colors
          # References: https://zschuessler.github.io/DeltaE/learn
          luminance1 = (0.299 * r1) + (0.587 * g1) + (0.114 * b1)
          luminance2 = (0.299 * r2) + (0.587 * g2) + (0.114 * b2)

          # Calculate absolute differences
          r_diff = (r2 - r1).abs
          g_diff = (g2 - g1).abs
          b_diff = (b1 - b2).abs
          lum_diff = (luminance2 - luminance1).abs

          # Calculate weighted difference magnitude (perceptually adjusted)
          # This better accounts for human perception of color differences
          diff_magnitude = Math.sqrt(
            (0.299 * (r_diff**2)) +
            (0.587 * (g_diff**2)) +
            (0.114 * (b_diff**2)),
          ) / 255.0

          # Determine the color based on perceptual analysis of the change
          diff_color = if diff_magnitude > 0.8
                         # Significant difference - bright red with high opacity
                         Magick::Pixel.new(Magick::QuantumRange, 0, 0, (Magick::QuantumRange * 0.95))
                       elsif lum_diff > 40
                         if luminance2 < luminance1
                           # Test is darker than reference - vivid green (addition)
                           Magick::Pixel.new(0, (Magick::QuantumRange * 0.8), 0, (Magick::QuantumRange * 0.85))
                         else
                           # Test is brighter than reference - vivid orange (removal)
                           Magick::Pixel.new(Magick::QuantumRange, (Magick::QuantumRange * 0.5), 0,
                                             (Magick::QuantumRange * 0.85),)
                         end
                       elsif [r_diff, g_diff, b_diff].max > 30
                         # Color shift without major luminance change - blue
                         Magick::Pixel.new(0, 0, Magick::QuantumRange, (Magick::QuantumRange * 0.8))
                       else
                         # Minor changes - purple with opacity based on severity
                         opacity = 0.6 + (diff_magnitude * 0.3) # 0.6 to 0.9 based on severity
                         Magick::Pixel.new(
                           (Magick::QuantumRange * 0.7),
                           0,
                           (Magick::QuantumRange * 0.7),
                           (Magick::QuantumRange * opacity),
                         )
                       end

          # Set the diff pixel
          diff_pixels.pixel_color(x, y, diff_color)

          # Track regions with changes - weighted by difference magnitude
          region_x = (x / region_size).floor
          region_y = (y / region_size).floor
          change_regions[[region_x, region_y]] ||= 0

          # Weight more significant differences higher in the region tracking
          change_weight = 1 + (diff_magnitude * 4).to_i
          change_regions[[region_x, region_y]] += change_weight
          max_changes_in_region = [max_changes_in_region, change_regions[[region_x, region_y]]].max
        end
      end
    end

    [diff_pixels, change_regions, max_changes_in_region]
  end

  # Draws visual indicators (circles) around areas with significant changes
  # @param diff_img [Magick::Image] The composite diff image
  # @param change_regions [Hash] Map of change regions and their counts
  # @param max_changes [Integer] Maximum number of changes in any region
  # @param width [Integer] Width of one section of the image
  # @return [void]
  def draw_difference_shapes(diff_img, change_regions, max_changes, width)
    region_size = 20 # Size of grid for change detection

    draw = Magick::Draw.new

    # Add visual indicators (circles) around areas with significant changes
    change_regions.each do |coords, count|
      next if count < [max_changes * 0.1, 5].max # Skip minor change regions

      region_x, region_y = coords
      center_x = (region_x * region_size) + (region_size / 2) + (width * 2)
      center_y = (region_y * region_size) + (region_size / 2)

      # Calculate circle intensity based on change density
      intensity = [count.to_f / max_changes, 1.0].min
      radius = (region_size * 0.6 * intensity).ceil

      # Draw circle using RMagick
      draw.stroke('yellow').stroke_opacity(0.6).fill('transparent').stroke_width(2)
      draw.circle(center_x, center_y, center_x + radius, center_y)
    end

    # Draw the circles on the image
    draw.draw(diff_img)
  end

  # Adds section labels and color legend to the diff image
  # @param diff_img [Magick::Image] The composite diff image
  # @param width [Integer] Width of one section of the image
  # @param height [Integer] Height of the image content (without legend)
  # @param legend_height [Integer] Height of the legend area
  # @return [void]
  def add_legend_and_labels(diff_img, width, height, legend_height)
    # Create a new approach for text labels
    draw = Magick::Draw.new
    draw.font_family('Arial')
    draw.pointsize(16)
    draw.font_weight(Magick::BoldWeight)
    draw.fill('black')
    draw.stroke('white')
    draw.stroke_width(0.5)
    draw.text_antialias(true)

    # Draw white background for the legend area
    bg_draw = Magick::Draw.new
    bg_draw.fill('white')
    bg_draw.rectangle(0, height, width * 3, height + legend_height)
    bg_draw.draw(diff_img)

    # Draw section headers with colored background
    section_header_bg = Magick::Draw.new
    section_header_bg.fill('#f0f0f0')
    section_header_bg.rectangle(0, height, width, height + 25)
    section_header_bg.rectangle(width, height, width * 2, height + 25)
    section_header_bg.rectangle(width * 2, height, width * 3, height + 25)
    section_header_bg.draw(diff_img)

    # Set text properties for center-aligned headers
    draw.gravity(Magick::CenterGravity)
    label_y_pos = height + 12 # Position in the middle of header area

    # Add section header labels - properly centered in each column
    draw.annotate(diff_img, width, 25, 0.5 * width, label_y_pos, 'REFERENCE')
    draw.annotate(diff_img, width, 25, 1.5 * width, label_y_pos, 'TEST')
    draw.annotate(diff_img, width, 25, 2.5 * width, label_y_pos, 'DIFF')

    # --- Add Legend Text ---
    legend_y_start = height + 35 # Move down a bit from the headers
    legend_x_start = 20
    box_size = 14 # Slightly larger boxes for better visibility

    # Add color legend boxes and labels
    colors = [
      ['red', 'Significant Diff', legend_x_start],
      ['green', 'Addition (Darker)', legend_x_start + 150],
      ['orange', 'Removal (Brighter)', legend_x_start + 300],
      ['blue', 'Color Shift', legend_x_start + 450],
      ['purple', 'Minor Change', legend_x_start + 600],
      ['yellow', 'Change Area', legend_x_start + 750],
    ]

    colors.each do |color, text, x_pos|
      # Draw colored box with border
      color_draw = Magick::Draw.new
      color_draw.fill(color)
      color_draw.stroke('black')
      color_draw.stroke_width(0.5)
      color_draw.rectangle(x_pos, legend_y_start, x_pos + box_size, legend_y_start + box_size)
      color_draw.draw(diff_img)

      # Draw text label with proper vertical alignment
      text_draw = Magick::Draw.new
      text_draw.font_family('Arial')
      text_draw.pointsize(12)
      text_draw.fill('black')

      # Use center gravity for vertical alignment
      text_draw.gravity(Magick::CenterGravity)

      # Position text to right of box, vertically centered
      text_x = x_pos + box_size + 5
      text_y = legend_y_start + (box_size / 2)

      # Width makes room for text, height centers vertically with box
      text_draw.annotate(diff_img, 135, box_size, text_x, text_y + (box_size / 2), text)
    end
  end
end

# --- RSpec Configuration ---
# Use a separate configure block to ensure it runs after initializers
RSpec.configure do |config|
  config.before(:suite) do
    # Clean test/diff directories before the suite runs
    FileUtils.rm_rf(VisualRegressionHelper::TEST_SCREENSHOT_PATH)
    FileUtils.rm_rf(VisualRegressionHelper::DIFF_SCREENSHOT_PATH)
    FileUtils.mkdir_p(VisualRegressionHelper::TEST_SCREENSHOT_PATH)
    FileUtils.mkdir_p(VisualRegressionHelper::DIFF_SCREENSHOT_PATH)

    # Ensure reference directory exists (might be cleaned by other processes)
    FileUtils.mkdir_p(VisualRegressionHelper::REFERENCE_SCREENSHOT_PATH)

  rescue StandardError => e
    # Raise a more informative error if setup fails
    raise "Visual Regression Setup Error: Failed to clean or create screenshot directories before test suite.\n  - Check permissions for:\n    - #{VisualRegressionHelper::REFERENCE_SCREENSHOT_PATH}\n    - #{VisualRegressionHelper::TEST_SCREENSHOT_PATH}\n    - #{VisualRegressionHelper::DIFF_SCREENSHOT_PATH}\n  - Error: #{e.message}"
  end

  # Include the helper methods in relevant RSpec groups
  # Adjust :feature / :system based on your test types
  config.include VisualRegressionHelper, type: :feature
  config.include VisualRegressionHelper, type: :system
end
