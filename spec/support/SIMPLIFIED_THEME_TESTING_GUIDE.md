# Simplified Visual Regression Theme Testing Framework

This guide explains the simplified, non-metaprogramming approach to visual regression theme testing that eliminates complexity while maintaining DRY principles.

## Overview

The refactored framework removes complex metaprogramming patterns and replaces them with explicit, readable code that's easy to understand and maintain.

## Key Improvements

### ❌ **Before: Complex Metaprogramming**
```ruby
# Complex, hard to understand
def test_scenarios_with_and_without_themes(test_scenarios, themes_to_test: table_themes.keys)
  context 'when testing without theme' do
    test_scenarios.each do |scenario|
      it scenario[:name] do
        # Dynamic method calling - hard to debug
        base_method = scenario[:aml_method].to_s.gsub('themed_', '')
        definition_aml = send(base_method, **scenario[:aml_params])
        # ...
      end
    end
  end
end
```

### ✅ **After: Explicit, Clear Code**
```ruby
# Simple, explicit, easy to understand
def test_scenarios_with_and_without_themes(test_scenarios, themes_to_test: table_themes.keys)
  context 'when testing without theme' do
    test_scenarios.each do |scenario|
      it scenario[:name] do
        run_test_scenario(scenario, with_theme: false)
      end
    end
  end

  context 'when testing with table themes' do
    themes_to_test.each do |theme_key|
      theme_name = table_themes[theme_key][:name]
      
      context "with #{theme_name}" do
        test_scenarios.each do |scenario|
          it "#{scenario[:name]} with #{theme_name}" do
            run_test_scenario(scenario, with_theme: true, theme_key: theme_key)
          end
        end
      end
    end
  end
end
```

## New Test Scenario Format

### ❌ **Before: Confusing Method References**
```ruby
let(:data_table_test_scenarios) do
  [
    {
      name: 'basic table',
      aml_method: :themed_data_table_aml,  # Confusing - why "themed" for both cases?
      aml_params: {},
      table_selector: '[data-uname="v1"] .h-table'
    }
  ]
end
```

### ✅ **After: Clear Visualization Type**
```ruby
let(:data_table_test_scenarios) do
  [
    {
      name: 'basic table',
      viz_type: :data_table,  # Clear - this is a data table test
      aml_params: {},
      table_selector: '[data-uname="v1"] .h-table'
    }
  ]
end
```

## How It Works

### 1. **Explicit AML Generation**
Instead of dynamic method calling, we use explicit case statements:

```ruby
def generate_aml_for_scenario(scenario, with_theme:, theme_key: nil)
  viz_type = scenario[:viz_type]
  aml_params = scenario[:aml_params] || {}
  
  case viz_type
  when :data_table
    if with_theme
      themed_data_table_aml(theme_key: theme_key, **aml_params)
    else
      data_table_aml(**aml_params)
    end
  when :metric_sheet
    if with_theme
      themed_metric_sheet_aml(theme_key: theme_key, **aml_params)
    else
      metric_sheet_aml(**aml_params)
    end
  when :pivot_table
    if with_theme
      themed_pivot_table_aml(theme_key: theme_key, **aml_params)
    else
      pivot_table_aml(**aml_params)
    end
  else
    raise ArgumentError, "Unknown visualization type: #{viz_type}"
  end
end
```

### 2. **Clear Test Execution**
```ruby
def run_test_scenario(scenario, with_theme:, theme_key: nil)
  # Generate AML based on visualization type and theme preference
  definition_aml = generate_aml_for_scenario(scenario, with_theme: with_theme, theme_key: theme_key)
  
  # Setup and run the test
  setup_visual_test(definition_aml: definition_aml, table_selector: scenario[:table_selector])
  
  # Execute any additional test steps (e.g., interactions)
  scenario[:additional_steps]&.call
  
  # Assert visual match
  assert_visual_match selector: scenario[:table_selector]
end
```

## Usage Example

```ruby
describe 'Visual Regression: Data Table theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    # Feature toggles
    FeatureToggle.toggle_global('viz:table_v2', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
  end

  # Define test scenarios once - clear and explicit
  let(:data_table_test_scenarios) do
    [
      {
        name: 'basic table',
        viz_type: :data_table,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-table'
      },
      {
        name: 'table with conditional formatting',
        viz_type: :data_table,
        aml_params: { 
          settings_aml: <<~SETTINGS
            conditional_formats: [
              ConditionalFormat {
                key: '3_value'
                format: SingleFormat {
                  condition { operator: 'greater_than', value: 20 }
                  text_color: '#328159'
                  background_color: '#B5E3CD'
                }
              }
            ]
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-table'
      },
      {
        name: 'interactive table with range selection',
        viz_type: :data_table,
        aml_params: { settings_aml: 'show_row_number: true' },
        table_selector: '[data-uname="v1"] .h-table',
        additional_steps: -> {
          first_header = page.find('.ag-header-cell[col-id="3_date_and_time"]')
          data_header = page.find('.ag-header-cell[col-id="3_customer_type"]')
          first_header.drag_to(data_header)
          sleep 1
        }
      }
    ]
  end

  # One line to run all scenarios with and without themes!
  define_theme_test_scenarios(data_table_test_scenarios)
end
```

## Benefits

### 🎯 **Developer Experience**
- **Easy to understand**: No metaprogramming knowledge required
- **Easy to debug**: Clear execution path, no dynamic method calls
- **Easy to extend**: Add new visualization types by updating the case statement

### 🔧 **Maintainability**
- **Explicit code paths**: Each visualization type is handled explicitly
- **Clear error messages**: Helpful error when unknown viz_type is used
- **Type safety**: IDE can provide better autocomplete and error detection

### 📊 **Functionality**
- **Same test coverage**: All scenarios run with and without themes
- **Same DRY benefits**: Test scenarios defined once, executed multiple ways
- **Interactive support**: `additional_steps` still work perfectly

### 🚀 **Performance**
- **No string manipulation**: No more `gsub` operations on method names
- **No dynamic dispatch**: Direct method calls are faster
- **Better caching**: Static method calls can be optimized by Ruby

## Migration Guide

To migrate existing test files:

1. **Change `aml_method` to `viz_type`**:
   ```ruby
   # Before
   aml_method: :themed_data_table_aml
   
   # After  
   viz_type: :data_table
   ```

2. **Supported visualization types**:
   - `:data_table` - for DataTable visualizations
   - `:metric_sheet` - for MetricSheet visualizations  
   - `:pivot_table` - for PivotTable and Transpose PivotTable visualizations

3. **Everything else stays the same**: `aml_params`, `table_selector`, `additional_steps` work exactly as before

This simplified approach maintains all the benefits of the DRY refactoring while making the code much more accessible to developers at all skill levels.
