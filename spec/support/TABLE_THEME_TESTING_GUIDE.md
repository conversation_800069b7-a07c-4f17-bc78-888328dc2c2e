# Table Theme Testing Mechanism Guide

This guide explains how to use the comprehensive table theme testing mechanism implemented in the `visual_regression_theme` shared context.

## Overview

The table theme testing mechanism allows you to:
- Test different table themes across multiple visualization types (DataTable, PivotTable, MetricSheet)
- Apply themes at dashboard level or visualization level
- Batch test multiple scenarios across all themes
- Create custom themes for specific test cases
- Perform visual regression testing with theme variations

## Available Themes

The mechanism includes three predefined themes:

### 1. Colorful Theme (`:colorful_theme`)
- Bright, high-contrast colors
- Blue background with yellow banding
- Gradient headers with bold styling
- Ideal for testing theme application and visual impact

### 2. Oasis Theme (`:oasis_theme`)
- Professional, subtle styling
- Light blue gradient background
- Consistent header styling
- Based on real-world theme examples

### 3. Minimal Theme (`:minimal_theme`)
- Clean, minimal styling
- White background with light gray accents
- Simple, readable design
- Good for testing basic theme functionality

## Basic Usage

### 1. Individual Theme Testing

```ruby
describe 'DataTable with colorful theme' do
  it 'renders correctly' do
    definition_aml = themed_data_table_aml(theme_key: :colorful_theme)
    
    setup_visual_test(definition_aml: definition_aml, table_selector: '[data-uname="v1"] .h-table')
    
    assert_visual_match selector: '[data-uname="v1"] .h-table'
  end
end
```

### 2. Theme with Custom Settings

```ruby
it 'renders data table with conditional formatting' do
  settings_aml = <<~SETTINGS
    conditional_formats: [
      ConditionalFormat {
        key: '3_value'
        format: SingleFormat {
          condition {
            operator: 'greater_than'
            value: 20
          }
          text_color: '#328159'
          background_color: '#B5E3CD'
        }
      }
    ]
  SETTINGS
  
  definition_aml = themed_data_table_aml(
    theme_key: :oasis_theme,
    settings_aml: settings_aml
  )
  
  setup_visual_test(definition_aml: definition_aml, table_selector: '[data-uname="v1"] .h-table')
  
  assert_visual_match selector: '[data-uname="v1"] .h-table'
end
```

### 3. Batch Testing with `test_with_themes`

```ruby
describe 'DataTable scenarios' do
  let(:scenarios) do
    [
      {
        name: 'basic data table',
        aml_method: :themed_data_table_aml,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-table'
      },
      {
        name: 'data table with row numbers',
        aml_method: :themed_data_table_aml,
        aml_params: { settings_aml: 'show_row_number: true' },
        table_selector: '[data-uname="v1"] .h-table'
      }
    ]
  end

  # This will test both scenarios with all available themes
  test_with_themes(scenarios)
  
  # Or test with specific themes only
  test_with_themes(scenarios, themes_to_test: [:colorful_theme, :minimal_theme])
end
```

## Available Helper Methods

### Theme-Specific AML Generators

- `themed_data_table_aml(theme_key:, **options)` - DataTable with theme
- `themed_metric_sheet_aml(theme_key:, **options)` - MetricSheet with theme  
- `themed_pivot_table_aml(theme_key:, **options)` - PivotTable with theme

### Theme Application Levels

Both dashboard-level and visualization-level theme application are supported:

```ruby
# Dashboard-level theme (affects all visualizations)
themed_data_table_aml(theme_key: :colorful_theme, theme_application: 'dashboard_level')

# Visualization-level theme (affects only this specific viz)
themed_data_table_aml(theme_key: :colorful_theme, theme_application: 'viz_level')
```

### Interactive Testing

```ruby
{
  name: 'data table with range selection',
  aml_method: :themed_data_table_aml,
  aml_params: { theme_key: :colorful_theme },
  table_selector: '[data-uname="v1"] .h-table',
  additional_steps: -> {
    # Perform range selection
    first_header = page.find('.ag-header-cell[col-id="3_date_and_time"]')
    data_header = page.find('.ag-header-cell[col-id="3_customer_type"]')
    first_header.drag_to(data_header)
    sleep 1
  }
}
```

## Advanced Usage

### Adding Custom Themes

You can extend the `table_themes` method to add custom themes:

```ruby
def custom_table_themes
  table_themes.merge({
    dark_theme: {
      name: 'dark_table_theme',
      definition: <<~THEME
        TableTheme dark_table_theme {
          general {
            bg_color: '#2d3748';
            font_color: '#ffffff';
            banding_color: '#4a5568';
            hover_color: '#718096';
          }
          header {
            bg_color: '#1a202c';
            font_color: '#ffffff';
            font_weight: 'bold';
          }
        }
      THEME
    }
  })
end
```

### Testing Specific Visualization Features

```ruby
describe 'PivotTable with themes' do
  test_with_themes([
    {
      name: 'pivot with subtotals',
      aml_method: :themed_pivot_table_aml,
      aml_params: { 
        settings_aml: 'show_sub_total: true',
        position: 'pos(20, 20, 1160, 500)'
      },
      table_selector: '[data-uname="v1"] .h-pivot'
    }
  ])
end
```

## Best Practices

1. **Use descriptive test names** that include the theme and scenario
2. **Test both dashboard-level and viz-level** theme application
3. **Include interactive scenarios** for comprehensive coverage
4. **Use batch testing** for efficiency when testing multiple themes
5. **Test with different visualization settings** (row numbers, totals, etc.)
6. **Verify theme inheritance** and override behavior

## Example Test Structure

```ruby
describe 'Visual Regression: Table Theme Testing' do
  include_context 'visual_regression_theme'

  context 'when testing table themes' do
    before do
      # Enable necessary feature toggles
    end

    describe 'DataTable scenarios' do
      test_with_themes(data_table_scenarios)
    end

    describe 'PivotTable scenarios' do
      test_with_themes(pivot_table_scenarios)
    end

    describe 'MetricSheet scenarios' do
      test_with_themes(metric_sheet_scenarios)
    end

    describe 'Theme application levels' do
      # Test dashboard vs viz level application
    end

    describe 'Interactive scenarios' do
      # Test with user interactions
    end
  end
end
```

This mechanism provides a comprehensive framework for testing table themes across all visualization types while maintaining clean, maintainable test code.
