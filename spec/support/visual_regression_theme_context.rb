# typed: false
# frozen_string_literal: true

# Shared context for visual regression testing of dashboard themes
# This context provides common setup, helper methods, and test patterns
# used across different visualization theme specs.
shared_context 'visual_regression_theme' do
  include_context 'dashboards_v4'
  include ActiveSupport::Testing::TimeHelpers # For travel_to

  # Common test data used across all visual regression tests
  let(:admin) { get_test_admin }
  let(:query_model_sql) do
    <<~SQL
      SELECT *
        FROM (VALUES
          ('2024-02-03T10:00:00.000000'::timestamp, 45,  'Electronics', 3, 1250.75, 'North', 'Premium', 'Online', 'Laptops'),
          ('2024-02-20T15:30:00.000000'::timestamp, 30,  'Electronics', 5, 950.25, 'South', 'Regular', 'Retail', 'Phones'),
          ('2024-03-10T09:15:00.000000'::timestamp, 55,  'Clothing', 8, 125.50, 'East', 'Premium', 'Online', 'Outerwear'),
          ('2024-04-05T14:45:00.000000'::timestamp, -10, 'Groceries', 12, 45.99, 'West', 'Regular', 'Retail', 'Produce'),
          ('2024-05-15T11:30:00.000000'::timestamp, 0,  'Home Goods', 4, 320.00, 'South', 'Business', 'Retail', 'Kitchen'),
          ('2024-06-25T16:20:00.000000'::timestamp, 80,  'Electronics', 9, 1800.00, 'East', 'Premium', 'Online', 'Audio'),
          ('2024-07-15T08:30:45.000000'::timestamp, 35, 'Electronics', 4, 899.99, 'North', 'Premium', 'Online', 'Tablets'),
          ('2024-08-20T14:45:30.000000'::timestamp, 28, 'Electronics', 6, 499.50, 'South', 'Regular', 'Retail', 'Accessories'),
          ('2024-09-10T09:15:00.000000'::timestamp, 42, 'Clothing', 3, 139.99, 'East', 'Business', 'Online', 'Casual'),
          ('2024-10-05T11:30:15.000000'::timestamp, -5, 'Clothing', 7, 249.50, 'West', 'Regular', 'Retail', 'Formal'),
          ('2024-11-22T16:00:45.000000'::timestamp, 60, 'Groceries', 9, 65.75, 'North', 'Premium', 'Online', 'Organic'),
          ('2024-12-14T08:45:00.000000'::timestamp, -12, 'Groceries', 11, 37.25, 'South', 'Regular', 'Retail', 'Frozen'),
          ('2025-01-10T13:20:30.000000'::timestamp, 75, 'Books', 5, 95.50, 'East', 'Business', 'Online', 'Educational'),
          ('2025-01-18T10:00:15.000000'::timestamp, 22, 'Books', 8, 75.25, 'West', 'Regular', 'Retail', 'Children'),
          ('2025-01-25T15:10:45.000000'::timestamp, 50, 'Home Goods', 2, 450.00, 'North', 'Premium', 'Online', 'Furniture'),
          ('2025-02-01T12:45:00.000000'::timestamp, 15, 'Home Goods', 6, 125.75, 'South', 'Regular', 'Retail', 'Appliances'),
          ('2024-07-05T17:45:00.000000'::timestamp, -8,  'Groceries', 15, 78.25, 'West', 'Business', 'Retail', 'Dairy'),
          ('2024-08-15T13:10:00.000000'::timestamp, 95,  'Books', 6, 150.00, 'North', 'Premium', 'Retail', 'Non-Fiction'),
          ('2024-09-20T09:30:00.000000'::timestamp, 40,  'Home Goods', 2, 89.99, 'South', 'Regular', 'Online', 'Decor'),
          ('2025-02-03T17:30:20.000000'::timestamp, 65, 'Books', 3, 125.75, 'East', 'Premium', 'Online', 'Biography')
        )
      AS t (date_and_time, value, category, quantity, price, region, customer_type, sales_channel, subcategory)
    SQL
  end

  # Common setup for all visual regression tests
  before do
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    travel_to Time.zone.parse('2025-02-05 00:00:00')
  end

  # Shared setup steps for visual tests
  # @param definition_aml [String] The AML definition for the dashboard
  # @param table_selector [String] CSS selector for the table element to wait for
  def setup_visual_test(definition_aml:, table_selector:)
    dashboard_table_no_timezone.update!(definition_aml: definition_aml)

    qlogin(admin, "/dashboards/v4/#{dashboard_table_no_timezone.id}/edit")
    wait_for_element_load { page.find_by_id('block-v1') }

    recompile_aml

    wait_for_element_load table_selector
  end

  # Helper method to recompile AML in the dashboard editor
  def recompile_aml
    # Ctrl + E to recompile the AML
    page.driver.browser.action
        .key_down(:control)
        .send_keys('e')
        .key_up(:control)
        .perform
  end

  # Base AML template for dashboard structure
  # @param viz_type [String] The visualization type (e.g., 'DataTable', 'PivotTable', 'MetricSheet')
  # @param viz_label [String] Label for the visualization block
  # @param viz_content [String] The specific visualization configuration
  # @param position [String] Position configuration for the block
  def base_dashboard_aml(viz_type:, viz_label:, viz_content:, position: 'pos(20, 20, 1000, 300)')
    <<~STR
      Dashboard visual_table {
        title: 'Visual Table'
        description: ''''''
        view: CanvasLayout {
          label: 'View 1'
          height: 840
          grid_size: 20
          mobile {
            mode: 'auto'
          }
          block v1 {
            position: #{position}
            layer: 1
          }
        }
        block v1: VizBlock {
          label: '#{viz_label}'
          viz: #{viz_type} {
            dataset: 'test_data_set'
            #{viz_content}
          }
        }
      }
    STR
  end

  # Default fields for DataTable visualizations
  def default_data_table_fields_aml
    <<~STR
      VizFieldFull {
        ref: ref('new_sql_model', 'date_and_time')
        format { type: 'datetime' }
        uname: '3_date_and_time'
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'category')
        format { type: 'text' }
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'value')
        format { type: 'number', pattern: 'inherited' }
        uname: '3_value'
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'quantity')
        format { type: 'number', pattern: 'inherited' }
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'price')
        format { type: 'number', pattern: 'inherited' }
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'region')
        format { type: 'text' }
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'customer_type')
        format { type: 'text' }
        uname: '3_customer_type'
      }
    STR
  end

  # Default rows for MetricSheet visualizations
  def default_metric_sheet_rows_aml
    <<~STR
      MetricHeading {
        label: 'Heading 1'
        settings {
          background_color: '#e1e3ea'
          text_color: '#31353f'
        }
      },
      MetricSeries {
        field: VizFieldFull {
          ref: ref('new_sql_model', 'value')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
          uname: 'sum_value'
        }
        settings {
          mark_type: 'line'
        }
      },
      MetricHeading {
        label: 'Heading 2'
        settings {
          background_color: '#e1e3ea'
          text_color: '#31353f'
        }
      },
      MetricSeries {
        field: VizFieldFull {
          ref: ref('new_sql_model', 'quantity')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
          uname: 'sum_quantity'
        }
      }
    STR
  end

  # Default rows for PivotTable visualizations
  def default_pivot_table_rows_aml
    <<~STR
      VizFieldFull {
        ref: ref('new_sql_model', 'category')
        format { type: 'text' }
        uname: '3_category'
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'region')
        format { type: 'text' }
        uname: '3_region'
      }
    STR
  end

  # Default columns for PivotTable visualizations
  def default_pivot_table_columns_aml
    <<~STR
      VizFieldFull {
        ref: ref('new_sql_model', 'customer_type')
        format { type: 'text' }
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'sales_channel')
        format { type: 'text' }
      }
    STR
  end

  # Default values for PivotTable visualizations
  def default_pivot_table_values_aml
    <<~STR
      VizFieldFull {
        ref: ref('new_sql_model', 'value')
        aggregation: 'sum'
        format { type: 'number', pattern: 'inherited' }
        uname: 'sum_value'
      },
      VizFieldFull {
        ref: ref('new_sql_model', 'quantity')
        aggregation: 'sum'
        format { type: 'number', pattern: 'inherited' }
        uname: 'sum_quantity'
      }
    STR
  end

  # Default fields for RetentionHeatmap visualizations
  def default_retention_heatmap_fields_aml
    {
      cohort_aml: <<~STR,
        VizFieldFull {
          ref: ref('new_sql_model', 'customer_type')
          format {
            type: 'text'
          }
        }
      STR
      cohort_size_aml: <<~STR,
        VizFieldFull {
          ref: ref('new_sql_model', 'quantity')
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      STR
      cohort_duration_aml: <<~STR,
        VizFieldFull {
          ref: ref('new_sql_model', 'date_and_time')
          transformation: 'datetrunc month'
          format {
            type: 'date'
            pattern: 'LLL yyyy'
          }
        }
      STR
      cohort_value_aml: <<~STR,
        VizFieldFull {
          ref: ref('new_sql_model', 'value')
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      STR
    }
  end

  # Default fields for ConversionFunnel visualizations
  def default_conversion_funnel_fields_aml
    {
      breakdown_by_aml: <<~STR,
        VizFieldFull {
          ref: ref('new_sql_model', 'customer_type')
          format {
            type: 'text'
          }
        }
      STR
      values_aml: <<~STR,
        VizFieldFull {
          ref: ref('new_sql_model', 'value')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        },
        VizFieldFull {
          ref: ref('new_sql_model', 'quantity')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      STR
    }
  end

  # Helper to generate AML for DataTable visualizations
  def data_table_aml(fields_aml: default_data_table_fields_aml, settings_aml: '', position: 'pos(20, 20, 1000, 300)')
    viz_content = <<~CONTENT
      fields: [
        #{fields_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    base_dashboard_aml(viz_type: 'DataTable', viz_label: 'Table', viz_content: viz_content, position: position)
  end

  # Helper to generate AML for MetricSheet visualizations
  def metric_sheet_aml(rows_aml: default_metric_sheet_rows_aml, settings_aml: '', position: 'pos(20, 20, 1000, 300)')
    viz_content = <<~CONTENT
      date_field: VizFieldFull {
        ref: ref('new_sql_model', 'date_and_time')
        transformation: 'datetrunc month'
        format {
          type: 'date'
          pattern: 'LLL yyyy'
        }
        uname: 'month_date_and_time'
      }
      rows: [
        #{rows_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    base_dashboard_aml(viz_type: 'MetricSheet', viz_label: 'Metric Sheet', viz_content: viz_content, position: position)
  end

  # Helper to generate AML for PivotTable visualizations
  def pivot_table_aml(rows_aml: default_pivot_table_rows_aml,
                      columns_aml: default_pivot_table_columns_aml,
                      values_aml: default_pivot_table_values_aml,
                      settings_aml: '',
                      position: 'pos(20, 20, 1000, 300)')
    viz_content = <<~CONTENT
      rows: [
        #{rows_aml}
      ]
      columns: [
        #{columns_aml}
      ]
      values: [
        #{values_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    base_dashboard_aml(viz_type: 'PivotTable', viz_label: 'Pivot', viz_content: viz_content, position: position)
  end

  # Helper to generate AML for RetentionHeatmap visualizations
  def retention_heatmap_aml(cohort_aml: nil, cohort_size_aml: nil, cohort_duration_aml: nil, cohort_value_aml: nil,
                            settings_aml: '', position: 'pos(0, 0, 1200, 300)')
    defaults = default_retention_heatmap_fields_aml
    cohort_aml ||= defaults[:cohort_aml]
    cohort_size_aml ||= defaults[:cohort_size_aml]
    cohort_duration_aml ||= defaults[:cohort_duration_aml]
    cohort_value_aml ||= defaults[:cohort_value_aml]

    viz_content = <<~CONTENT
      cohort: #{cohort_aml.strip}
      cohort_size: #{cohort_size_aml.strip}
      cohort_duration: #{cohort_duration_aml.strip}
      cohort_value: #{cohort_value_aml.strip}
      settings {
        #{settings_aml}
      }
    CONTENT

    base_dashboard_aml(viz_type: 'RetentionHeatmap', viz_label: 'Retention Heatmap', viz_content: viz_content,
                       position: position,)
  end

  # Helper to generate AML for ConversionFunnel visualizations
  def conversion_funnel_aml(breakdown_by_aml: nil, values_aml: nil, settings_aml: '', position: 'pos(40, 20, 840, 400)')
    defaults = default_conversion_funnel_fields_aml
    breakdown_by_aml ||= defaults[:breakdown_by_aml]
    values_aml ||= defaults[:values_aml]

    viz_content = <<~CONTENT
      breakdown_by: #{breakdown_by_aml.strip}
      values: [
        #{values_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    base_dashboard_aml(viz_type: 'ConversionFunnel', viz_label: 'Conversion Funnel', viz_content: viz_content,
                       position: position,)
  end

  # ============================================================================
  # TABLE THEME TESTING MECHANISM
  # ============================================================================

  # Predefined table themes for testing different styling scenarios
  def table_themes
    {
      colorful_theme: {
        name: 'colorful_table_theme',
        definition: <<~THEME,
          TableTheme colorful_table_theme {
            general {
              bg_color: '#00BFFF';
              banding_color: '#f9ebae';
              hover_color: 'blue';
              font_family: 'Arial'
              font_size: '12px';
              font_color: 'red';
              border_color: 'red';
              border_width: '2px';
              grid_color: 'green';
            }

            header {
              bg_color: 'linear-gradient(to right, #E74C3C, #F1C40F, #2ECC71)';
              font_size: '16px';
              font_weight: 'bold';
              font_color: 'blue';
            }

            sub_header {
              bg_color: '#9b59b6';
              font_size: '20px';
              font_weight: 'normal';
              font_color: 'blue';
            }

            sub_title {
              font_size: '12px';
              font_weight: 'light';
              font_color: 'green';
            }
          }
        THEME
      },

      oasis_theme: {
        name: 'oasis_table_theme',
        definition: <<~THEME,
          TableTheme oasis_table_theme {
            general {
              bg_color: 'linear-gradient(to top, #ECF5FD, #FEFFFF)';
              banding_color: '#EEF5FC';
              hover_color: '#D9E5F4';
              font_size: '12px';
              font_color: '#3C5C7D';
              border_color: '#538CC7';
              grid_color: '#538CC7';
            }

            header {
              bg_color: '#AECEEE';
              font_size: '14px';
              font_color: '#243577';
              font_weight: 'bold';
            }

            sub_header {
              bg_color: '#AECEEE';
              font_size: '14px';
              font_color: '#243577';
              font_weight: 'bold';
            }
          }
        THEME
      },

      minimal_theme: {
        name: 'minimal_table_theme',
        definition: <<~THEME,
          TableTheme minimal_table_theme {
            general {
              bg_color: '#ffffff';
              banding_color: '#f8f9fa';
              hover_color: '#e9ecef';
              font_size: '14px';
              font_color: '#212529';
              border_color: '#dee2e6';
              grid_color: '#dee2e6';
            }

            header {
              bg_color: '#f8f9fa';
              font_size: '14px';
              font_color: '#495057';
              font_weight: 'bold';
            }
          }
        THEME
      },
    }
  end

  # Generate dashboard AML with table theme applied
  # @param viz_type [String] The visualization type ('DataTable', 'PivotTable', 'MetricSheet')
  # @param theme_key [Symbol] Key from table_themes hash (:colorful_theme, :oasis_theme, :minimal_theme)
  # @param viz_content [String] The visualization-specific content
  # @param position [String] Position configuration for the block
  # @param theme_application [String] How to apply the theme ('dashboard_level', 'viz_level')
  def themed_dashboard_aml(viz_type:, theme_key:, viz_content:, position: 'pos(20, 20, 1000, 300)',
                           theme_application: 'dashboard_level')
    theme_info = table_themes[theme_key]
    raise ArgumentError, "Unknown theme: #{theme_key}" unless theme_info

    theme_definition = theme_info[:definition]
    theme_name = theme_info[:name]

    case theme_application
    when 'dashboard_level'
      # Apply theme at dashboard level (affects all visualizations)
      <<~STR
        #{theme_definition}

        Dashboard visual_table {
          title: 'Visual Table'
          description: ''''''
          theme: H.themes.vanilla.extend({
            viz {
              table: #{theme_name}
            }
          })
          view: CanvasLayout {
            label: 'View 1'
            height: 840
            grid_size: 20
            mobile {
              mode: 'auto'
            }
            block v1 {
              position: #{position}
              layer: 1
            }
          }
          block v1: VizBlock {
            label: '#{viz_type}'
            viz: #{viz_type} {
              dataset: 'test_data_set'
              #{viz_content}
            }
          }
        }
      STR
    when 'viz_level'
      # Apply theme at visualization level (affects only this specific viz)
      <<~STR
        #{theme_definition}

        Dashboard visual_table {
          title: 'Visual Table'
          description: ''''''
          view: CanvasLayout {
            label: 'View 1'
            height: 840
            grid_size: 20
            mobile {
              mode: 'auto'
            }
            block v1 {
              position: #{position}
              layer: 1
            }
          }
          block v1: VizBlock {
            label: '#{viz_type}'
            viz: #{viz_type} {
              dataset: 'test_data_set'
              #{viz_content}
              theme {
                table: #{theme_name}
              }
            }
          }
        }
      STR
    else
      raise ArgumentError, "Unknown theme_application: #{theme_application}. Use 'dashboard_level' or 'viz_level'"
    end
  end

  # Convenience methods for themed visualizations
  def themed_data_table_aml(theme_key:, fields_aml: default_data_table_fields_aml, settings_aml: '',
                            position: 'pos(20, 20, 1000, 300)', theme_application: 'dashboard_level')
    viz_content = <<~CONTENT
      fields: [
        #{fields_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    themed_dashboard_aml(
      viz_type: 'DataTable',
      theme_key: theme_key,
      viz_content: viz_content,
      position: position,
      theme_application: theme_application,
    )
  end

  def themed_metric_sheet_aml(theme_key:, rows_aml: default_metric_sheet_rows_aml, settings_aml: '',
                              position: 'pos(20, 20, 1000, 300)', theme_application: 'dashboard_level')
    viz_content = <<~CONTENT
      date_field: VizFieldFull {
        ref: ref('new_sql_model', 'date_and_time')
        transformation: 'datetrunc month'
        format {
          type: 'date'
          pattern: 'LLL yyyy'
        }
        uname: 'month_date_and_time'
      }
      rows: [
        #{rows_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    themed_dashboard_aml(
      viz_type: 'MetricSheet',
      theme_key: theme_key,
      viz_content: viz_content,
      position: position,
      theme_application: theme_application,
    )
  end

  def themed_pivot_table_aml(theme_key:, rows_aml: default_pivot_table_rows_aml,
                             columns_aml: default_pivot_table_columns_aml, values_aml: default_pivot_table_values_aml, settings_aml: '', position: 'pos(20, 20, 1000, 300)', theme_application: 'dashboard_level')
    viz_content = <<~CONTENT
      rows: [
        #{rows_aml}
      ]
      columns: [
        #{columns_aml}
      ]
      values: [
        #{values_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    themed_dashboard_aml(
      viz_type: 'PivotTable',
      theme_key: theme_key,
      viz_content: viz_content,
      position: position,
      theme_application: theme_application,
    )
  end

  def themed_retention_heatmap_aml(theme_key:, cohort_aml: nil, cohort_size_aml: nil, cohort_duration_aml: nil,
                                   cohort_value_aml: nil, settings_aml: '', position: 'pos(0, 0, 1200, 300)', theme_application: 'dashboard_level')
    defaults = default_retention_heatmap_fields_aml
    cohort_aml ||= defaults[:cohort_aml]
    cohort_size_aml ||= defaults[:cohort_size_aml]
    cohort_duration_aml ||= defaults[:cohort_duration_aml]
    cohort_value_aml ||= defaults[:cohort_value_aml]

    viz_content = <<~CONTENT
      cohort: #{cohort_aml.strip}
      cohort_size: #{cohort_size_aml.strip}
      cohort_duration: #{cohort_duration_aml.strip}
      cohort_value: #{cohort_value_aml.strip}
      settings {
        #{settings_aml}
      }
    CONTENT

    themed_dashboard_aml(
      viz_type: 'RetentionHeatmap',
      theme_key: theme_key,
      viz_content: viz_content,
      position: position,
      theme_application: theme_application,
    )
  end

  def themed_conversion_funnel_aml(theme_key:, breakdown_by_aml: nil, values_aml: nil, settings_aml: '',
                                   position: 'pos(40, 20, 840, 400)', theme_application: 'dashboard_level')
    defaults = default_conversion_funnel_fields_aml
    breakdown_by_aml ||= defaults[:breakdown_by_aml]
    values_aml ||= defaults[:values_aml]

    viz_content = <<~CONTENT
      breakdown_by: #{breakdown_by_aml.strip}
      values: [
        #{values_aml}
      ]
      settings {
        #{settings_aml}
      }
    CONTENT

    themed_dashboard_aml(
      viz_type: 'ConversionFunnel',
      theme_key: theme_key,
      viz_content: viz_content,
      position: position,
      theme_application: theme_application,
    )
  end

  # Execute a single test scenario with or without theme
  # @param scenario [Hash] Test scenario configuration
  # @param with_theme [Boolean] Whether to apply a theme
  # @param theme_key [Symbol] Theme key to apply (required if with_theme is true)
  def run_test_scenario(scenario, with_theme:, theme_key: nil)
    # Generate AML based on visualization type and theme preference
    definition_aml = generate_aml_for_scenario(scenario, with_theme: with_theme, theme_key: theme_key)

    # Setup and run the test
    setup_visual_test(definition_aml: definition_aml, table_selector: scenario[:table_selector])

    # Execute any additional test steps (e.g., interactions)
    scenario[:additional_steps]&.call

    # Assert visual match
    assert_visual_match selector: scenario[:table_selector]
  end

  # Generate AML for a test scenario with explicit visualization type handling
  # @param scenario [Hash] Test scenario configuration
  # @param with_theme [Boolean] Whether to apply a theme
  # @param theme_key [Symbol] Theme key to apply (required if with_theme is true)
  def generate_aml_for_scenario(scenario, with_theme:, theme_key: nil)
    viz_type = scenario[:viz_type]
    aml_params = scenario[:aml_params] || {}

    case viz_type
    when :data_table
      if with_theme
        themed_data_table_aml(theme_key: theme_key, **aml_params)
      else
        data_table_aml(**aml_params)
      end
    when :metric_sheet
      if with_theme
        themed_metric_sheet_aml(theme_key: theme_key, **aml_params)
      else
        metric_sheet_aml(**aml_params)
      end
    when :pivot_table
      if with_theme
        themed_pivot_table_aml(theme_key: theme_key, **aml_params)
      else
        pivot_table_aml(**aml_params)
      end
    when :retention_heatmap
      if with_theme
        themed_retention_heatmap_aml(theme_key: theme_key, **aml_params)
      else
        retention_heatmap_aml(**aml_params)
      end
    when :conversion_funnel
      if with_theme
        themed_conversion_funnel_aml(theme_key: theme_key, **aml_params)
      else
        conversion_funnel_aml(**aml_params)
      end
    else
      raise ArgumentError,
            "Unknown visualization type: #{viz_type}. Supported types: :data_table, :metric_sheet, :pivot_table, :retention_heatmap, :conversion_funnel"
    end
  end
end
