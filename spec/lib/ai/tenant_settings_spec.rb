# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Ai::TenantSettings do
  it 'get correctly coerce to T::Struct' do
    tenant = Tenant.new(settings: {
      ai: {
        agents: {
          phoebe: {
            enabled: false
          },
          meg: {
            enabled: true
          }
        }
      }
    })
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Phoebe].enabled).to eq(false)
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Meg].enabled).to eq(true)
  end
  it 'always have a default value' do
    tenant = Tenant.new(settings: {})
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Phoebe].enabled).to eq(true)
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Meg].enabled).to eq(true)
    expect(tenant.settings_struct.ai.agents[Ai::Types::AgentName::Docsearch].enabled).to eq(true)
  end
end