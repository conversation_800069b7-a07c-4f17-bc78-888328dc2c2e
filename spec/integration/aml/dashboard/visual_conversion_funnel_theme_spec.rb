# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Conversion Funnel theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  # Individual test cases for all scenarios with unified theme approach
  # This enables line-based test execution (e.g., rspec file_spec.rb:123) and debugging breakpoints
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic conversion funnel' do
      scenario = {
        name: 'basic conversion funnel',
        viz_type: :conversion_funnel,
        aml_params: {},
        table_selector: '[data-uname="v1"] .ci-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'conversion funnel with custom circle color' do
      scenario = {
        name: 'conversion funnel with custom circle color',
        viz_type: :conversion_funnel,
        aml_params: { settings_aml: 'circle_color: "#FEDBDB"' },
        table_selector: '[data-uname="v1"] .ci-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'conversion funnel with custom columns color' do
      scenario = {
        name: 'conversion funnel with custom columns color',
        viz_type: :conversion_funnel,
        aml_params: { settings_aml: 'columns_color: "#F8DDC4"' },
        table_selector: '[data-uname="v1"] .ci-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'conversion funnel with both custom colors' do
      scenario = {
        name: 'conversion funnel with both custom colors',
        viz_type: :conversion_funnel,
        aml_params: {
          settings_aml: <<~SETTINGS,
            circle_color: "#FEDBDB"
            columns_color: "#F8DDC4"
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .ci-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    # Scenarios with colorful theme
    context 'with colorful theme' do
      it 'basic conversion funnel with colorful_table_theme' do
        scenario = {
          name: 'basic conversion funnel',
          viz_type: :conversion_funnel,
          aml_params: {},
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'conversion funnel with custom circle color with colorful_table_theme' do
        scenario = {
          name: 'conversion funnel with custom circle color',
          viz_type: :conversion_funnel,
          aml_params: { settings_aml: 'circle_color: "#FEDBDB"' },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'conversion funnel with custom columns color with colorful_table_theme' do
        scenario = {
          name: 'conversion funnel with custom columns color',
          viz_type: :conversion_funnel,
          aml_params: { settings_aml: 'columns_color: "#F8DDC4"' },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end

      it 'conversion funnel with both custom colors with colorful_table_theme' do
        scenario = {
          name: 'conversion funnel with both custom colors',
          viz_type: :conversion_funnel,
          aml_params: {
            settings_aml: <<~SETTINGS,
              circle_color: "#FEDBDB"
              columns_color: "#F8DDC4"
            SETTINGS
          },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end
    end

    # Scenarios with oasis theme
    context 'with oasis theme' do
      it 'basic conversion funnel with oasis_table_theme' do
        scenario = {
          name: 'basic conversion funnel',
          viz_type: :conversion_funnel,
          aml_params: {},
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'conversion funnel with custom circle color with oasis_table_theme' do
        scenario = {
          name: 'conversion funnel with custom circle color',
          viz_type: :conversion_funnel,
          aml_params: { settings_aml: 'circle_color: "#FEDBDB"' },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'conversion funnel with custom columns color with oasis_table_theme' do
        scenario = {
          name: 'conversion funnel with custom columns color',
          viz_type: :conversion_funnel,
          aml_params: { settings_aml: 'columns_color: "#F8DDC4"' },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end

      it 'conversion funnel with both custom colors with oasis_table_theme' do
        scenario = {
          name: 'conversion funnel with both custom colors',
          viz_type: :conversion_funnel,
          aml_params: {
            settings_aml: <<~SETTINGS,
              circle_color: "#FEDBDB"
              columns_color: "#F8DDC4"
            SETTINGS
          },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end
    end

    # Scenarios with minimal theme
    context 'with minimal theme' do
      it 'basic conversion funnel with minimal_table_theme' do
        scenario = {
          name: 'basic conversion funnel',
          viz_type: :conversion_funnel,
          aml_params: {},
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'conversion funnel with custom circle color with minimal_table_theme' do
        scenario = {
          name: 'conversion funnel with custom circle color',
          viz_type: :conversion_funnel,
          aml_params: { settings_aml: 'circle_color: "#FEDBDB"' },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'conversion funnel with custom columns color with minimal_table_theme' do
        scenario = {
          name: 'conversion funnel with custom columns color',
          viz_type: :conversion_funnel,
          aml_params: { settings_aml: 'columns_color: "#F8DDC4"' },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end

      it 'conversion funnel with both custom colors with minimal_table_theme' do
        scenario = {
          name: 'conversion funnel with both custom colors',
          viz_type: :conversion_funnel,
          aml_params: {
            settings_aml: <<~SETTINGS,
              circle_color: "#FEDBDB"
              columns_color: "#F8DDC4"
            SETTINGS
          },
          table_selector: '[data-uname="v1"] .ci-conversion-funnel',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end
    end
  end
end
