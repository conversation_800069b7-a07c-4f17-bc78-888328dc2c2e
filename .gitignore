# See http://help.github.com/ignore-files/ for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile ~/.gitignore_global

# Ignore cache
.cache

# Ignore bundler config
/.bundle
.tags

# Ignore the default SQLite database.
/db/*.sqlite3

# Ignore all logfiles and tempfiles.
/log/*.log
/tmp
.rspec_status

# Ignore credentials configuration
config/database.yml
databases.yml

docker/ssh/deploy_key
docker/volumes

# Ignore RubyMine
.idea/

# Ignore fixture builder yaml files
/spec/fixtures/*.yml
/spec/fixtures/repositories*

.DS_Store

dump.rdb

.powrc
bower_components/
.vagrant/
.env
*.pyc
docker/ssl/ssl.*
public/assets/
coverage/
tags
junit.xml

# NodeJS stuff
node_modules

.byebug_history
/public/packs/*
/public/packs-test/*
/public/packs-exporters/*

spec/javascripts/jasmine/es6_output/*

.vscode/*
!.vscode/extensions.json

docker/volumes

# Encoded
on-premise/ruby_encoder/holistics_encoded/

yarn-error.log
yarn-debug.log*
.yarn-integrity
.yarnrc
.yarn/

design_system_docs/dist/

# Built binaries
bin/built/*

# Eslint
.eslintcache

# AML Engine
engines/aml_studio/spec/dummy/tmp/
engines/aml_studio/spec/dummy/.sass-cache
engines/aml_studio/spec/dummy/log

# AML Server
packages/aml-server/lib/

# Vite Ruby
/public/vite
/public/vite-dev
/public/vite-test
*.local
.jj

# lockfiles of other package managers
pnpm-lock.yaml
package-lock.json

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Visual Regression
tmp/capybara/screenshots/test/
tmp/capybara/screenshots/diff/
