# RFC 2715: setup visual regression testing

Status: WIP
Date: May 2, 2025 10:30 AM
Owner: <PERSON><PERSON> Ng<PERSON>en
ID: RFC-517

# TLDR

# Motivation

- Our table have large amount of style rule, which dependence on other through multiple way: priority, overlap on same area
- Maintain table style easy break other, this break usually subtle and hard to automation test using through checking style rule on element, or manual test
- Set up pixel vs pixel test to minimize unexpected appearance change to our table, reduce error prone from manual check.

# Background info

- Our currently interation testing system using Rspec, generate seed data to database.

# Detailed design

### Approach 1: Using available visual regression testing tool

Indicate include: **Applit<PERSON>s, <PERSON>, and BackstopJS**

Experience with BackstopJS cause it’s free and have big community. Applit<PERSON><PERSON> and <PERSON> charge moẻ than 25$/month

These tool access provided url (localhost:3000), perform automation script, then capture image and compare them

![image.png](RFC%202715%20setup%20visual%20regression%20testing%201e7f89dc7e498030ad55e9c480132231/image.png)

| Pros | Cons |
| --- | --- |
| GUI support | Run independence with our the Rails\Rspec test |
| Feature rich | Require large effort to set up integration test |
|  |  |

### Approach 2: Implement to Integrated RSpec using image handling gem

Experience with chunky_png and rmagick

[Image comparison gem](RFC%202715%20setup%20visual%20regression%20testing%201e7f89dc7e498030ad55e9c480132231/Image%20comparison%20gem%201e7f89dc7e4980ea9a6fe9f74430b912.csv)

→ Chossing Rmagick cause it more reliable, and don’t be restricted by lack of any feature

---

## End

# Decision records

<aside>
📝 Record significant decisions, considerations, and/or alternative designs. [Ref](https://icepanel.io/blog/2023-03-29-architecture-decision-records-adrs)

</aside>

- **Example**
    
    This is an example from [Decision Records](https://www.notion.so/Decision-Records-e541cc5590d04afd927799be119168e5?pvs=21) 
    
    [Untitled](RFC%202715%20setup%20visual%20regression%20testing%201e7f89dc7e498030ad55e9c480132231/Untitled%201e7f89dc7e4981c9bc23d3bb2387f482.csv)
    

[Decision records table](RFC%202715%20setup%20visual%20regression%20testing%201e7f89dc7e498030ad55e9c480132231/Decision%20records%20table%201e7f89dc7e4981a687bae455ee3c336e.csv)

# Implementation plan (optional)

<aside>
📝 If this is already included in Asana/PRDs, then exclude this. If this is for internal engineering team plan, and not written anywhere else, this should be included.

</aside>

# Research notes (optional)

<aside>
📝 Notes should be put in a sub-pages to avoid cluttering the main page.

</aside>

# Considerations

## Security

<aside>
📝 Please refer to [COD 115: Product Security Assessment](https://www.notion.so/COD-115-Product-Security-Assessment-fa85225d3f6042a5bf55c305befd291f?pvs=21)

</aside>

# Discussion logs (optional)

<aside>
📝  For more detailed discussions, can put those pages into a table here

</aside>