# Simplification Plan for `spec/support/visual_regression_helper.rb`

**Approved:** 2025-04-28

**Context:** This helper is being kept for specific RSpec integration tests alongside a new BackstopJS implementation. The goal is to simplify its capture logic by removing complex, less frequently needed strategies.

## Plan Details

1.  **Remove Unused Capture Strategies:**
    *   Delete the private methods: `try_scroll_and_capture_sections`.
    *   Delete the private method: `try_adjust_viewport`.
    *   Delete the private method: `try_full_page_capture`.

2.  **Simplify `assert_visual_match` Capture Flow:**
    *   Remove the logic that iterates through multiple capture strategies.
    *   The simplified capture flow will be:
        *   Attempt to capture using the native element screenshot method (`element.native.screenshot`) if available.
        *   If the native method fails or is unavailable, fall back *directly* to taking a full-page screenshot (`page.save_screenshot`) and then cropping it using the element's calculated rectangle (`rect`) and padding.
    *   Keep the existing JavaScript for scrolling the element into view and calculating the element's rectangle (`rect`).
    *   Keep the existing logic for handling reference image creation and comparison.

3.  **Update RSpec Configuration:**
    *   No changes needed. The RSpec `before(:suite)` block and the `include` statements remain as the helper module itself is still being used.

## Simplified Capture Flow Diagram

```mermaid
graph TD
    A[Start assert_visual_match] --> B{Find element};
    B --> C{Visible?};
    C -- Yes --> D[Scroll element into view];
    D --> E[Get element rect];
    E --> F{Try native element.screenshot};
    F -- Success --> G[Compare with Reference];
    F -- Failure/Unavailable --> H[Fallback: page.save_screenshot];
    H --> I[Crop full screenshot using rect];
    I --> G;
    G --> J{Diff <= Threshold?};
    J -- Yes --> K[Pass / Cleanup];
    J -- No --> L[Fail / Save Diff Image];
    C -- No --> M[Raise Error: Element not visible];
    B -- Not Found --> N[Raise Error: Selector not found];
```

## Summary of Changes

*   Removes complex capture strategies (`try_scroll_and_capture_sections`, `try_adjust_viewport`, `try_full_page_capture`).
*   Simplifies the core capture logic in `assert_visual_match` to a primary (native) and a single fallback (full-page + crop) method.
*   Retains the core functionality for RSpec integration.