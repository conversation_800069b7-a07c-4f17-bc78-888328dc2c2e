# Visual Regression Testing Guide

## Overview

Visual regression testing in this project uses a custom implementation built on top of Capybara and ChunkyPNG to detect visual changes in the UI. The system compares screenshots of UI elements against reference images to ensure visual consistency.

## How It Works

```mermaid
flowchart TD
    A[Test Execution] --> B[Take Screenshot]
    B --> C{Reference Image Exists?}
    C -->|No| D[Create Reference]
    C -->|Yes| E[Compare Images]
    E --> F{Difference <= Threshold?}
    F -->|Yes| G[Test Passes]
    F -->|No| H[Generate Diff Image]
    H --> I[Test Fails]
    D --> J[Mark Test Pending]
```

## Directory Structure

```
spec/
├── screenshots/
│   └── reference/     # Reference screenshots
└── tmp/capybara/
    └── screenshots/
        ├── test/      # New test screenshots
        └── diff/      # Difference images when tests fail
```

## Setup and Configuration

The visual regression testing system is automatically configured in the RSpec environment through the `VisualRegressionHelper` module. Key configurations:

1. Directory paths:
   ```ruby
   REFERENCE_SCREENSHOT_PATH = Rails.root.join('spec/screenshots/reference')
   TEST_SCREENSHOT_PATH = Rails.root.join('tmp/capybara/screenshots/test')
   DIFF_SCREENSHOT_PATH = Rails.root.join('tmp/capybara/screenshots/diff')
   ```

2. RSpec integration:
   ```ruby
   config.include VisualRegressionHelper, type: :feature
   config.include VisualRegressionHelper, type: :system
   ```

## Usage

### Basic Example

```ruby
describe 'Visual Regression Test', :js, type: :feature do
  it 'matches the expected visual reference' do
    visit '/your-page'
    
    # Compare the entire page
    assert_visual_match('page_name')
    
    # Compare a specific element
    assert_visual_match('element_name', selector: '.your-element')
  end
end
```

### Advanced Usage

```ruby
assert_visual_match(
  'unique_test_name',
  selector: '.specific-element',  # CSS selector of element to capture
  threshold: 0.01,               # Allowed difference percentage (0.01 = 1%)
  wait: 0.5                      # Wait time before capture (for animations)
)
```

## Key Features

1. **Automatic Reference Creation**
   - First run creates reference images
   - Marks test as pending to ensure manual verification
   - Reference images should be committed to version control

2. **Pixel-Perfect Comparison**
   - Uses ChunkyPNG for pixel-by-pixel comparison
   - Configurable threshold for acceptable differences
   - Generates visual diffs for failures

3. **Debugging Support**
   - Saves diff images on failure
   - Uses Capybara's save_and_open_page on element not found
   - Clear error messages with difference percentages

4. **Clean Test Environment**
   - Automatically cleans test and diff directories before test suite
   - Maintains reference images for version control
   - Organizes screenshots in structured directories

## Best Practices

1. **Naming Convention**
   - Use descriptive, unique names for tests
   - Names are automatically parameterized for filenames
   - Example: 'header navigation menu' → 'header_navigation_menu.png'

2. **Selector Usage**
   - Be as specific as possible with selectors
   - Target the smallest necessary element
   - Avoid capturing dynamic content

3. **Handling Dynamic Content**
   - Use appropriate wait times for animations
   - Consider using data-testid attributes for stable selectors
   - Adjust threshold for areas with expected minor variations

4. **Reference Image Management**
   - Review reference images before committing
   - Update references when intentional changes occur
   - Keep reference images in version control

## Real Project Example

From `visual_table_theme_spec.rb`:

```ruby
it 'can handle theme on viz level (visual check)' do
  load_dashboard
  
  assert_visual_match(
    'table theme viz level pivot 2',
    selector: '[data-uname="v2"] .h-pivot'
  )
end
```

## Troubleshooting

1. **Test Failing Due to Small Differences**
   - Adjust the threshold parameter
   - Check for dynamic content in the captured area
   - Review the diff image in the diff directory

2. **Element Not Found**
   - Check selector accuracy
   - Ensure element is visible and rendered
   - Verify wait times for dynamic content

3. **Inconsistent Results**
   - Ensure consistent test environment
   - Check for animations or transitions
   - Verify browser window size and resolution

## Detailed: Pixel Comparison and Diff Generation

The visual regression testing system uses a sophisticated pixel-by-pixel comparison approach implemented using ChunkyPNG. Here's a detailed breakdown of how it works:

### Image Comparison Process

1. **Image Loading**
  ```ruby
  images = [
    ChunkyPNG::Image.from_file(ref_path),
    ChunkyPNG::Image.from_file(test_path)
  ]
  ```
  - Both reference and test images are loaded into memory
  - ChunkyPNG provides direct pixel access capabilities
  - Images are treated as 2D arrays of pixels

2. **Dimension Verification**
  ```ruby
  ref_width, ref_height = images[0].width, images[0].height
  test_width, test_height = images[1].width, images[1].height
  
  unless ref_width == test_width && ref_height == test_height
    raise "Visual Mismatch: Screenshot dimensions differ..."
  end
  ```
  - Ensures both images have identical dimensions
  - Fails fast if dimensions don't match
  - Prevents pixel misalignment issues

3. **Pixel-by-Pixel Comparison**
  ```ruby
  diff_pixels = []
  pixel_count = ref_width * ref_height
  
  images[0].height.times do |y|
    images[0].width.times do |x|
      diff_pixels << [x, y] unless images[0][x, y] == images[1][x, y]
    end
  end
  ```
  - Iterates through each pixel position
  - Compares RGB values at each coordinate
  - Records coordinates of differing pixels

4. **Difference Calculation**
  ```ruby
  diff_percentage = (diff_pixels.length.to_f / pixel_count) * 100
  ```
  - Calculates percentage of differing pixels
  - Based on total number of pixels in image
  - Used for comparison against threshold

### Diff Image Generation

When differences exceed the threshold, a visual diff is generated:

1. **Creation of Diff Image**
  ```ruby
  diff_image = ChunkyPNG::Image.new(ref_width, ref_height, ChunkyPNG::Color::WHITE)
  ```
  - Creates new white background image
  - Same dimensions as original images
  - Provides clean canvas for highlighting differences

2. **Marking Differences**
  ```ruby
  diff_pixels.each { |x, y| diff_image[x, y] = ChunkyPNG::Color::RED }
  ```
  - Marks each differing pixel in red
  - Creates visual representation of changes
  - Makes differences easily identifiable

3. **Saving Diff Image**
  ```ruby
  diff_image.save(diff_path)
  ```
  - Saves to configured diff directory
  - Uses consistent naming convention
  - Available for review after test failure

### Threshold Implementation

The threshold system allows for flexibility in comparison:

```ruby
if diff_percentage <= threshold
 # Clean up test and diff images if they exist
 FileUtils.rm(test_path) if File.exist?(test_path)
 FileUtils.rm(diff_path) if File.exist?(diff_path)
else
 # Generate and save diff image, then fail test
 expect(diff_percentage).to be <= threshold
end
```

- Default threshold is 0.01% (can be customized)
- Handles minor anti-aliasing differences
- Allows for slight variations in rendering

### Performance Considerations

1. **Memory Usage**
  - Images are fully loaded into memory
  - Pixel arrays can be large for big images
  - Consider targeting smaller elements when possible

2. **Processing Time**
  - Pixel-by-pixel comparison is CPU-intensive
  - Time complexity is O(width * height)
  - Larger images take proportionally longer to process

3. **Storage Management**
  - Test and diff images are cleaned up on success
  - Only failed test artifacts are preserved
  - Reference images are maintained in version control

This implementation provides a robust and precise way to detect visual changes while generating useful debugging artifacts when differences are found.

## Detailed: Debugging Failed Visual Regression Tests

### 1. Understanding Test Failures

When a visual regression test fails, you'll typically encounter one of these scenarios:

1. **Pixel Difference Exceeds Threshold**
   ```ruby
   Visual Mismatch: 'element_name' differed by 2.5% (threshold: 0.01%)
   Diff image saved to: tmp/capybara/screenshots/diff/element_name.png
   ```

2. **Dimension Mismatch**
   ```ruby
   Visual Mismatch: Screenshot dimensions differ.
   Reference: 800x600, Test: 800x650
   ```

3. **Element Not Found**
   ```ruby
   Visual Regression Error: Selector '.my-element' not found
   Page content has been saved to tmp/capybara/page.html
   ```

### 2. Systematic Debugging Process

Follow this step-by-step process when debugging failures:

1. **Initial Investigation**
   ```ruby
   # Location of debug artifacts
   Reference: spec/screenshots/reference/[test_name].png
   Test Image: tmp/capybara/screenshots/test/[test_name].png
   Diff Image: tmp/capybara/screenshots/diff/[test_name].png
   ```
   - Compare the three images side by side
   - Check if differences are intentional changes
   - Look for obvious rendering issues

2. **Common Issues and Solutions**

   a. **Timing-Related Issues**
   ```ruby
   assert_visual_match('dynamic_content',
     selector: '.loading-component',
     wait: 2.0  # Increase wait time
   )
   ```
   - Increase wait time for dynamic content
   - Add explicit waits for specific conditions
   - Verify loading states are complete

   b. **Element State Issues**
   ```ruby
   before do
     # Ensure consistent state
     page.execute_script("document.querySelector('.animation').style.animationPlayState = 'paused'")
   end
   ```
   - Disable animations temporarily
   - Force specific element states
   - Clear any dynamic content

   c. **Browser/Environment Issues**
   ```ruby
   before do
     # Force consistent window size
     page.driver.browser.manage.window.resize_to(1024, 768)
   end
   ```
   - Set explicit window dimensions
   - Control browser zoom level
   - Clear browser caches

### 3. Advanced Debugging Techniques

1. **Visual Inspection Tools**
   ```ruby
   # In case of failure
   save_and_open_page                    # View full page HTML
   page.save_screenshot('debug.png')     # Capture full page
   ```

2. **Console Debugging**
   ```ruby
   # Add debug information
   puts "Element dimensions: #{find(selector).size.inspect}"
   puts "Viewport size: #{page.driver.browser.manage.window.size.inspect}"
   ```

3. **Temporary Threshold Adjustment**
   ```ruby
   # Temporarily increase threshold to identify boundary cases
   assert_visual_match('problematic_element',
     threshold: 0.05,  # 5% threshold for investigation
     selector: '.element'
   )
   ```

### 4. Prevention Strategies

1. **Stable Selectors**
   ```ruby
   # Avoid
   assert_visual_match('nav', selector: '.nav-item:first-child')
   
   # Better
   assert_visual_match('nav', selector: '[data-testid="main-nav-item"]')
   ```

2. **State Management**
   ```ruby
   before do
     # Reset to known state
     clear_local_storage
     reset_session!
     stub_dynamic_content
   end
   ```

3. **Documentation**
   ```ruby
   # Add comments explaining expected variations
   assert_visual_match('profile_card',
     selector: '.profile-card',
     threshold: 0.02,  # Higher threshold for variable content
     wait: 1.0        # Wait for avatar to load
   )
   ```

### 5. When to Update Reference Images

1. **Legitimate Changes**
   - Design updates
   - New features
   - Intentional layout changes

2. **Process for Updating**
   ```bash
   # 1. Delete old reference
   rm spec/screenshots/reference/element_name.png
   
   # 2. Run test to generate new reference
   rspec spec/path/to_spec.rb
   
   # 3. Verify new reference
   # 4. Commit updated reference
   git add spec/screenshots/reference/element_name.png
   ```

Remember to document significant reference image updates in your commit messages, explaining why the visual change was necessary and approved.