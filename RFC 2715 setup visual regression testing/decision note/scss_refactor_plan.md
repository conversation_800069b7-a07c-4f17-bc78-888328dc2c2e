# SCSS Refactoring Plan for Table Styles

## 1. Goal
Improve the structure, readability, and maintainability of the table-related SCSS files by applying SCSS best practices, particularly nesting selectors using the `&` operator.

## 2. Scope
All `.scss` files within `app/javascript/core/assets/scss/custom/` and its subdirectories (`standard_table`, `pivot_table`, `metric_sheet`, `cohort_retention`, `data_table`), including:
- `_variables.scss`
- `_general.scss`
- `_handsontable.scss`
- All files within the subdirectories.

## 3. Strategy
- Iterate through each file in the scope.
- Analyze the existing CSS rules.
- Identify opportunities to use the `&` selector for nesting child or related state selectors (e.g., `.parent .child` becomes `.parent { &.child { ... } }` or `.parent { .child { ... } }`).
- Combine selectors where appropriate without losing specificity or clarity.
- Ensure the refactoring doesn't change the final compiled CSS output's behavior or specificity unintentionally.
- Maintain the existing file structure and import order defined in `_table.scss`.

## 4. Order of Refactoring
Process files logically, starting from base/general files and moving to specifics:
1.  `_variables.scss` (Review - unlikely to need nesting changes)
2.  `_general.scss`
3.  `standard_table/` partials (e.g., `_header_table.scss`, `_body_table.scss`) followed by `_general_table.scss`.
4.  `pivot_table/` partials followed by `_general_pivot.scss`.
5.  `metric_sheet/` partials followed by `_general_metric_sheet.scss`.
6.  `cohort_retention/` partials followed by `_general_cohort.scss`.
7.  `data_table/` partials followed by `_general_data_table.scss`.
8.  `_handsontable.scss` (Address this last due to its size and complexity).

## 5. Review
After refactoring each file or logical group of files, conceptually review the changes for correctness and improved structure.

## Example Transformation (Conceptual)

**Before:**
```scss
.ag-root-wrapper {
  border-width: 1px;
}
.ag-root-wrapper .ag-header {
  background: gray;
}
.ag-root-wrapper .ag-cell {
  padding: 5px;
}
```

**After:**
```scss
.ag-root-wrapper {
  border-width: 1px;

  .ag-header {
    background: gray;
  }

  .ag-cell {
    padding: 5px;
  }
}