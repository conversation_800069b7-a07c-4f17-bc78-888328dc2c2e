# Visual Regression Testing Approach Decision

## 1. Goal

To implement an automated visual regression testing strategy for the Holistics application to catch unintended UI changes during development.

## 2. Approaches Considered

Two main approaches were evaluated:

### Approach 1: BackstopJS (Standalone Node.js Tool)

*   **Description:** A popular, dedicated JavaScript tool for visual regression testing. It uses a headless browser (like Puppeteer) to capture screenshots based on a JSON configuration file and compares them against reference images.
*   **Pros:**
    *   Mature and feature-rich (viewport management, detailed HTML reports, scenario filtering).
    *   Uses modern browser engines (P<PERSON>peteer/Playwright) directly.
    *   JSON configuration might be accessible to non-Ruby developers.
*   **Cons (for this project):**
    *   **Separate Environment:** Runs independently of the Rails/RSpec test suite, requiring a separate running development server.
    *   **State Synchronization:** Difficult to share test data, database state, and user sessions between RSpec and BackstopJS. Requires complex workarounds (seeding, API logins within scripts).
    *   **CI Complexity:** Needs separate steps in the CI pipeline to manage the Node.js process, run commands, and handle artifacts.
    *   **Context Switching:** Requires developers to manage both Ruby/RSpec and Node.js/JSON tooling and workflows.

### Approach 2: Integrated RSpec (`capybara-screenshot` + Ruby Image Comparison)

*   **Description:** Leverages existing Ruby gems within the RSpec test suite. `capybara-screenshot` captures screenshots during test execution, and a Ruby image processing gem (like `chunky_png` or `oily_png`) is used to compare these against reference images via custom helper methods.
*   **Pros:**
    *   **Fully Integrated:** Runs within the `rspec` command and workflow.
    *   **Shared Environment:** Automatically uses the same database state (Database Cleaner), test data (FactoryBot), user sessions (Devise helpers), and other configurations as existing integration tests.
    *   **Simplified Automation:** Integrates seamlessly into CI pipelines already running `rspec`. Visual failures are reported as standard RSpec failures.
    *   **Consistent Tooling:** Uses familiar Ruby/RSpec/Capybara syntax and concepts.
    *   **Granular Control:** Easy to set up specific application states before visual assertions within RSpec tests.
*   **Cons:**
    *   **DIY Comparison/Reporting:** Requires custom helper code for image comparison logic. Reporting is less sophisticated than BackstopJS (RSpec output + diff images).
    *   **Potential Performance:** Ruby-based image comparison might be slower than optimized Node.js libraries for very large test suites.
    *   **Helper Maintenance:** The custom comparison helper code needs to be maintained.

## 3. Comparison Summary

| Feature             | BackstopJS                     | `capybara-screenshot` + Ruby |
| :------------------ | :----------------------------- | :--------------------------- |
| **Environment**     | Separate (Node.js)             | Integrated (RSpec/Ruby)      |
| **State Sync**      | Difficult / Manual Workarounds | Automatic / Shared           |
| **Automation (CI)** | Requires Separate Steps        | Integrates with `rspec`      |
| **Tooling**         | Node.js / JSON                 | Ruby / RSpec / Capybara      |
| **Reporting**       | Rich HTML UI                   | Basic (RSpec + Diff Images)  |
| **Setup Complexity**| Moderate (Config + Scripts)    | Moderate (Gems + Helper)     |

## 4. Decision

The **Integrated RSpec (`capybara-screenshot` + Ruby Image Comparison)** approach was chosen.

## 5. Rationale

While BackstopJS offers a more polished, dedicated visual testing experience, the primary requirement for this project was **seamless integration with the existing Rails/RSpec testing infrastructure**.

The major drawbacks of BackstopJS were the difficulty in synchronizing the test environment state (database, user sessions) and the added complexity to the CI/automation pipeline.

The chosen approach directly addresses these issues by:

1.  **Leveraging the existing test environment:** Eliminates the need for complex state synchronization workarounds.
2.  **Fitting into the current workflow:** Developers use familiar tools (RSpec, Capybara, Ruby), and visual tests run alongside other integration tests with the `rspec` command.
3.  **Simplifying CI:** No extra steps are needed beyond running the standard RSpec suite.

Although this requires implementing custom comparison logic and results in less sophisticated reporting, these trade-offs were deemed acceptable in exchange for the significant benefits of tight integration and simplified workflow/automation within the context of this specific Rails project.