# Refactoring Plan: Split visual_table_theme_spec.rb

**Objective:** Split the large spec file `spec/integration/aml/dashboard/visual_table_theme_spec.rb` into smaller, more focused files, one for each table type tested within it (Data Table, Pivot Table, Transpose Pivot Table, Metric Sheet).

**Plan:**

1.  **Identify Shared Code:** Extract the common setup code required by all test contexts. This includes:
    *   File header comments (`# typed: false`, `# frozen_string_literal: true`)
    *   `require 'rails_helper'`
    *   `include_context 'dashboards_v4'`
    *   `include ActiveSupport::Testing::TimeHelpers`
    *   The main `describe 'Visual Regression: table theme', :js, type: :feature do ... end` block structure.
    *   Helper methods: `setup_visual_test` and `recompile_aml`.
    *   `let` blocks for `admin` and `query_model_sql`.
    *   The top-level `before` block.

2.  **Identify Table-Specific Code:** Isolate the code specific to each table type:
    *   **Data Table:** Helper methods `data_table_aml`, `default_data_table_fields_aml`, and the `context 'Data table block'`.
    *   **Pivot Table:** Helper methods `pivot_table_aml`, `default_pivot_table_rows_aml`, `default_pivot_table_columns_aml`, `default_pivot_table_values_aml`, and the `context 'pivot table block'`.
    *   **Transpose Pivot Table:** Reuses the Pivot Table helper methods and the `context 'tranpose pivot table block'`.
    *   **Metric Sheet:** Helper methods `metric_sheet_aml`, `default_metric_sheet_date_field_aml`, `default_metric_sheet_metrics_aml`, `default_metric_sheet_dimensions_aml`, and the `context 'Metric sheet block'`.

3.  **Create New Files:** Generate four new spec files in the same directory (`spec/integration/aml/dashboard/`):
    *   `visual_data_table_theme_spec.rb`
    *   `visual_pivot_table_theme_spec.rb`
    *   `visual_transpose_pivot_table_theme_spec.rb`
    *   `visual_metric_sheet_theme_spec.rb`

4.  **Populate New Files:** Each new file will contain:
    *   The identified shared code (from step 1).
    *   The specific helper methods for that table type (from step 2).
    *   The specific `context` block for that table type (from step 2), placed within the main `describe` block.

5.  **Verification (Manual):** After creation, these files should be reviewed to ensure they contain the correct code and all necessary dependencies.

6.  **Original File Handling:** Decide whether to keep or delete the original `visual_table_theme_spec.rb` file.

**Diagram of Proposed File Structure:**

```mermaid
graph TD
    A[visual_table_theme_spec.rb] --> B(Split);
    B --> C[visual_data_table_theme_spec.rb];
    B --> D[visual_pivot_table_theme_spec.rb];
    B --> E[visual_transpose_pivot_table_theme_spec.rb];
    B --> F[visual_metric_sheet_theme_spec.rb];

    subgraph Shared Code
        S1[Requires/Includes]
        S2[Main Describe Block]
        S3[setup_visual_test Helper]
        S4[recompile_aml Helper]
        S5[let/before Blocks]
    end

    subgraph Data Table Specific
        DT1[data_table_aml Helpers]
        DT2[Data Table Context]
    end

    subgraph Pivot Table Specific
        PT1[pivot_table_aml Helpers]
        PT2[Pivot Table Context]
    end

    subgraph Transpose Pivot Specific
        TPT1(Uses Pivot Helpers)
        TPT2[Transpose Pivot Context]
    end

    subgraph Metric Sheet Specific
        MS1[metric_sheet_aml Helpers]
        MS2[Metric Sheet Context]
    end

    Shared Code --> C;
    Data Table Specific --> C;

    Shared Code --> D;
    Pivot Table Specific --> D;

    Shared Code --> E;
    TPT1 --> E;
    TPT2 --> E;


    Shared Code --> F;
    Metric Sheet Specific --> F;