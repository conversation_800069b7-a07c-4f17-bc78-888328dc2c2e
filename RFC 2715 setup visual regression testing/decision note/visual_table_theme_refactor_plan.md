# Refactoring Plan: `visual_table_theme_spec.rb` for Visual Regression

**Goal:** Modify `spec/integration/aml/dashboard/visual_table_theme_spec.rb` to use stable, predictable data for reliable visual regression testing.

**Problem:** The current spec uses `get_test_ds`, which likely introduces randomized data, causing visual inconsistencies.

**Solution:** Adopt the FactoryBot pattern seen in other `ag-grid-table` specs to define the visualization directly within the test, using a stable data source provided by an RSpec context.

**Steps:**

1.  **Include Contexts:**
    *   Keep `include_context 'aml_studio_dev_mode'`.
    *   Add `include_context 'format is defined in data modeling'` (defined in `spec/support/data_formats/data_format_context.rb`). This context provides a stable `DataSet` (named `data_set`) based on `orders_model` with 6 fixed rows via the `prepare_data` method.

2.  **Remove AML Dependency:**
    *   Remove the `include_context 'aml_studio_basic'` block.
    *   Remove the `let!(:ds)` block that uses `get_test_ds`.
    *   Remove `let(:dashboard_url)`.
    *   Remove the `load_dashboard` helper method.

3.  **Define Objects with FactoryBot:**
    *   In a `let` or `before` block, create:
        *   `let!(:report)`: Create a `Report` using FactoryBot, linking it to `data_set` from the context.
        *   `let!(:viz_setting)`: Define the visualization settings for the `report`.
            *   Set `viz_type: 'pivot_table'`.
            *   Configure fields based on `orders_model` (e.g., `quantity`, `discount`, `created_at`).
            *   **Crucially, apply the desired theme settings directly within this `viz_setting` definition.** (The specific theme settings need to be determined during implementation, possibly by inspecting the original AML or related code).
        *   `let!(:dashboard)`: Create a `Dashboard` using FactoryBot.
        *   `let!(:widget)`: Create a `Widget` using FactoryBot, linking `dashboard` and `report`.

4.  **Update Test Setup (`before` block):**
    *   Ensure necessary Feature Toggles are enabled (keep existing ones like `ag-grid:pivot-table`).
    *   Set `ThreadContext.set(:current_user, user)`.
    *   Navigate directly to the created dashboard: `qlogin(user, "/dashboards/v4/#{dashboard.id}")`.
    *   Wait for the pivot table visualization within the widget to load (e.g., `wait_for_element_load "#block-#{widget.id} .h-pivot"`).

5.  **Update Test Logic (`it` block):**
    *   Remove the steps related to clicking/expanding the block (`safe_click '[data-uname="v2"] ...'`, `wait_for_element_load '.ci-expanded-block'`).
    *   Update the `assert_visual_match` selector to target the pivot table within the specific widget, e.g., `selector: "#block-#{widget.id} .h-pivot"`.

6.  **Visual Baseline:** The visual baseline image for `assert_visual_match` will need to be regenerated after these changes are implemented.

**Diagram:**

```mermaid
graph TD
    A[Start: Need Stable Data] --> B{Analyze conditional_formatting_spec.rb};
    B --> C{Identify Random Data Source in visual_table_theme_spec.rb};
    C --> D[Search AG-Grid Specs];
    D --> E{Analyze Data Setup Patterns};
    E --> F[Search for Context Definition];
    F --> G[Read data_format_context.rb];
    G --> H{Context uses fixed data via prepare_data};
    H --> I[Attempt Read table_theme.page.aml];
    I -- Fails --> J{File Not Found - AML path likely incorrect/inaccessible};
    J --> K{Re-evaluate: Modify AML vs FactoryBot};
    K --> L[Decision: Use FactoryBot Approach];
    L --> M[Plan: Define Viz Objects in Spec];
    M --> N[Outline Code Changes for Spec (Contexts, FactoryBot, Navigation, Assertions)];
    N --> O{Present Revised Plan to User};
    O -- User Approves --> P[Save Plan to File];
    P --> Q[Suggest Switch to Code Mode];