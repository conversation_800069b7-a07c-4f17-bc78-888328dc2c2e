# Ruby Image Comparison Tool Decision for Visual Regression Testing

## 1. Context

For the integrated RSpec visual regression testing approach (using `capybara-screenshot`), a Ruby gem is required to programmatically compare the captured test screenshots against reference screenshots.

## 2. Requirements for the Tool

*   Read PNG image pixel data.
*   Compare two PNG images pixel by pixel.
*   Calculate the difference (percentage or pixel count).
*   Ideally, be able to generate a visual "diff" image highlighting differences.
*   Minimize external system dependencies for easier setup and CI integration.

## 3. Options Considered

*   **`chunky_png`:**
    *   **Description:** A pure Ruby library for reading, writing, and manipulating PNG images.
    *   **Pros:** No external dependencies (just Ruby), simple installation (`bundle install`), easy to use API.
    *   **Cons:** Performance might be slower than C-based alternatives for intensive pixel operations on many large images.
*   **`oily_png`:**
    *   **Description:** A C extension designed as a drop-in accelerator for `chunky_png`. It overrides key `chunky_png` methods with faster C implementations.
    *   **Pros:** Significantly faster performance than `chunky_png`, API-compatible (easy to switch).
    *   **Cons:** Requires a C compiler during gem installation, which can complicate environment setup or CI builds.
*   **`rmagick`:**
    *   **Description:** A Ruby wrapper for the powerful ImageMagick library.
    *   **Pros:** Extremely feature-rich, supports many formats and operations, can perform comparisons.
    *   **Cons:** Heavy external dependency (ImageMagick library + development headers must be installed on the system *before* the gem), significantly complicates setup/CI, generally overkill for simple PNG comparison.

## 4. Comparison Summary

| Feature             | `chunky_png`                     | `oily_png`                         | `rmagick`                          |
| :------------------ | :------------------------------- | :--------------------------------- | :--------------------------------- |
| **Implementation**  | Pure Ruby                        | C Extension (for `chunky_png`)     | Ruby Wrapper (for ImageMagick)   |
| **Dependencies**    | None (Ruby only)                 | C Compiler (at install time)       | ImageMagick Library (System)     |
| **Installation**    | Simple (`bundle install`)        | Can fail if C compiler missing     | Complex (System Lib + Gem)       |
| **Performance**     | Slower                           | Faster                             | Variable (depends on ImageMagick)  |
| **API**             | Simple, PNG-focused              | Same as `chunky_png`               | Complex, General Purpose           |
| **Setup Simplicity**| High                             | Medium                             | Low                                |

## 5. Decision

Start with **`chunky_png`**.

## 6. Rationale

*   **Simplicity First:** `chunky_png` provides the necessary functionality with the simplest possible setup, requiring no external system dependencies or build tools beyond Ruby itself. This aligns well with the goal of easy integration into the existing development and CI environments.
*   **Sufficient Functionality:** It can read, compare, and generate diff images as required by the proposed `VisualRegressionHelper`.
*   **Optimization Path:** If performance becomes a bottleneck later (unlikely unless dealing with a massive number of large screenshots), `oily_png` offers a clear and relatively simple upgrade path by just adding the gem (assuming a C compiler is available), without requiring changes to the comparison logic code.
*   **Avoid Complexity:** `rmagick` introduces significant external dependency management complexity that is unnecessary for the specific task of PNG comparison.

Therefore, `chunky_png` offers the best balance of functionality and ease of integration for the initial implementation.