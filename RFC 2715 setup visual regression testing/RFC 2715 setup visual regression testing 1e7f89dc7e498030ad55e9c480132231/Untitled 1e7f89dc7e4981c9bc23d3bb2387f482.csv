﻿Status,Date,Context,Decision,Pros,Cons,Related Asana/Slack/PP
Not started,"November 14, 2023",,No need to wait for children,"- Simplify tracing logic
- No negative performance impact",- Significant amount of incorrect duration data,
In progress,"November 14, 2023",Handle children node long loading time,Watch loading attribute,- Precise page duration tracing,"- Required hacky technical solution, which is also hard to maintain
- Performance of MutationObserver since HTML attribute check is needed (check benchmark in page body)",
Not started,"November 14, 2023",,Wati for all Ajax requests to be done,- Easier/less hacky to implement,"- Precision as one request can exist through multiple pages
- Performance as need to loop/recursively call the tracing method to check if all requests are done",
Not started,"November 14, 2023",,Check if node is still in body,- Straightforward,"- Need extra check in MutationObserver callback
- Performance consideration (no difference, check benchmark in page body)",
In progress,"November 14, 2023",Handle navigating to another page before MutationObserver done,"Discard all spans in store before each navigation and check if current route span is in store ","- Little to no effect on performance
- No need for extra check (reuse existing span check)",- Extra clearing spans step in before every navigation,
In progress,"November 14, 2023",Timeout duration after no DOM change,Record duration after no DOM change in 3s,"- The keep-alive logic is simple
- More reliable than 1s, 2s (tested)",- Not entirely reliable due to the nature of JS setTimeout,
In progress,,,Linking a child dom rendering span with current page span,,,